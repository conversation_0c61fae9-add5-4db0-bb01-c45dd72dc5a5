#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
抽象装箱算法基类
为所有装箱算法提供统一的接口和基本功能
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Tuple, Any, Optional
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
log = logging.getLogger("AbstractPacker")

class AbstractPacker(ABC):
    """
    抽象装箱算法基类
    为所有装箱算法提供统一的接口和基本功能
    """
    
    def __init__(self, container_width: int, image_spacing: int = 0, max_height: int = 0):
        """
        初始化装箱器
        
        Args:
            container_width: 容器宽度（像素）
            image_spacing: 图片间距（像素）
            max_height: 最大高度限制（0表示无限制）
        """
        self.container_width = container_width
        self.image_spacing = image_spacing
        self.max_height_limit = max_height
        self.placed_images = []  # 已放置的图片列表
        
    @abstractmethod
    def place_image(self, width: int, height: int, image_data: Dict[str, Any] = None) -> Tuple[int, int, bool]:
        """
        放置图片
        
        Args:
            width: 图片宽度
            height: 图片高度
            image_data: 图片相关数据
            
        Returns:
            Tuple[int, int, bool]: (x, y, 是否成功)
        """
        pass
    
    def get_placed_images(self) -> List[Dict[str, Any]]:
        """
        获取已放置的图片列表
        
        Returns:
            List[Dict[str, Any]]: 已放置的图片列表
        """
        return self.placed_images
    
    def get_max_height(self) -> int:
        """
        获取当前最大高度
        
        Returns:
            int: 当前最大高度
        """
        if not self.placed_images:
            return 0
        return max(img['y'] + img['height'] for img in self.placed_images)
    
    def get_utilization(self) -> float:
        """
        计算容器利用率
        
        Returns:
            float: 容器利用率 (0-1)
        """
        if not self.placed_images:
            return 0
            
        # 计算图片总面积
        total_image_area = sum(img['width'] * img['height'] for img in self.placed_images)
        
        # 计算容器面积
        container_height = self.get_max_height()
        if container_height == 0:
            return 0
        container_area = self.container_width * container_height
        
        # 计算利用率
        return total_image_area / container_area if container_area > 0 else 0
    
    def get_horizontal_utilization(self) -> Dict[int, float]:
        """
        计算每一行的水平利用率
        
        Returns:
            Dict[int, float]: 行高度到利用率的映射
        """
        if not self.placed_images:
            return {}
            
        # 按行高度分组
        rows = {}
        for img in self.placed_images:
            y = img['y']
            if y not in rows:
                rows[y] = []
            rows[y].append((img['x'], img['x'] + img['width']))
            
        # 计算每一行的利用率
        row_utils = {}
        for y, ranges in rows.items():
            # 合并重叠的范围
            ranges.sort()
            merged = []
            for start, end in ranges:
                if not merged or start > merged[-1][1]:
                    merged.append((start, end))
                else:
                    merged[-1] = (merged[-1][0], max(merged[-1][1], end))
                    
            # 计算行利用率
            covered_width = sum(end - start for start, end in merged)
            row_utils[y] = covered_width / self.container_width if self.container_width > 0 else 0
            
        return row_utils
    
    def clear(self) -> None:
        """
        清空已放置的图片
        """
        self.placed_images = []
