#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
画布截断处理器模块

提供画布截断处理功能：
1. 优化画布截断逻辑
2. 处理底部行图片
3. 提高画布利用率
"""

import logging
import copy
from typing import List, Dict, Any

# 导入优化器模块
from utils.bottom_row_optimizer import BottomRowOptimizer
from utils.canvas_truncation_optimizer import CanvasTruncationOptimizer
from utils.canvas_utilization_analyzer import CanvasUtilizationAnalyzer
from utils.bottom_gap_filler import BottomGapFiller

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
log = logging.getLogger("CanvasTruncationHandler")

class CanvasTruncationHandler:
    """
    画布截断处理器，提供画布截断处理功能

    特性：
    1. 优化画布截断逻辑
    2. 处理底部行图片
    3. 提高画布利用率
    """

    def __init__(self, tetris_packer=None):
        """
        初始化画布截断处理器

        Args:
            tetris_packer: Tetris算法实例，如果为None则需要在调用方法时提供
        """
        self.tetris_packer = tetris_packer
        self.bottom_row_optimizer = BottomRowOptimizer(tetris_packer)
        self.truncation_optimizer = CanvasTruncationOptimizer(tetris_packer)
        self.utilization_analyzer = CanvasUtilizationAnalyzer(tetris_packer)
        self.bottom_gap_filler = BottomGapFiller(tetris_packer)
        log.info("画布截断处理器初始化完成")

    def handle_canvas_truncation(self, tetris_packer=None, remaining_patterns: List[Dict[str, Any]] = None, is_last_batch: bool = False) -> Dict[str, Any]:
        """
        处理画布截断

        此方法是对外接口，调用统一的画布截断处理函数

        Args:
            tetris_packer: Tetris算法实例，如果为None则使用初始化时提供的实例
            remaining_patterns: 剩余未放置的图片列表
            is_last_batch: 是否是最后一批图片

        Returns:
            Dict[str, Any]: 处理结果
        """
        packer = tetris_packer or self.tetris_packer
        if not packer or not packer.placed_images:
            log.error("未提供有效的Tetris算法实例或画布为空")
            return {'success': False, 'message': "未提供有效的Tetris算法实例或画布为空"}

        try:
            # 记录原始状态
            original_placed_images = copy.deepcopy(packer.placed_images)
            original_max_height = packer.get_max_height()
            original_utilization = packer.get_utilization()

            log.info(f"开始处理画布截断，原始高度: {original_max_height}, 原始利用率: {original_utilization:.4f}, 是否最后一批: {is_last_batch}")

            # 如果没有提供剩余图片，无法进行优化
            if remaining_patterns is None:
                log.info("未提供剩余图片，无法进行优化")
                return {'success': False, 'message': "未提供剩余图片，无法进行优化"}

            # 尝试使用统一画布截断处理函数
            try:
                # 导入统一画布截断处理函数
                from core.unified_canvas_truncation import unified_canvas_truncation

                # 调用统一画布截断处理函数
                truncation_result = unified_canvas_truncation.truncate_canvas(
                    packer,
                    remaining_patterns,
                    is_last_batch
                )

                # 记录日志
                log.info(f"使用统一画布截断处理函数处理画布截断: {truncation_result.get('message', '')}")
            except Exception as e:
                log.warning(f"使用统一画布截断处理函数处理画布截断失败: {str(e)}，将使用传统方法")
                # 如果统一画布截断处理函数失败，使用传统方法
                truncation_result = self.truncate_canvas(packer, remaining_patterns, is_last_batch)

            # 如果截断失败，恢复原始状态
            if not truncation_result['success']:
                log.info(f"画布截断失败: {truncation_result['message']}")
                packer.placed_images = original_placed_images
                packer.current_max_height = max(img['y'] + img['height'] for img in packer.placed_images) if packer.placed_images else 0
                return {'success': False, 'message': truncation_result['message']}

            # 如果有移动的图片，需要将它们添加到剩余图片列表的开头
            if truncation_result['moved_patterns'] and remaining_patterns is not None:
                for pattern in truncation_result['moved_patterns']:
                    if pattern:
                        remaining_patterns.insert(0, pattern)
                log.info(f"已将 {len(truncation_result['moved_patterns'])} 个图片移至下一画布")

            # 计算新的利用率
            new_utilization = packer.get_utilization()

            # 返回处理结果
            return {
                'success': True,
                'action': truncation_result['action'],
                'original_utilization': original_utilization,
                'new_utilization': new_utilization,
                'utilization_improvement': (new_utilization - original_utilization) / original_utilization * 100 if original_utilization > 0 else 0,
                'message': truncation_result['message'],
                'height': truncation_result['height']
            }

        except Exception as e:
            log.error(f"处理画布截断失败: {str(e)}")
            # 恢复原始状态
            if 'original_placed_images' in locals():
                packer.placed_images = original_placed_images
                packer.current_max_height = max(img['y'] + img['height'] for img in packer.placed_images) if packer.placed_images else 0
            return {'success': False, 'message': f"处理画布截断失败: {str(e)}"}

    def truncate_canvas(self, tetris_packer=None, remaining_patterns: List[Dict[str, Any]] = None, is_last_batch: bool = False) -> Dict[str, Any]:
        """
        统一的画布截断处理函数

        此方法现在调用统一画布截断处理函数，确保所有画布截断逻辑一致

        Args:
            tetris_packer: Tetris算法实例，如果为None则使用初始化时提供的实例
            remaining_patterns: 剩余未放置的图片列表
            is_last_batch: 是否是最后一批图片

        Returns:
            Dict[str, Any]: 截断结果，包含以下字段：
                - height: 截断高度（像素）
                - success: 是否成功截断
                - action: 执行的操作（如"移动底部行"、"保留底部行"等）
                - message: 操作说明
                - moved_patterns: 移动到下一画布的图片列表（如果有）
        """
        packer = tetris_packer or self.tetris_packer
        if not packer or not packer.placed_images:
            return {
                'height': 0,
                'success': False,
                'action': "无操作",
                'message': "未提供有效的Tetris算法实例或画布为空",
                'moved_patterns': []
            }

        try:
            # 记录原始状态
            original_placed_images = copy.deepcopy(packer.placed_images)

            # 尝试使用统一画布截断处理函数
            try:
                # 导入统一画布截断处理函数
                from core.unified_canvas_truncation import unified_canvas_truncation

                # 调用统一画布截断处理函数
                truncation_result = unified_canvas_truncation.truncate_canvas(
                    packer,
                    remaining_patterns,
                    is_last_batch
                )

                # 记录日志
                log.info(f"使用统一画布截断处理函数处理画布截断: {truncation_result.get('message', '')}")

                return truncation_result
            except Exception as e:
                log.warning(f"使用统一画布截断处理函数处理画布截断失败: {str(e)}，将使用传统方法")

                # 如果统一画布截断处理函数失败，恢复原始状态
                packer.placed_images = original_placed_images

                # 返回当前最大高度
                return {
                    'height': packer.get_max_height(),
                    'success': False,
                    'action': "处理失败",
                    'message': f"画布截断处理失败: {str(e)}",
                    'moved_patterns': []
                }

        except Exception as e:
            log.error(f"画布截断处理失败: {str(e)}")
            # 恢复原始状态
            if 'original_placed_images' in locals():
                packer.placed_images = original_placed_images
                packer.current_max_height = max(img['y'] + img['height'] for img in packer.placed_images) if packer.placed_images else 0

            # 返回当前最大高度
            return {
                'height': packer.get_max_height(),
                'success': False,
                'action': "处理失败",
                'message': f"画布截断处理失败: {str(e)}",
                'moved_patterns': []
            }

    def _get_image_class(self, image: Dict[str, Any]) -> str:
        """
        获取图片的类别

        Args:
            image: 图片信息

        Returns:
            str: 图片类别，如'A', 'B', 'C'等，默认为'C'
        """
        # 尝试从不同位置获取图片类别
        if 'image_class' in image:
            return image['image_class']

        if 'category' in image:
            return image['category']

        if 'pattern' in image and isinstance(image['pattern'], dict):
            pattern = image['pattern']
            if 'image_class' in pattern:
                return pattern['image_class']
            if 'category' in pattern:
                return pattern['category']

        # 默认返回C类
        return 'C'

    def _can_form_complete_group(self, bottom_image: Dict[str, Any], remaining_patterns: List[Dict[str, Any]], container_width: int) -> bool:
        """
        检查底部图片是否能与待处理图片形成完整组

        Args:
            bottom_image: 底部图片信息
            remaining_patterns: 剩余未放置的图片列表
            container_width: 容器宽度

        Returns:
            bool: 是否能形成完整组
        """
        # 获取底部图片类别
        bottom_image_class = self._get_image_class(bottom_image)

        # 如果不是B类图片，直接返回False
        if bottom_image_class != 'B':
            return False

        # 获取底部图片宽度
        bottom_image_width = bottom_image.get('width', 0)
        if bottom_image_width <= 0:
            return False

        # 如果底部图片宽度超过容器宽度的80%，认为已经是完整的
        if bottom_image_width >= container_width * 0.8:
            return True

        # 计算一行可以放置的B类图片数量
        max_count = container_width // bottom_image_width

        # 如果只能放置一个，则已经是完整的
        if max_count <= 1:
            return True

        # 在剩余图片中查找B类图片
        b_class_patterns = []
        for pattern in remaining_patterns:
            if not isinstance(pattern, dict):
                continue

            # 获取图片类别
            pattern_class = 'C'
            if 'image_class' in pattern:
                pattern_class = pattern['image_class']
            elif 'category' in pattern:
                pattern_class = pattern['category']

            # 如果是B类图片，添加到列表
            if pattern_class == 'B':
                # 获取图片宽度
                pattern_width = 0
                if 'width_px' in pattern:
                    pattern_width = pattern['width_px']
                elif 'width' in pattern:
                    pattern_width = pattern['width']

                # 如果宽度为0，跳过此图片
                if pattern_width <= 0:
                    continue

                # 计算宽度匹配度
                width_match = 1.0 - min(abs(pattern_width - bottom_image_width) / max(pattern_width, bottom_image_width), 1.0)

                # 如果宽度匹配度高，添加到列表
                if width_match > 0.8:
                    b_class_patterns.append({
                        'pattern': pattern,
                        'width': pattern_width,
                        'match_score': width_match
                    })

        # 检查是否有足够的B类图片形成完整的一组
        if len(b_class_patterns) >= max_count - 1:
            # 计算底部图片和待处理B类图片的总宽度
            total_width = bottom_image_width

            # 添加最匹配的图片
            for i in range(min(max_count - 1, len(b_class_patterns))):
                pattern_info = b_class_patterns[i]
                total_width += pattern_info['width']

            # 如果总宽度接近容器宽度，认为可以形成完整的B类图片组
            if total_width >= container_width * 0.85:
                return True

        return False

    def _evaluate_bottom_row_movement(self, packer, bottom_row_info: Dict[str, Any], remaining_patterns: List[Dict[str, Any]]) -> bool:
        """
        评估是否应将底部行图片移至下一画布

        Args:
            packer: Tetris算法实例
            bottom_row_info: 底部行信息
            remaining_patterns: 剩余未放置的图片列表

        Returns:
            bool: 是否应移动底部行
        """
        # 如果底部行利用率较高，不需要移动
        bottom_row_utilization = bottom_row_info['width_used'] / packer.container_width
        if bottom_row_utilization > 0.7:
            log.info(f"底部行利用率较高 ({bottom_row_utilization:.4f})，不需要移动")
            return False

        # 计算底部行图片占用的总面积
        bottom_row_area = sum(img['width'] * img['height'] for img in bottom_row_info['images'])

        # 计算底部行图片占整个画布的面积比例
        total_area = sum(img['width'] * img['height'] for img in packer.placed_images)
        area_ratio = bottom_row_area / total_area if total_area > 0 else 0

        # 如果底部行图片占比很小，考虑移动
        if area_ratio < 0.05:  # 底部行图片占总面积不到5%
            log.info(f"底部行图片占总面积比例很小 ({area_ratio:.4f})，建议移动")
            return True

        # 检查底部行是否包含B类图片组
        b_class_images = [img for img in bottom_row_info['images'] if self._get_image_class(img) == 'B']
        if b_class_images and len(b_class_images) > 1:
            # 计算B类图片组的总宽度
            b_group_width = sum(img['width'] for img in b_class_images)

            # 如果B类图片组宽度接近画布宽度，认为是完整的B类图片组，不应移动
            if b_group_width >= packer.container_width * 0.85:
                log.info(f"底部行包含完整的B类图片组，不应移动")
                return False

        # 检查剩余图片数量 - 如果是最后几张图片，优先完成排列
        if len(remaining_patterns) <= 3:
            log.info(f"剩余图片数量很少 ({len(remaining_patterns)}张)，优先完成排列")
            return True

        # 默认情况下，如果底部行利用率低于50%，建议移动
        return bottom_row_utilization < 0.5

    def get_truncation_height(self, tetris_packer=None) -> int:
        """
        获取截断高度

        此方法调用统一的画布截断处理函数，并返回截断高度

        Args:
            tetris_packer: Tetris算法实例，如果为None则使用初始化时提供的实例

        Returns:
            int: 截断高度
        """
        packer = tetris_packer or self.tetris_packer
        if not packer or not packer.placed_images:
            return 0

        try:
            # 尝试使用统一画布截断处理函数
            try:
                # 导入统一画布截断处理函数
                from core.unified_canvas_truncation import unified_canvas_truncation

                # 调用统一画布截断处理函数
                result = unified_canvas_truncation.truncate_canvas(
                    packer,
                    remaining_patterns=[],
                    is_last_batch=packer.is_all_images_arranged if hasattr(packer, 'is_all_images_arranged') else False
                )

                # 记录日志
                log.info(f"使用统一画布截断处理函数获取截断高度: {result.get('height', 0)}像素")
            except Exception as e:
                log.warning(f"使用统一画布截断处理函数获取截断高度失败: {str(e)}，将使用传统方法")
                # 如果统一画布截断处理函数失败，使用传统方法
                result = self.truncate_canvas(packer)

            # 返回截断高度
            return result['height']

        except Exception as e:
            log.error(f"获取截断高度失败: {str(e)}")
            return packer.get_max_height()
