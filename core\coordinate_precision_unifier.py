#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
坐标精度统一处理器

确保测试模式和正式环境使用完全相同的坐标精度规则

作者: PS画布修复团队
日期: 2024-12-19
版本: 第三阶段优化
"""

import math
from typing import Dict, Any, Tuple, List, Optional
from dataclasses import dataclass, field
from enum import Enum


class CoordinateUnit(Enum):
    """坐标单位枚举"""
    PIXEL = "px"
    CENTIMETER = "cm"
    INCH = "in"
    POINT = "pt"


class PrecisionLevel(Enum):
    """精度级别枚举"""
    INTEGER = "integer"      # 整数精度
    HALF_PIXEL = "half"      # 半像素精度
    QUARTER_PIXEL = "quarter" # 四分之一像素精度
    FLOAT = "float"          # 浮点精度


@dataclass
class CoordinateConfig:
    """坐标配置"""
    precision_level: PrecisionLevel = PrecisionLevel.INTEGER
    rounding_method: str = "round"  # round, floor, ceil
    unit: CoordinateUnit = CoordinateUnit.PIXEL
    ppi: int = 72
    tolerance: float = 2.0  # 容差范围（像素）
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'precision_level': self.precision_level.value,
            'rounding_method': self.rounding_method,
            'unit': self.unit.value,
            'ppi': self.ppi,
            'tolerance': self.tolerance
        }


@dataclass
class CoordinatePoint:
    """坐标点"""
    x: float = 0.0
    y: float = 0.0
    unit: CoordinateUnit = CoordinateUnit.PIXEL
    
    def to_pixels(self, ppi: int = 72) -> 'CoordinatePoint':
        """转换为像素单位"""
        if self.unit == CoordinateUnit.PIXEL:
            return CoordinatePoint(self.x, self.y, CoordinateUnit.PIXEL)
        elif self.unit == CoordinateUnit.CENTIMETER:
            # 1cm = 0.393701 inches, 1inch = ppi pixels
            px_x = self.x * 0.393701 * ppi
            px_y = self.y * 0.393701 * ppi
            return CoordinatePoint(px_x, px_y, CoordinateUnit.PIXEL)
        elif self.unit == CoordinateUnit.INCH:
            px_x = self.x * ppi
            px_y = self.y * ppi
            return CoordinatePoint(px_x, px_y, CoordinateUnit.PIXEL)
        elif self.unit == CoordinateUnit.POINT:
            # 1 point = 1/72 inch
            px_x = self.x * ppi / 72
            px_y = self.y * ppi / 72
            return CoordinatePoint(px_x, px_y, CoordinateUnit.PIXEL)
        else:
            raise ValueError(f"不支持的单位: {self.unit}")


@dataclass
class CoordinateRect:
    """坐标矩形"""
    x: float = 0.0
    y: float = 0.0
    width: float = 0.0
    height: float = 0.0
    unit: CoordinateUnit = CoordinateUnit.PIXEL
    
    def to_pixels(self, ppi: int = 72) -> 'CoordinateRect':
        """转换为像素单位"""
        if self.unit == CoordinateUnit.PIXEL:
            return CoordinateRect(self.x, self.y, self.width, self.height, CoordinateUnit.PIXEL)
        
        # 转换位置
        pos = CoordinatePoint(self.x, self.y, self.unit).to_pixels(ppi)
        # 转换尺寸
        size = CoordinatePoint(self.width, self.height, self.unit).to_pixels(ppi)
        
        return CoordinateRect(pos.x, pos.y, size.x, size.y, CoordinateUnit.PIXEL)
    
    def get_bounds(self) -> Tuple[float, float, float, float]:
        """获取边界坐标"""
        return (self.x, self.y, self.x + self.width, self.y + self.height)


class CoordinatePrecisionUnifier:
    """坐标精度统一处理器"""
    
    def __init__(self, config: CoordinateConfig = None):
        self.config = config or CoordinateConfig()
        self.processing_history: List[Dict[str, Any]] = []
        
    def unify_coordinate(self, value: float) -> float:
        """
        统一坐标值精度
        
        Args:
            value: 原始坐标值
            
        Returns:
            float: 统一精度后的坐标值
        """
        if self.config.precision_level == PrecisionLevel.INTEGER:
            if self.config.rounding_method == "round":
                return float(round(value))
            elif self.config.rounding_method == "floor":
                return float(math.floor(value))
            elif self.config.rounding_method == "ceil":
                return float(math.ceil(value))
        elif self.config.precision_level == PrecisionLevel.HALF_PIXEL:
            return round(value * 2) / 2.0
        elif self.config.precision_level == PrecisionLevel.QUARTER_PIXEL:
            return round(value * 4) / 4.0
        elif self.config.precision_level == PrecisionLevel.FLOAT:
            return value
        
        return float(round(value))  # 默认整数精度
    
    def unify_coordinate_point(self, point: CoordinatePoint) -> CoordinatePoint:
        """
        统一坐标点精度
        
        Args:
            point: 原始坐标点
            
        Returns:
            CoordinatePoint: 统一精度后的坐标点
        """
        # 先转换为像素
        pixel_point = point.to_pixels(self.config.ppi)
        
        # 统一精度
        unified_x = self.unify_coordinate(pixel_point.x)
        unified_y = self.unify_coordinate(pixel_point.y)
        
        return CoordinatePoint(unified_x, unified_y, CoordinateUnit.PIXEL)
    
    def unify_coordinate_rect(self, rect: CoordinateRect) -> CoordinateRect:
        """
        统一坐标矩形精度
        
        Args:
            rect: 原始坐标矩形
            
        Returns:
            CoordinateRect: 统一精度后的坐标矩形
        """
        # 先转换为像素
        pixel_rect = rect.to_pixels(self.config.ppi)
        
        # 统一精度
        unified_x = self.unify_coordinate(pixel_rect.x)
        unified_y = self.unify_coordinate(pixel_rect.y)
        unified_width = self.unify_coordinate(pixel_rect.width)
        unified_height = self.unify_coordinate(pixel_rect.height)
        
        # 确保尺寸为正数
        if unified_width <= 0:
            unified_width = 1.0
        if unified_height <= 0:
            unified_height = 1.0
        
        return CoordinateRect(unified_x, unified_y, unified_width, unified_height, CoordinateUnit.PIXEL)
    
    def validate_coordinate_rect(self, rect: CoordinateRect, canvas_width: int = None, 
                               canvas_height: int = None) -> Tuple[bool, List[str]]:
        """
        验证坐标矩形有效性
        
        Args:
            rect: 坐标矩形
            canvas_width: 画布宽度
            canvas_height: 画布高度
            
        Returns:
            Tuple[bool, List[str]]: (是否有效, 错误信息列表)
        """
        errors = []
        
        # 基本有效性检查
        if rect.x < 0:
            errors.append(f"X坐标不能为负数: {rect.x}")
        if rect.y < 0:
            errors.append(f"Y坐标不能为负数: {rect.y}")
        if rect.width <= 0:
            errors.append(f"宽度必须为正数: {rect.width}")
        if rect.height <= 0:
            errors.append(f"高度必须为正数: {rect.height}")
        
        # 画布边界检查
        if canvas_width is not None:
            if rect.x + rect.width > canvas_width:
                errors.append(f"图片超出画布右边界: {rect.x + rect.width} > {canvas_width}")
        
        if canvas_height is not None:
            if rect.y + rect.height > canvas_height:
                errors.append(f"图片超出画布下边界: {rect.y + rect.height} > {canvas_height}")
        
        return len(errors) == 0, errors
    
    def process_image_coordinates(self, image_data: Dict[str, Any], 
                                canvas_width: int = None, canvas_height: int = None) -> Dict[str, Any]:
        """
        处理图片坐标数据
        
        Args:
            image_data: 图片数据字典
            canvas_width: 画布宽度
            canvas_height: 画布高度
            
        Returns:
            Dict: 处理后的图片数据
        """
        # 提取原始坐标
        original_rect = CoordinateRect(
            x=image_data.get('x', 0),
            y=image_data.get('y', 0),
            width=image_data.get('width', 0),
            height=image_data.get('height', 0),
            unit=CoordinateUnit.PIXEL  # 假设输入已经是像素
        )
        
        # 统一精度
        unified_rect = self.unify_coordinate_rect(original_rect)
        
        # 验证有效性
        is_valid, errors = self.validate_coordinate_rect(unified_rect, canvas_width, canvas_height)
        
        # 记录处理历史
        processing_record = {
            'original': {
                'x': original_rect.x,
                'y': original_rect.y,
                'width': original_rect.width,
                'height': original_rect.height
            },
            'unified': {
                'x': unified_rect.x,
                'y': unified_rect.y,
                'width': unified_rect.width,
                'height': unified_rect.height
            },
            'is_valid': is_valid,
            'errors': errors,
            'config': self.config.to_dict()
        }
        self.processing_history.append(processing_record)
        
        # 构建结果
        result = image_data.copy()
        result.update({
            'x': int(unified_rect.x),
            'y': int(unified_rect.y),
            'width': int(unified_rect.width),
            'height': int(unified_rect.height),
            'coordinate_unified': True,
            'coordinate_valid': is_valid,
            'coordinate_errors': errors,
            'precision_level': self.config.precision_level.value
        })
        
        return result
    
    def batch_process_coordinates(self, images_data: List[Dict[str, Any]], 
                                canvas_width: int = None, canvas_height: int = None) -> List[Dict[str, Any]]:
        """
        批量处理图片坐标
        
        Args:
            images_data: 图片数据列表
            canvas_width: 画布宽度
            canvas_height: 画布高度
            
        Returns:
            List[Dict]: 处理后的图片数据列表
        """
        results = []
        
        for image_data in images_data:
            processed = self.process_image_coordinates(image_data, canvas_width, canvas_height)
            results.append(processed)
        
        return results
    
    def get_processing_statistics(self) -> Dict[str, Any]:
        """获取处理统计信息"""
        if not self.processing_history:
            return {'processed_count': 0}
        
        total_count = len(self.processing_history)
        valid_count = sum(1 for record in self.processing_history if record['is_valid'])
        error_count = total_count - valid_count
        
        # 计算精度变化统计
        x_changes = []
        y_changes = []
        width_changes = []
        height_changes = []
        
        for record in self.processing_history:
            orig = record['original']
            unified = record['unified']
            
            x_changes.append(abs(unified['x'] - orig['x']))
            y_changes.append(abs(unified['y'] - orig['y']))
            width_changes.append(abs(unified['width'] - orig['width']))
            height_changes.append(abs(unified['height'] - orig['height']))
        
        return {
            'processed_count': total_count,
            'valid_count': valid_count,
            'error_count': error_count,
            'success_rate': valid_count / total_count * 100 if total_count > 0 else 0,
            'precision_changes': {
                'x_max_change': max(x_changes) if x_changes else 0,
                'y_max_change': max(y_changes) if y_changes else 0,
                'width_max_change': max(width_changes) if width_changes else 0,
                'height_max_change': max(height_changes) if height_changes else 0,
                'x_avg_change': sum(x_changes) / len(x_changes) if x_changes else 0,
                'y_avg_change': sum(y_changes) / len(y_changes) if y_changes else 0
            },
            'config': self.config.to_dict()
        }
    
    def clear_history(self):
        """清空处理历史"""
        self.processing_history.clear()


class TestModeCoordinateProcessor:
    """测试模式坐标处理器"""
    
    def __init__(self, unifier: CoordinatePrecisionUnifier):
        self.unifier = unifier
    
    def process_for_test_mode(self, images_data: List[Dict[str, Any]], 
                            canvas_width: int, canvas_height: int) -> List[Dict[str, Any]]:
        """
        为测试模式处理坐标
        
        Args:
            images_data: 图片数据列表
            canvas_width: 画布宽度
            canvas_height: 画布高度
            
        Returns:
            List[Dict]: 处理后的图片数据列表
        """
        return self.unifier.batch_process_coordinates(images_data, canvas_width, canvas_height)


class ProductionModeCoordinateProcessor:
    """正式环境坐标处理器"""
    
    def __init__(self, unifier: CoordinatePrecisionUnifier):
        self.unifier = unifier
    
    def process_for_production_mode(self, images_data: List[Dict[str, Any]], 
                                  canvas_width: int, canvas_height: int) -> List[Dict[str, Any]]:
        """
        为正式环境处理坐标
        
        Args:
            images_data: 图片数据列表
            canvas_width: 画布宽度
            canvas_height: 画布高度
            
        Returns:
            List[Dict]: 处理后的图片数据列表
        """
        # 使用完全相同的处理逻辑
        return self.unifier.batch_process_coordinates(images_data, canvas_width, canvas_height)


# 导出主要类
__all__ = [
    'CoordinateUnit',
    'PrecisionLevel', 
    'CoordinateConfig',
    'CoordinatePoint',
    'CoordinateRect',
    'CoordinatePrecisionUnifier',
    'TestModeCoordinateProcessor',
    'ProductionModeCoordinateProcessor'
]
