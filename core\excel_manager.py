#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Excel管理器模块

提供图片分类结果的Excel表格存储和读取功能：
1. 将A/B/C类图片分别存储在不同的sheet中
2. 从不同的sheet中读取A/B/C类图片
3. 支持图片唯一标识符"图片名_宽_高"
"""

import os
import logging
import pandas as pd
from typing import List, Dict, Any, Tuple, Optional
from PyQt6.QtCore import QObject, pyqtSignal

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
log = logging.getLogger("ExcelManager")

class ExcelManager(QObject):
    """Excel管理器类

    功能特性：
    1. 将A/B/C类图片分别存储在不同的sheet中
    2. 从不同的sheet中读取A/B/C类图片
    3. 支持图片唯一标识符"图片名_宽_高"
    """

    # 信号定义
    progress_signal = pyqtSignal(int)  # 进度值 (0-100)
    log_signal = pyqtSignal(str)       # 日志信息
    error_signal = pyqtSignal(str)     # 错误信息

    def __init__(self, log_signal=None):
        """初始化Excel管理器

        Args:
            log_signal: 日志信号，用于向UI发送日志信息
        """
        super().__init__()
        self.log_signal = log_signal

    def emit_log(self, message: str):
        """发送日志信息

        Args:
            message: 日志信息
        """
        if self.log_signal:
            self.log_signal.emit(message)
        log.info(message)

    def save_classification_to_excel(self,
                                    class_a_patterns: List[Dict[str, Any]],
                                    class_b_patterns: List[Dict[str, Any]],
                                    class_c_patterns: List[Dict[str, Any]],
                                    output_path: str) -> Tuple[bool, str]:
        """将A/B/C类图片分别存储在不同的sheet中

        Args:
            class_a_patterns: A类图片列表
            class_b_patterns: B类图片列表
            class_c_patterns: C类图片列表
            output_path: 输出文件路径

        Returns:
            (成功标志, 消息)
        """
        try:
            # 创建输出目录
            os.makedirs(os.path.dirname(output_path), exist_ok=True)

            # 将图片列表转换为DataFrame
            df_a = pd.DataFrame(class_a_patterns)
            df_b = pd.DataFrame(class_b_patterns)
            df_c = pd.DataFrame(class_c_patterns)

            # 确保每个DataFrame中的pattern_name字段为字符串类型
            for df in [df_a, df_b, df_c]:
                if 'pattern_name' in df.columns:
                    # 将pattern_name列转换为字符串类型
                    df['pattern_name'] = df['pattern_name'].astype(str)
                    self.emit_log(f"已将pattern_name列转换为文本格式")

                if 'unique_id' not in df.columns:
                    # 创建唯一标识符，格式为"图片名_宽_高"
                    df['unique_id'] = df.apply(
                        lambda row: f"{row.get('pattern_name', '')}_{row.get('width_cm', 0):.1f}_{row.get('height_cm', 0):.1f}",
                        axis=1
                    )

            # 从输出路径中提取宽幅值
            # 例如，从 "材料名_分类结果_200.xlsx" 中提取宽幅值
            # 默认使用200作为宽幅值
            width_value = 200
            try:
                # 尝试从文件名中提取宽幅值（作为后缀）
                filename = os.path.basename(output_path)
                # 移除扩展名
                filename_without_ext = os.path.splitext(filename)[0]
                # 获取最后一个下划线后的部分
                parts = filename_without_ext.split('_')
                if len(parts) >= 3 and parts[-1].isdigit():
                    width_value = int(parts[-1])
                    self.emit_log(f"从文件名后缀提取到宽幅值: {width_value}")
                else:
                    self.emit_log(f"无法从文件名后缀提取宽幅值，使用默认值: {width_value}")
            except Exception as e:
                self.emit_log(f"提取宽幅值失败: {str(e)}，使用默认值: {width_value}")

            # 使用正确的sheet表名称格式：宽幅_A，宽幅_B，宽幅_C
            sheet_a_name = f"{width_value}_A"
            sheet_b_name = f"{width_value}_B"
            sheet_c_name = f"{width_value}_C"

            self.emit_log(f"使用sheet表名称: {sheet_a_name}, {sheet_b_name}, {sheet_c_name}")

            # 写入Excel文件
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                df_a.to_excel(writer, sheet_name=sheet_a_name, index=False)
                df_b.to_excel(writer, sheet_name=sheet_b_name, index=False)
                df_c.to_excel(writer, sheet_name=sheet_c_name, index=False)

                # 设置pattern_name列为文本格式
                workbook = writer.book
                for sheet_name in [sheet_a_name, sheet_b_name, sheet_c_name]:
                    worksheet = workbook[sheet_name]
                    # 找到pattern_name列的索引
                    header_row = next(worksheet.rows)
                    pattern_name_idx = None
                    for idx, cell in enumerate(header_row):
                        if cell.value == 'pattern_name':
                            pattern_name_idx = idx + 1  # openpyxl的列索引从1开始
                            break

                    if pattern_name_idx:
                        self.emit_log(f"在{sheet_name}中设置pattern_name列(第{pattern_name_idx}列)为文本格式")
                        # 设置整列为文本格式
                        for row in range(2, worksheet.max_row + 1):  # 从第2行开始（跳过表头）
                            cell = worksheet.cell(row=row, column=pattern_name_idx)
                            cell.number_format = '@'  # '@'是文本格式的代码

            self.emit_log(f"已将分类结果保存到Excel文件: {output_path}")
            self.emit_log(f"A类图片: {len(class_a_patterns)}个, B类图片: {len(class_b_patterns)}个, C类图片: {len(class_c_patterns)}个")
            return True, f"成功保存分类结果到 {output_path}"

        except Exception as e:
            error_msg = f"保存Excel文件失败: {str(e)}"
            log.error(error_msg)
            self.emit_log(error_msg)
            return False, error_msg

    def load_classification_from_excel(self, excel_path: str) -> Tuple[bool, str, List[Dict[str, Any]], List[Dict[str, Any]], List[Dict[str, Any]]]:
        """从Excel文件中读取A/B/C类图片

        Args:
            excel_path: Excel文件路径

        Returns:
            (成功标志, 消息, A类图片列表, B类图片列表, C类图片列表)
        """
        try:
            # 检查文件是否存在
            if not os.path.exists(excel_path):
                return False, f"Excel文件不存在: {excel_path}", [], [], []

            # 读取Excel文件
            xls = pd.ExcelFile(excel_path)

            # 从文件名中提取宽幅值
            # 例如，从 "材料名_分类结果_200.xlsx" 中提取宽幅值
            # 默认使用200作为宽幅值
            width_value = 200
            try:
                # 尝试从文件名中提取宽幅值（作为后缀）
                filename = os.path.basename(excel_path)
                # 移除扩展名
                filename_without_ext = os.path.splitext(filename)[0]
                # 获取最后一个下划线后的部分
                parts = filename_without_ext.split('_')
                if len(parts) >= 3 and parts[-1].isdigit():
                    width_value = int(parts[-1])
                    self.emit_log(f"从文件名后缀提取到宽幅值: {width_value}")
                else:
                    self.emit_log(f"无法从文件名后缀提取宽幅值，使用默认值: {width_value}")
            except Exception as e:
                self.emit_log(f"提取宽幅值失败: {str(e)}，使用默认值: {width_value}")

            # 使用正确的sheet表名称格式：宽幅_A，宽幅_B，宽幅_C
            sheet_a_name = f"{width_value}_A"
            sheet_b_name = f"{width_value}_B"
            sheet_c_name = f"{width_value}_C"

            self.emit_log(f"查找sheet表: {sheet_a_name}, {sheet_b_name}, {sheet_c_name}")

            # 检查是否包含所需的sheet
            required_sheets = [sheet_a_name, sheet_b_name, sheet_c_name]

            # 检查是否存在旧格式的sheet名称
            old_format_sheets = ['宽幅_A', '宽幅_B', '宽幅_C']
            if all(sheet in xls.sheet_names for sheet in old_format_sheets) and not all(sheet in xls.sheet_names for sheet in required_sheets):
                self.emit_log(f"检测到旧格式的sheet名称，将使用旧格式")
                sheet_a_name = '宽幅_A'
                sheet_b_name = '宽幅_B'
                sheet_c_name = '宽幅_C'
                required_sheets = old_format_sheets

            missing_sheets = [sheet for sheet in required_sheets if sheet not in xls.sheet_names]
            if missing_sheets:
                return False, f"Excel文件缺少必需的sheet: {', '.join(missing_sheets)}", [], [], []

            # 设置读取Excel时的数据类型转换器，确保pattern_name列被读取为字符串
            converters = {'pattern_name': str}

            # 读取各个sheet，并指定pattern_name列为字符串类型
            df_a = pd.read_excel(excel_path, sheet_name=sheet_a_name, converters=converters)
            df_b = pd.read_excel(excel_path, sheet_name=sheet_b_name, converters=converters)
            df_c = pd.read_excel(excel_path, sheet_name=sheet_c_name, converters=converters)

            # 确保每个DataFrame中的pattern_name字段为字符串类型
            for df, sheet_name in zip([df_a, df_b, df_c], [sheet_a_name, sheet_b_name, sheet_c_name]):
                if 'pattern_name' in df.columns:
                    # 将pattern_name列转换为字符串类型
                    df['pattern_name'] = df['pattern_name'].astype(str)
                    self.emit_log(f"已将{sheet_name}中的pattern_name列转换为文本格式")

            # 将DataFrame转换为字典列表
            class_a_patterns = df_a.to_dict('records')
            class_b_patterns = df_b.to_dict('records')
            class_c_patterns = df_c.to_dict('records')

            # 记录读取结果
            self.emit_log(f"已从Excel文件读取分类结果: {excel_path}")
            self.emit_log(f"A类图片: {len(class_a_patterns)}个, B类图片: {len(class_b_patterns)}个, C类图片: {len(class_c_patterns)}个")

            return True, "成功读取分类结果", class_a_patterns, class_b_patterns, class_c_patterns

        except Exception as e:
            error_msg = f"读取Excel文件失败: {str(e)}"
            log.error(error_msg)
            self.emit_log(error_msg)
            return False, error_msg, [], [], []
