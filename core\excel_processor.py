#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Excel处理模块

提供Excel文件的读取、处理和保存功能：
1. 支持多个Excel文件批量处理
2. 支持图案信息检索和匹配
3. 支持数据验证和错误处理
4. 支持进度显示和状态更新
"""

import os
import logging
import pandas as pd
from typing import Tuple, List, Dict, Optional
from PyQt6.QtCore import QObject, pyqtSignal

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
log = logging.getLogger("ExcelProcessor")

class ExcelProcessor(QObject):
    """Excel处理类

    功能特性：
    1. 支持多个Excel文件批量处理
    2. 支持图案信息检索和匹配
    3. 支持数据验证和错误处理
    4. 支持进度显示和状态更新
    """

    # 信号定义
    progress_signal = pyqtSignal(int)  # 进度值 (0-100)
    status_signal = pyqtSignal(str)    # 状态信息
    error_signal = pyqtSignal(str)     # 错误信息

    def __init__(self):
        """初始化Excel处理器"""
        super().__init__()
        self.required_columns = ['数量']  # 只有数量是必需的，图案全称和图案至少需要一个
        self._processed_sheets = {}  # 用于存储已处理的工作表数据

    def process_material_folder(self, folder_path: str, image_indexer, exact_pattern_search: bool = False, is_standard_mode: bool = True, is_fuzzy_query: bool = False) -> Tuple[bool, str, List[Dict]]:
        """处理材质表格文件夹

        Args:
            folder_path: 材质表格文件夹路径
            image_indexer: 图片索引器实例
            exact_pattern_search: 是否仅精确查询图案全称
            is_standard_mode: 是否使用标准表格模式，True为标准模式，False为自定义模式
            is_fuzzy_query: 是否开启模糊查询，True为开启，False为关闭

        Returns:
            (成功标志, 消息, 材质任务列表)
        """
        try:
            if not os.path.exists(folder_path):
                return False, "材质表格文件夹不存在", []

            # 获取所有Excel文件
            excel_files = []
            for file in os.listdir(folder_path):
                if file.endswith(('.xlsx', '.xls')):
                    excel_files.append(os.path.join(folder_path, file))
                    log.info(f"找到Excel文件: {file}")
                    self.status_signal.emit(f"找到Excel文件: {file}")

            if not excel_files:
                return False, "未找到Excel文件", []

            log.info(f"共找到 {len(excel_files)} 个Excel文件")
            self.status_signal.emit(f"共找到 {len(excel_files)} 个Excel文件")

            # 处理每个Excel文件
            material_tasks = []
            total_files = len(excel_files)

            for i, excel_file in enumerate(excel_files):
                try:
                    # 读取Excel文件的所有工作表
                    xls = pd.ExcelFile(excel_file)
                    sheet_names = xls.sheet_names

                    # 记录工作表信息
                    log.info(f"Excel文件 {os.path.basename(excel_file)} 包含 {len(sheet_names)} 个工作表: {', '.join(sheet_names)}")
                    self.status_signal.emit(f"Excel文件 {os.path.basename(excel_file)} 包含 {len(sheet_names)} 个工作表")

                    # 获取文件名（不含扩展名）作为材质名称
                    material_name = os.path.splitext(os.path.basename(excel_file))[0]

                    # 清空当前文件的已处理工作表数据
                    self._processed_sheets[excel_file] = {}

                    # 处理每个工作表
                    for sheet_name in sheet_names:
                        # 跳过名为"数据异常"的工作表
                        if sheet_name == "数据异常":
                            log.info(f"跳过数据异常工作表: {sheet_name}")
                            self.status_signal.emit(f"跳过数据异常工作表: {sheet_name}")
                            continue

                        # 创建任务
                        task = {
                            'material_name': material_name,
                            'excel_file': excel_file,
                            'sheet_name': sheet_name,
                            'status': 'pending',
                            'message': ''
                        }
                        material_tasks.append(task)

                        # 立即处理工作表
                        success, message, df = self.read_sheet_data(
                            excel_file,
                            sheet_name,
                            image_indexer,
                            exact_pattern_search,
                            is_standard_mode,
                            is_fuzzy_query
                        )

                        # 更新任务状态
                        task['status'] = 'success' if success else 'failed'
                        task['message'] = message

                        # 存储处理后的数据
                        if success and df is not None:
                            self._processed_sheets[excel_file][sheet_name] = df

                    # 保存处理后的Excel文件（包含所有工作表）
                    if self._processed_sheets[excel_file]:
                        output_file = os.path.join(
                            os.path.dirname(excel_file),
                            f"{os.path.splitext(os.path.basename(excel_file))[0]}_已检索.xlsx"
                        )
                        self._save_all_sheets(excel_file, output_file)

                    # 更新进度
                    progress = int((i + 1) / total_files * 100)
                    self.progress_signal.emit(progress)
                    self.status_signal.emit(f"已处理 {i + 1}/{total_files} 个文件")

                except Exception as e:
                    log.error(f"处理Excel文件失败 {excel_file}: {str(e)}")
                    self.error_signal.emit(f"处理Excel文件失败 {excel_file}: {str(e)}")

            if material_tasks:
                # 统计处理结果
                total_tasks = len(material_tasks)
                success_tasks = sum(1 for task in material_tasks if task['status'] == 'success')
                failed_tasks = sum(1 for task in material_tasks if task['status'] == 'failed')

                result_message = (
                    f"处理完成:\n"
                    f"- 总任务数: {total_tasks}\n"
                    f"- 成功任务: {success_tasks}\n"
                    f"- 失败任务: {failed_tasks}"
                )

                return True, result_message, material_tasks
            else:
                return False, "未找到有效的材质任务", []

        except Exception as e:
            log.error(f"处理材质文件夹失败: {str(e)}")
            return False, f"处理材质文件夹失败: {str(e)}", []

    def process_excel_file(self, excel_file_path: str, image_indexer=None, exact_pattern_search: bool = False, is_standard_mode: bool = True, is_fuzzy_query: bool = False) -> List[Dict]:
        """处理单个Excel文件，返回图案项列表

        Args:
            excel_file_path: Excel文件路径
            image_indexer: 图片索引器实例（可选）
            exact_pattern_search: 是否仅精确查询图案全称
            is_standard_mode: 是否使用标准表格模式
            is_fuzzy_query: 是否开启模糊查询

        Returns:
            图案项列表，每个项包含pattern_name, width_cm, height_cm, quantity等字段
        """
        try:
            if not os.path.exists(excel_file_path):
                log.error(f"Excel文件不存在: {excel_file_path}")
                return []

            # 读取Excel文件的所有工作表
            xls = pd.ExcelFile(excel_file_path)
            sheet_names = xls.sheet_names

            all_patterns = []

            for sheet_name in sheet_names:
                # 跳过数据异常工作表
                if sheet_name == "数据异常":
                    continue

                try:
                    # 读取工作表数据
                    success, message, df = self.read_sheet_data(
                        excel_file_path,
                        sheet_name,
                        image_indexer,
                        exact_pattern_search,
                        is_standard_mode,
                        is_fuzzy_query
                    )

                    if success and df is not None:
                        # 转换DataFrame为图案项列表，传递image_indexer参数
                        patterns = self._convert_dataframe_to_patterns(df, sheet_name, image_indexer)
                        all_patterns.extend(patterns)
                        log.info(f"从工作表 {sheet_name} 提取了 {len(patterns)} 个图案")
                    else:
                        log.warning(f"处理工作表 {sheet_name} 失败: {message}")

                except Exception as e:
                    log.error(f"处理工作表 {sheet_name} 时发生错误: {str(e)}")
                    continue

            log.info(f"从Excel文件 {os.path.basename(excel_file_path)} 总共提取了 {len(all_patterns)} 个图案")
            return all_patterns

        except Exception as e:
            log.error(f"处理Excel文件失败 {excel_file_path}: {str(e)}")
            return []

    def _convert_dataframe_to_patterns(self, df: pd.DataFrame, sheet_name: str, image_indexer=None) -> List[Dict]:
        """将DataFrame转换为图案项列表

        Args:
            df: 处理后的DataFrame
            sheet_name: 工作表名称
            image_indexer: 图片索引器，用于判断是否跳过“未入库”的图片

        Returns:
            图案项列表
        """
        patterns = []

        try:
            for index, row in df.iterrows():
                # 只有在没有image_indexer时才跳过“未入库”的图片
                # 如果有image_indexer，则允许在后续步骤中检索图片
                if image_indexer is None and row.get('图片路径', '') == '未入库':
                    continue

                # 获取图案名称 - 优先使用图案全称
                pattern_name = None
                if '图案全称' in df.columns and pd.notna(row['图案全称']):
                    pattern_name = str(row['图案全称']).strip()
                elif '图案' in df.columns and pd.notna(row['图案']):
                    pattern_name = str(row['图案']).strip()

                if not pattern_name:
                    continue

                # 获取尺寸信息
                try:
                    width_cm = float(row['width_cm']) if 'width_cm' in row else float(row.get('宽cm', 0))
                    height_cm = float(row['height_cm']) if 'height_cm' in row else float(row.get('高cm', 0))
                except (ValueError, TypeError):
                    log.warning(f"跳过无效尺寸的行: {pattern_name}")
                    continue

                # 获取数量信息
                try:
                    quantity = int(float(row.get('数量', 1)))
                except (ValueError, TypeError):
                    quantity = 1

                # 确保数量至少为1
                quantity = max(1, quantity)

                # 创建图案项
                pattern = {
                    'pattern_name': pattern_name,
                    'width_cm': width_cm,
                    'height_cm': height_cm,
                    'quantity': quantity,
                    'sheet_name': sheet_name,
                    'path': row.get('图片路径', ''),
                    'search_method': row.get('查询方式', ''),
                    'row_number': index + 2  # Excel行号从1开始，第1行是表头
                }

                # 根据数量创建多个副本
                for i in range(quantity):
                    pattern_copy = pattern.copy()
                    pattern_copy['copy_index'] = i + 1
                    pattern_copy['unique_id'] = f"{pattern_name}_{width_cm:.1f}_{height_cm:.1f}_{index}_{i}"
                    patterns.append(pattern_copy)

        except Exception as e:
            log.error(f"转换DataFrame为图案项时发生错误: {str(e)}")

        return patterns

    def _standardize_column_names(self, df: pd.DataFrame) -> None:
        """智能检测和标准化列名

        Args:
            df: 要处理的DataFrame
        """
        # 创建列名映射字典
        column_mapping = {}

        # 常见的尺寸列名变体
        size_variants = ['尺寸', '规格', '大小', '尺寸(cm)', '规格(cm)', '尺寸（cm）', '规格（cm）']
        width_variants = ['宽', '宽度', '宽cm', '宽(cm)', '宽（cm）', 'width', 'Width', 'WIDTH']
        height_variants = ['高', '高度', '高cm', '高(cm)', '高（cm）', 'height', 'Height', 'HEIGHT']
        quantity_variants = ['数量', '个数', '件数', 'quantity', 'Quantity', 'QUANTITY', 'qty', 'Qty', 'QTY']
        pattern_variants = ['图案', '图案名', '图案名称', 'pattern', 'Pattern', 'PATTERN']
        pattern_full_variants = ['图案全称', '完整图案名', '图案全名', '图案完整名称']

        # 检测并映射列名
        for col in df.columns:
            col_str = str(col).strip()
            col_lower = col_str.lower()

            # 检测尺寸列 - 精确匹配优先
            if col_str in size_variants or any(variant.lower() == col_lower for variant in size_variants):
                if '尺寸' not in df.columns:
                    column_mapping[col] = '尺寸'
            # 检测尺寸列 - 包含匹配
            elif any(variant.lower() in col_lower for variant in size_variants):
                if '尺寸' not in df.columns and '尺寸' not in column_mapping.values():
                    column_mapping[col] = '尺寸'

            # 检测宽度列 - 精确匹配优先
            elif col_str in width_variants or any(variant.lower() == col_lower for variant in width_variants):
                if '宽cm' not in df.columns:
                    column_mapping[col] = '宽cm'
            # 检测宽度列 - 包含匹配
            elif any(variant.lower() in col_lower for variant in width_variants):
                if '宽cm' not in df.columns and '宽cm' not in column_mapping.values():
                    column_mapping[col] = '宽cm'

            # 检测高度列 - 精确匹配优先
            elif col_str in height_variants or any(variant.lower() == col_lower for variant in height_variants):
                if '高cm' not in df.columns:
                    column_mapping[col] = '高cm'
            # 检测高度列 - 包含匹配
            elif any(variant.lower() in col_lower for variant in height_variants):
                if '高cm' not in df.columns and '高cm' not in column_mapping.values():
                    column_mapping[col] = '高cm'

            # 检测数量列 - 精确匹配优先
            elif col_str in quantity_variants or any(variant.lower() == col_lower for variant in quantity_variants):
                if '数量' not in df.columns:
                    column_mapping[col] = '数量'
            # 检测数量列 - 包含匹配
            elif any(variant.lower() in col_lower for variant in quantity_variants):
                if '数量' not in df.columns and '数量' not in column_mapping.values():
                    column_mapping[col] = '数量'

            # 检测图案全称列 - 精确匹配优先
            elif col_str in pattern_full_variants or any(variant.lower() == col_lower for variant in pattern_full_variants):
                if '图案全称' not in df.columns:
                    column_mapping[col] = '图案全称'
            # 检测图案全称列 - 包含匹配
            elif any(variant.lower() in col_lower for variant in pattern_full_variants):
                if '图案全称' not in df.columns and '图案全称' not in column_mapping.values():
                    column_mapping[col] = '图案全称'

            # 检测图案列 - 精确匹配优先
            elif col_str in pattern_variants or any(variant.lower() == col_lower for variant in pattern_variants):
                if '图案' not in df.columns and '图案全称' not in column_mapping.values():
                    column_mapping[col] = '图案'
            # 检测图案列 - 包含匹配
            elif any(variant.lower() in col_lower for variant in pattern_variants):
                if '图案' not in df.columns and '图案全称' not in column_mapping.values() and '图案' not in column_mapping.values():
                    column_mapping[col] = '图案'

        # 应用列名映射
        if column_mapping:
            df.rename(columns=column_mapping, inplace=True)
            log.info(f"列名标准化映射: {column_mapping}")

    def _validate_required_columns(self, df: pd.DataFrame, is_standard_mode: bool) -> Dict[str, any]:
        """智能验证必需列

        Args:
            df: 要验证的DataFrame
            is_standard_mode: 是否为标准模式

        Returns:
            包含success和message的字典
        """
        missing_columns = []
        suggestions = []

        # 根据模式调整验证策略
        mode_str = "标准" if is_standard_mode else "自定义"
        log.info(f"使用{mode_str}模式进行列验证")

        # 检查数量列（在自定义模式下放宽要求）
        has_quantity_col = '数量' in df.columns
        if not has_quantity_col:
            # 查找可能的数量列
            possible_qty_cols = [col for col in df.columns
                               if any(variant in str(col).lower()
                                     for variant in ['数量', '个数', '件数', 'quantity', 'qty'])]
            if possible_qty_cols:
                suggestions.append(f"可能的数量列: {', '.join(possible_qty_cols)}")
                # 在自定义模式下，如果找到可能的数量列，就认为有数量信息
                if not is_standard_mode:
                    has_quantity_col = True
                    log.info(f"自定义模式下接受可能的数量列: {possible_qty_cols}")

            # 在自定义模式下，即使没有数量列也可以继续（默认数量为1）
            if not is_standard_mode:
                has_quantity_col = True
                log.info("自定义模式下允许缺少数量列，将使用默认数量1")

            if not has_quantity_col:
                missing_columns.append('数量')

        # 检查尺寸信息
        has_size_info = False

        # 方式1：有尺寸列
        if '尺寸' in df.columns:
            has_size_info = True

        # 方式2：有宽度和高度列
        elif '宽cm' in df.columns and '高cm' in df.columns:
            has_size_info = True

        # 方式3：查找其他可能的尺寸列
        else:
            size_related_cols = [col for col in df.columns
                               if any(variant in str(col).lower()
                                     for variant in ['尺寸', '规格', '大小', '宽', '高', 'width', 'height', 'size'])]
            if size_related_cols:
                suggestions.append(f"发现可能的尺寸相关列: {', '.join(size_related_cols)}")
                # 如果有至少2个尺寸相关列，认为可能有尺寸信息
                if len(size_related_cols) >= 2:
                    has_size_info = True
                    log.info(f"检测到可能的尺寸列组合: {size_related_cols}")
                # 在自定义模式下，放宽尺寸要求
                elif not is_standard_mode and len(size_related_cols) >= 1:
                    has_size_info = True
                    log.info(f"自定义模式下接受单个尺寸列: {size_related_cols}")

        if not has_size_info:
            missing_columns.append('尺寸信息（尺寸列 或 宽度+高度列）')

        # 检查图案信息（在自定义模式下放宽要求）
        has_pattern_info = '图案全称' in df.columns or '图案' in df.columns
        if not has_pattern_info:
            # 查找可能的图案列
            possible_pattern_cols = [col for col in df.columns
                                   if any(variant in str(col).lower()
                                         for variant in ['图案', 'pattern', '名称', 'name'])]
            if possible_pattern_cols:
                suggestions.append(f"可能的图案列: {', '.join(possible_pattern_cols)}")
                # 在自定义模式下，如果找到可能的图案列，就认为有图案信息
                if not is_standard_mode:
                    has_pattern_info = True
                    log.info(f"自定义模式下接受可能的图案列: {possible_pattern_cols}")

            if not has_pattern_info:
                missing_columns.append('图案信息（图案全称 或 图案列）')

        # 构建结果
        if missing_columns:
            message = f"表格缺少必需列: {', '.join(missing_columns)}"
            if suggestions:
                message += f"\n建议检查以下列: {'; '.join(suggestions)}"
            message += f"\n当前表格列名: {', '.join(df.columns.tolist())}"
            return {'success': False, 'message': message}

        return {'success': True, 'message': '列验证通过'}

    def _save_all_sheets(self, original_file: str, output_file: str) -> Tuple[bool, str]:
        """保存所有处理后的工作表到一个Excel文件

        Args:
            original_file: 原始Excel文件路径
            output_file: 输出文件路径

        Returns:
            (成功标志, 消息)
        """
        try:
            # 创建输出目录
            os.makedirs(os.path.dirname(output_file), exist_ok=True)

            # 读取原始Excel的所有工作表
            with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
                xls = pd.ExcelFile(original_file)
                all_sheets = xls.sheet_names

                # 写入所有工作表
                for sheet_name in all_sheets:
                    # 跳过名为"数据异常"的工作表
                    if sheet_name == "数据异常":
                        log.info(f"保存时跳过数据异常工作表: {sheet_name}")
                        # 仍然保留原始的"数据异常"工作表
                        df = pd.read_excel(original_file, sheet_name=sheet_name)
                        df.to_excel(writer, sheet_name=sheet_name, index=False)
                        continue

                    if sheet_name in self._processed_sheets[original_file]:
                        # 确保DataFrame中的pattern_name字段为字符串类型
                        df = self._processed_sheets[original_file][sheet_name]
                        if 'pattern_name' in df.columns:
                            df['pattern_name'] = df['pattern_name'].astype(str)
                        if '图案全称' in df.columns:
                            df['图案全称'] = df['图案全称'].astype(str)
                        if '图案' in df.columns:
                            df['图案'] = df['图案'].astype(str)

                        # 写入处理后的数据
                        df.to_excel(
                            writer,
                            sheet_name=sheet_name,
                            index=False
                        )
                    else:
                        # 写入原始数据
                        df = pd.read_excel(original_file, sheet_name=sheet_name)
                        # 添加图片路径列（如果不存在）
                        if '图片路径' not in df.columns:
                            df['图片路径'] = '未处理'

                        # 确保图案相关列为字符串类型
                        if '图案全称' in df.columns:
                            df['图案全称'] = df['图案全称'].astype(str)
                        if '图案' in df.columns:
                            df['图案'] = df['图案'].astype(str)

                        df.to_excel(writer, sheet_name=sheet_name, index=False)

                # 设置所有工作表中的图案相关列为文本格式
                workbook = writer.book
                for sheet_name in workbook.sheetnames:
                    worksheet = workbook[sheet_name]
                    # 找到表头行
                    header_row = next(worksheet.rows)

                    # 查找图案相关列的索引
                    pattern_columns = {}
                    for idx, cell in enumerate(header_row):
                        if cell.value in ['pattern_name', '图案全称', '图案']:
                            pattern_columns[cell.value] = idx + 1  # openpyxl的列索引从1开始

                    # 设置图案相关列为文本格式
                    for col_name, col_idx in pattern_columns.items():
                        log.info(f"在{sheet_name}中设置{col_name}列(第{col_idx}列)为文本格式")
                        # 设置整列为文本格式
                        for row in range(2, worksheet.max_row + 1):  # 从第2行开始（跳过表头）
                            cell = worksheet.cell(row=row, column=col_idx)
                            cell.number_format = '@'  # '@'是文本格式的代码

            self.status_signal.emit(f"已保存处理后的Excel文件: {output_file}")
            return True, f"成功保存数据到 {output_file}"

        except Exception as e:
            error_msg = f"保存Excel文件失败: {str(e)}"
            log.error(error_msg)
            self.error_signal.emit(error_msg)
            return False, error_msg

    def read_sheet_data(self, excel_file: str, sheet_name: str, image_indexer, exact_pattern_search: bool = False, is_standard_mode: bool = True, is_fuzzy_query: bool = False) -> Tuple[bool, str, Optional[pd.DataFrame]]:
        """读取Excel表格数据并处理图案信息

        Args:
            excel_file: Excel文件路径
            sheet_name: 工作表名称
            image_indexer: 图片索引器实例
            exact_pattern_search: 是否仅精确查询图案全称
            is_standard_mode: 是否使用标准表格模式，True为标准模式，False为自定义模式
            is_fuzzy_query: 是否开启模糊查询，True为开启，False为关闭

        Returns:
            (成功标志, 消息, 处理后的DataFrame)
        """
        try:
            # 设置读取Excel时的数据类型转换器，确保图案相关列被读取为字符串
            converters = {
                'pattern_name': str,
                '图案全称': str,
                '图案': str
            }

            # 读取Excel文件，并指定图案相关列为字符串类型
            df = pd.read_excel(excel_file, sheet_name=sheet_name, converters=converters)
            log.info(f"原始数据: 行数={len(df)}, 列数={len(df.columns)}")
            log.info(f"原始列名: {list(df.columns)}")

            # 记录表格模式
            mode_str = "标准" if is_standard_mode else "自定义"
            log.info(f"使用{mode_str}表格模式处理工作表 {sheet_name}")
            self.status_signal.emit(f"使用{mode_str}表格模式处理工作表 {sheet_name}")

            # 记录模糊查询模式
            if is_fuzzy_query:
                log.info(f"模糊查询模式已开启，可以查询部分匹配的结果")
                self.status_signal.emit(f"模糊查询模式已开启，可以查询部分匹配的结果")

            # 智能检测和标准化列名
            self._standardize_column_names(df)

            # 检查列名并尝试标准化尺寸信息
            if '尺寸' in df.columns:
                # 处理尺寸列
                def parse_size(size_str):
                    try:
                        if isinstance(size_str, (int, float)):
                            return size_str, size_str
                        # 处理 "宽x高" 或 "宽X高" 格式
                        parts = str(size_str).replace('X', 'x').split('x')
                        if len(parts) == 2:
                            return float(parts[0]), float(parts[1])
                        # 处理单个数字（正方形）
                        return float(size_str), float(size_str)
                    except:
                        return None, None

                # 解析尺寸列
                sizes = df['尺寸'].apply(parse_size)
                df['宽cm'] = [s[0] if s else None for s in sizes]
                df['高cm'] = [s[1] if s else None for s in sizes]

            # 智能检查必需列
            log.info(f"开始列验证，当前列名: {list(df.columns)}")
            validation_result = self._validate_required_columns(df, is_standard_mode)
            log.info(f"列验证结果: {validation_result}")
            if not validation_result['success']:
                log.error(f"列验证失败: {validation_result['message']}")
                return False, validation_result['message'], None

            # 特殊检查：至少需要"图案全称"或"图案"中的一个
            if '图案全称' not in df.columns and '图案' not in df.columns:
                return False, "表格缺少必需列: 图案全称 或 图案", None

            # 如果缺少图案全称列，记录日志
            if '图案全称' not in df.columns and '图案' in df.columns:
                log.info(f"表格缺少'图案全称'列，将使用'图案'列进行匹配")
                self.status_signal.emit(f"表格缺少'图案全称'列，将使用'图案'列进行匹配")

            # 添加图片路径列
            df['图片路径'] = '未入库'

            # 添加查询方式列（用于标记精确查询还是模糊查询）
            df['查询方式'] = ''

            # 确保宽度和高度列为数值类型
            try:
                # 转换宽度和高度为数值类型
                df['width_cm'] = pd.to_numeric(df['宽cm'], errors='coerce')
                df['height_cm'] = pd.to_numeric(df['高cm'], errors='coerce')

                # 检查是否有无效的尺寸数据
                invalid_sizes = df[df['width_cm'].isna() | df['height_cm'].isna()]
                if not invalid_sizes.empty:
                    invalid_rows = [index + 2 for index in invalid_sizes.index]
                    return False, f"以下行的尺寸数据无效: {', '.join(map(str, invalid_rows))}", None

                # 检查尺寸范围
                if (df['width_cm'] <= 0).any() or (df['height_cm'] <= 0).any():
                    invalid_rows = [index + 2 for index, row in df.iterrows()
                                  if row['width_cm'] <= 0 or row['height_cm'] <= 0]
                    return False, f"以下行的尺寸必须大于0: {', '.join(map(str, invalid_rows))}", None

                log.info(f"工作表 {sheet_name} 的尺寸数据类型转换成功")
                log.info(f"宽度范围: {df['width_cm'].min():.2f} - {df['width_cm'].max():.2f} cm")
                log.info(f"高度范围: {df['height_cm'].min():.2f} - {df['height_cm'].max():.2f} cm")
            except Exception as e:
                log.error(f"尺寸数据类型转换失败: {str(e)}")
                return False, f"尺寸数据类型转换失败: {str(e)}", None

            # 初始化日志信息
            log.info(f"开始处理工作表 {sheet_name}，共 {len(df)} 行数据")
            self.status_signal.emit(f"开始处理工作表 {sheet_name}，共 {len(df)} 行数据")

            # 检查尺寸有效性
            invalid_sizes = df[(df['width_cm'] <= 0) | (df['height_cm'] <= 0)]
            if not invalid_sizes.empty:
                invalid_rows = [index + 2 for index in invalid_sizes.index]
                return False, f"以下行的尺寸无效: {', '.join(map(str, invalid_rows))}", None

            # 处理每一行数据
            total_rows = len(df)
            processed_count = 0
            found_count = 0

            for index, row in df.iterrows():
                # 初始化变量
                image_path = None
                pattern_name = None
                search_method = None

                # 第一优先级：使用图案全称进行精确匹配（如果列存在）
                if '图案全称' in df.columns and pd.notna(row['图案全称']):
                    # 确保pattern_name是字符串类型
                    pattern_name = str(row['图案全称']).strip()
                    if pattern_name and image_indexer is not None:
                        self.status_signal.emit(f"[第一优先级] 使用图案全称进行精确匹配: {pattern_name}")
                        log.info(f"[第一优先级] 使用图案全称进行精确匹配: {pattern_name}")
                        # 使用图案全称进行精确匹配
                        try:
                            image_path = image_indexer.find_image(pattern_name, exact_match=True)
                        except Exception as e:
                            log.error(f"查找图片时发生错误 {pattern_name}: {str(e)}")
                            image_path = None
                        if image_path:
                            search_method = "图案全称精确匹配"
                            self.status_signal.emit(f"✓ 使用图案全称找到图片: {pattern_name} -> {image_path}")
                            log.info(f"✓ 使用图案全称找到图片: {pattern_name} -> {image_path}")
                        else:
                            self.status_signal.emit(f"✗ 使用图案全称未找到图片: {pattern_name}")
                            log.info(f"✗ 使用图案全称未找到图片: {pattern_name}")

                # 第二优先级：使用图案进行精确匹配（当exact_pattern_search为False时，或者图案全称列不存在时）
                if (not exact_pattern_search or '图案全称' not in df.columns) and image_path is None and pd.notna(row['图案']):
                    # 确保pattern_name是字符串类型
                    pattern_name = str(row['图案']).strip()
                    if pattern_name and image_indexer is not None:
                        self.status_signal.emit(f"[第二优先级] 使用图案名称进行精确匹配: {pattern_name}")
                        log.info(f"[第二优先级] 使用图案名称进行精确匹配: {pattern_name}")
                        # 只进行精确匹配
                        try:
                            image_path = image_indexer.find_image(pattern_name, exact_match=True)
                        except Exception as e:
                            log.error(f"查找图片时发生错误 {pattern_name}: {str(e)}")
                            image_path = None
                        if image_path:
                            search_method = "图案名称精确匹配"
                            self.status_signal.emit(f"✓ 使用图案精确匹配找到图片: {pattern_name} -> {image_path}")
                            log.info(f"✓ 使用图案精确匹配找到图片: {pattern_name} -> {image_path}")
                        else:
                            self.status_signal.emit(f"✗ 使用图案精确匹配未找到图片: {pattern_name}")
                            log.info(f"✗ 使用图案精确匹配未找到图片: {pattern_name}")

                            # 如果开启了模糊查询，且精确匹配失败，尝试模糊匹配
                            if is_fuzzy_query:
                                self.status_signal.emit(f"[第三优先级] 使用图案名称进行模糊匹配: {pattern_name}")
                                log.info(f"[第三优先级] 使用图案名称进行模糊匹配: {pattern_name}")
                                try:
                                    image_path = image_indexer.find_image(pattern_name, exact_match=False)
                                except Exception as e:
                                    log.error(f"模糊查找图片时发生错误 {pattern_name}: {str(e)}")
                                    image_path = None
                                if image_path:
                                    search_method = "图案名称模糊匹配"
                                    self.status_signal.emit(f"✓ 使用图案模糊匹配找到图片: {pattern_name} -> {image_path}")
                                    log.info(f"✓ 使用图案模糊匹配找到图片: {pattern_name} -> {image_path}")
                                else:
                                    self.status_signal.emit(f"✗ 使用图案模糊匹配未找到图片: {pattern_name}")
                                    log.info(f"✗ 使用图案模糊匹配未找到图片: {pattern_name}")
                elif exact_pattern_search and '图案全称' in df.columns and image_path is None and pd.notna(row['图案']):
                    # 当开启精确查询图案全称时，且图案全称列存在，记录跳过图案字段的日志
                    # 确保pattern_name是字符串类型
                    pattern_name = str(row['图案']).strip()
                    if pattern_name:
                        self.status_signal.emit(f"[跳过] 精确查询图案全称模式已开启，跳过图案字段: {pattern_name}")
                        log.info(f"[跳过] 精确查询图案全称模式已开启，跳过图案字段: {pattern_name}")

                # 更新图片路径和查询方式，并记录日志
                if image_path:
                    df.at[index, '图片路径'] = image_path
                    # 更新查询方式列
                    df.at[index, '查询方式'] = search_method if search_method else '未知'
                    log.info(f"行 {index+2}: 使用{search_method}找到图片 {pattern_name} -> {image_path}")
                    self.status_signal.emit(f"行 {index+2}: 找到图片 {os.path.basename(image_path)}")
                else:
                    df.at[index, '图片路径'] = '未入库'
                    df.at[index, '查询方式'] = '未找到'
                    log.info(f"行 {index+2}: 未找到图片 {pattern_name}")
                    self.status_signal.emit(f"行 {index+2}: 未找到图片")
                if image_path:
                    self.status_signal.emit(f"[成功] 第{index+2}行 - {search_method}: {pattern_name} -> {image_path}")
                else:
                    self.status_signal.emit(f"[失败] 第{index+2}行 - 未找到图片: {pattern_name if pattern_name else '未知图案'}")

                # 更新计数
                processed_count += 1
                if image_path:
                    found_count += 1
                    log.info(f"[成功] 第{index+2}行 - {search_method}: {pattern_name} -> {image_path}")
                else:
                    log.warning(f"[失败] 第{index+2}行 - 未找到图片: {pattern_name if pattern_name else '未知图案'}")


                # 更新进度
                progress = int(processed_count / total_rows * 100)
                self.progress_signal.emit(progress)

                # 发送状态信息
                if processed_count % 10 == 0 or processed_count == total_rows:
                    self.status_signal.emit(f"已处理 {processed_count}/{total_rows} 行，找到 {found_count} 个图片")

            # 验证数据
            invalid_rows = []
            for index, row in df.iterrows():
                try:
                    # 检查数值是否有效
                    width = pd.to_numeric(row['宽cm'])
                    height = pd.to_numeric(row['高cm'])
                    quantity = pd.to_numeric(row['数量'])

                    # 记录无效数据
                    if pd.isna(width) or pd.isna(height) or pd.isna(quantity) or width <= 0 or height <= 0 or quantity <= 0:
                        invalid_rows.append({
                            'row': index + 2,
                            'width': row['宽cm'],
                            'height': row['高cm'],
                            'quantity': row['数量']
                        })
                except Exception as e:
                    invalid_rows.append({
                        'row': index + 2,
                        'error': str(e)
                    })

            if invalid_rows:
                error_details = []
                for item in invalid_rows:
                    if 'error' in item:
                        error_details.append(f"第{item['row']}行: 数据转换错误 - {item['error']}")
                    else:
                        error_details.append(
                            f"第{item['row']}行: 宽度={item['width']}, "
                            f"高度={item['height']}, 数量={item['quantity']}"
                        )
                log.error(f"数据验证失败:\n" + "\n".join(error_details))
                return False, f"以下行的数据无效:\n" + "\n".join(error_details), None

            # 添加宽度和高度列
            df['width_cm'] = pd.to_numeric(df['宽cm'])
            df['height_cm'] = pd.to_numeric(df['高cm'])

            # 输出尺寸统计信息
            log.info(f"工作表 {sheet_name} 尺寸统计:")
            log.info(f"宽度范围: {df['width_cm'].min():.2f} - {df['width_cm'].max():.2f} cm")
            log.info(f"高度范围: {df['height_cm'].min():.2f} - {df['height_cm'].max():.2f} cm")

            # 检查尺寸有效性
            invalid_sizes = df[(df['width_cm'] <= 0) | (df['height_cm'] <= 0)]
            if not invalid_sizes.empty:
                invalid_rows = [index + 2 for index in invalid_sizes.index]
                return False, f"以下行的尺寸无效: {', '.join(map(str, invalid_rows))}", None

            # 展开数量列，为每个图案创建多行
            expanded_rows = []
            for index, row in df.iterrows():
                new_row = {
                    '宽cm': row['width_cm'],
                    '高cm': row['height_cm'],
                    '数量': row['数量'],
                    '图片路径': row['图片路径'],
                    '查询方式': row['查询方式'],
                    'row_number': index + 2  # 添加表格行号（Excel行号从1开始，第1行是表头，所以加2）
                }

                # 添加图案全称列（如果存在），确保是字符串类型
                if '图案全称' in df.columns:
                    new_row['图案全称'] = str(row['图案全称']) if pd.notna(row['图案全称']) else None
                else:
                    new_row['图案全称'] = None  # 如果列不存在，设为None

                # 添加图案列，确保是字符串类型
                new_row['图案'] = str(row['图案']) if pd.notna(row['图案']) else None

                expanded_rows.append(new_row)


            # 创建展开后的DataFrame
            expanded_df = pd.DataFrame(expanded_rows)

            # 检查是否有可用的图片（仅在标准模式下检查）
            available_images = expanded_df[expanded_df['图片路径'] != '未入库']

            # 在标准模式下，要求必须有可用的图片
            # 在自定义模式下，允许先提取所有图案数据，后续再检索图片
            if is_standard_mode and available_images.empty:
                return False, "没有找到任何可用的图片", None

            # 返回所有数据（包括未入库的图片）
            total_patterns = len(expanded_df)
            available_count = len(available_images)

            if is_standard_mode:
                return True, f"成功处理 {available_count} 个图案", expanded_df
            else:
                return True, f"成功提取 {total_patterns} 个图案数据（其中 {available_count} 个已有图片，{total_patterns - available_count} 个待检索）", expanded_df

        except Exception as e:
            log.error(f"处理Excel文件失败: {str(e)}")
            import traceback
            log.error(traceback.format_exc())
            return False, f"处理Excel文件失败: {str(e)}", None