#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
图片排列器模块

提供图片排列功能，严格按照分类结果表格中的A类->B类->C类顺序进行排列：
1. 从分类结果表格中读取A类、B类、C类图片数据
2. 按照A类->B类->C类的顺序进行排列
3. 按照分类表格中的行顺序，依次获取图片信息进行排列
4. 高效、清晰的代码逻辑

注意：默认宽是长边，高是短边
"""

import logging
import time
from typing import List, Dict, Any, Tuple
from PyQt6.QtCore import QObject, pyqtSignal

# 导入俄罗斯方块式装箱器
from core.tetris_packer import TetrisPacker

# 导入新的RectPack排列器
try:
    from core.rectpack_arranger import RectPackArranger
    RECTPACK_AVAILABLE = True
except ImportError:
    RECTPACK_AVAILABLE = False

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
log = logging.getLogger("ImageArranger")

class ImageArranger(QObject):
    """图片排列器类

    功能特性：
    1. 严格按照分类结果表格中的A类->B类->C类顺序进行排列
    2. 按照分类表格中的行顺序，依次获取图片信息进行排列
    3. 高效、清晰的代码逻辑
    """

    # 信号定义
    progress_signal = pyqtSignal(int)  # 进度值 (0-100)
    log_signal = pyqtSignal(str)       # 日志信息
    error_signal = pyqtSignal(str)     # 错误信息

    def __init__(self, log_signal=None, use_rectpack=False):
        """初始化图片排列器

        Args:
            log_signal: 日志信号，用于向UI发送日志信息
            use_rectpack: 是否使用RectPack算法（默认False，使用原有算法）
        """
        super().__init__()
        self.log_signal = log_signal
        self.use_rectpack = use_rectpack and RECTPACK_AVAILABLE

        # 初始化俄罗斯方块式装箱器
        self.tetris_packer = None

        # 初始化RectPack排列器
        self.rectpack_arranger = None

        # 初始化画布设置
        self.canvas_width_px = 0
        self.canvas_height_px = 0
        self.image_spacing_px = 0
        self.max_height_px = 0
        self.ppi = 72  # 默认PPI值

        # 初始化排列状态
        self.images_arranged = 0
        self.arrangement_start_time = None
        self.last_speed_update_time = None
        self.speed_update_interval = 2.0  # 速度更新间隔（秒）

        # 初始化已处理图片ID集合
        self.processed_image_ids = set()

    def emit_log(self, message: str):
        """发送日志信息

        Args:
            message: 日志信息
        """
        if self.log_signal:
            self.log_signal.emit(message)
        log.info(message)

    def initialize(self, canvas_width_px: int, max_height_px: int, image_spacing_px: int, ppi: float = 72):
        """初始化画布设置

        Args:
            canvas_width_px: 画布宽度（像素）
            max_height_px: 最大高度（像素）
            image_spacing_px: 图片间距（像素）
            ppi: 每英寸像素数
        """
        self.canvas_width_px = canvas_width_px
        self.max_height_px = max_height_px
        self.image_spacing_px = image_spacing_px
        self.ppi = ppi

        # 初始化统一画布截断处理函数
        from core.unified_canvas_truncation import unified_canvas_truncation
        self.unified_canvas_truncation = unified_canvas_truncation

        # 初始化画布截断处理器（保留向后兼容性）
        from core.canvas_truncation_handler import CanvasTruncationHandler
        self.canvas_truncation_handler = CanvasTruncationHandler()

        if self.use_rectpack:
            # 使用RectPack排列器
            self.rectpack_arranger = RectPackArranger(
                container_width=canvas_width_px,
                image_spacing=image_spacing_px,
                max_height=max_height_px,
                log_signal=self.log_signal,
                canvas_truncation_handler=self.canvas_truncation_handler
            )
            self.emit_log(f"初始化RectPack图片排列器: 画布宽度={canvas_width_px}像素, 最大高度={max_height_px}像素, 图片间距={image_spacing_px}像素, PPI={ppi}")
        else:
            # 使用传统的俄罗斯方块式装箱器
            self.tetris_packer = TetrisPacker(
                container_width=canvas_width_px,
                image_spacing=image_spacing_px,
                max_height=max_height_px,
                log_signal=self.log_signal,
                canvas_truncation_handler=self.canvas_truncation_handler
            )

            # 设置画布截断处理器的俄罗斯方块式装箱器
            self.canvas_truncation_handler.tetris_packer = self.tetris_packer

            self.emit_log(f"初始化传统图片排列器: 画布宽度={canvas_width_px}像素, 最大高度={max_height_px}像素, 图片间距={image_spacing_px}像素, PPI={ppi}")

        # 重置排列状态
        self.images_arranged = 0
        self.arrangement_start_time = time.time()
        self.last_speed_update_time = time.time()
        self.processed_image_ids.clear()

    def set_algorithm_params(self, horizontal_priority: int, gap_filling_priority: int, rotation_priority: int):
        """设置算法参数

        Args:
            horizontal_priority: 水平优先级（百分比）
            gap_filling_priority: 空隙填充优先级（百分比）
            rotation_priority: 旋转优先级（百分比）
        """
        if self.tetris_packer:
            self.tetris_packer.horizontal_priority = horizontal_priority
            self.tetris_packer.gap_filling_priority = gap_filling_priority
            self.tetris_packer.rotation_priority = rotation_priority

            self.emit_log(f"设置算法参数: 水平优先级={horizontal_priority}%, 空隙填充优先级={gap_filling_priority}%, 旋转优先级={rotation_priority}%")

    def _cm_to_px(self, cm_value: float) -> int:
        """将厘米转换为像素

        Args:
            cm_value: 厘米值

        Returns:
            int: 像素值
        """
        # 1厘米 = 0.393701英寸
        inches = cm_value * 0.393701
        # 像素 = 英寸 * PPI
        pixels = int(inches * self.ppi)
        return pixels

    def _update_arrangement_speed(self):
        """更新排列速度信息"""
        current_time = time.time()
        elapsed_time = current_time - self.arrangement_start_time

        # 每隔一段时间更新一次速度信息
        if current_time - self.last_speed_update_time >= self.speed_update_interval:
            if elapsed_time > 0:
                images_per_second = self.images_arranged / elapsed_time
                self.emit_log(f"已排列 {self.images_arranged} 个图片，速度: {images_per_second:.2f} 图片/秒")

            self.last_speed_update_time = current_time

    def generate_unique_id(self, pattern: Dict[str, Any]) -> str:
        """生成图片的唯一标识符

        Args:
            pattern: 图片数据字典

        Returns:
            str: 唯一标识符
        """
        # 获取基本信息
        width_cm = pattern.get('width_cm', 0)
        height_cm = pattern.get('height_cm', 0)
        pattern_name = pattern.get('pattern_name', '')
        index = pattern.get('index', -1)
        row_number = pattern.get('row_number', -1)

        # 如果已有唯一标识符，则直接返回
        if 'unique_id' in pattern and pattern['unique_id']:
            return pattern['unique_id']

        # 创建基础唯一标识符
        base_unique_id = f"{pattern_name}_{width_cm:.1f}_{height_cm:.1f}"

        # 创建完整唯一标识符，包含索引和行号
        unique_id = f"{base_unique_id}_{index}_{row_number}"

        return unique_id

    def arrange_a_class_image(self, pattern: Dict[str, Any]) -> Tuple[Dict[str, Any], bool]:
        """排列A类图片

        Args:
            pattern: A类图片数据

        Returns:
            Tuple[Dict[str, Any], bool]: (图片信息, 是否成功)
        """
        # 获取图案信息
        image_path = pattern['path']
        width_cm = pattern['width_cm']
        height_cm = pattern['height_cm']
        pattern_name = pattern['pattern_name']
        need_rotation = pattern.get('need_rotation', False)

        # 使用通用方法生成唯一标识符
        unique_id = self.generate_unique_id(pattern)

        # 更新图片数据中的唯一标识符
        pattern['unique_id'] = unique_id

        # 检查是否已处理过该图片
        if unique_id in self.processed_image_ids:
            self.emit_log(f"跳过已处理的A类图片: {unique_id}")
            return None, False

        # 根据是否需要旋转设置宽高像素值
        if need_rotation:  # 需要旋转
            # 交换宽高 - 旋转90度意味着宽高互换
            width_px = self._cm_to_px(height_cm)  # 原高度变为宽度
            height_px = self._cm_to_px(width_cm)  # 原宽度变为高度

            # 记录交换后的值用于日志显示和调试
            self.emit_log(f"A类图片旋转90度: {pattern_name} 原尺寸({width_cm}x{height_cm}cm) → 旋转后({height_cm}x{width_cm}cm), 像素值({width_px}x{height_px}px)")
        else:  # 不需要旋转
            width_px = self._cm_to_px(width_cm)  # 宽度保持不变
            height_px = self._cm_to_px(height_cm)  # 高度保持不变

        # 准备图片数据
        image_data = {
            'path': image_path,
            'name': pattern_name,
            'width_cm': width_cm,
            'height_cm': height_cm,
            'original_width_px': width_px,
            'original_height_px': height_px,
            'width_larger': width_cm >= height_cm,  # 宽度是否大于等于高度（原始状态）
            'swapped_dimensions': need_rotation,  # 是否交换了宽高（旋转状态）
            'need_rotation': need_rotation,  # 是否需要旋转
            'image_class': 'A',  # 图片分类
            'max_height_limit': self.max_height_px,  # 最大高度限制（像素）
            'unique_id': unique_id  # 唯一标识符
        }

        # 根据配置选择排列算法
        if self.use_rectpack and self.rectpack_arranger:
            # 使用RectPack算法放置图片
            x, y, success = self.rectpack_arranger.find_position(width_px, height_px, image_data)
        else:
            # 使用俄罗斯方块式算法放置图片
            x, y, success = self.tetris_packer.find_position(width_px, height_px, image_data)

        if not success:
            self.emit_log(f"无法放置A类图片 {unique_id}")
            return None, False

        # 更新已排列图片数量
        self.images_arranged += 1
        self._update_arrangement_speed()

        # 添加到已处理图片集合
        self.processed_image_ids.add(unique_id)

        # 收集图像信息
        image_info = {
            'x': x,
            'y': y,
            'width': width_px,
            'height': height_px,
            'path': image_path,
            'name': pattern_name,
            'original_width_cm': width_cm,
            'original_height_cm': height_cm,
            'need_rotation': need_rotation,  # 添加旋转信息
            'image_class': 'A',  # 添加图片分类信息
            'table_width_cm': pattern.get('width_cm', width_cm),  # 添加表格里的宽数据
            'table_height_cm': pattern.get('height_cm', height_cm),  # 添加表格里的长数据
            'unique_id': unique_id  # 唯一标识符
        }

        return image_info, True

    def arrange_b_class_image(self, pattern: Dict[str, Any]) -> Tuple[Dict[str, Any], bool]:
        """排列B类图片

        Args:
            pattern: B类图片数据

        Returns:
            Tuple[Dict[str, Any], bool]: (图片信息, 是否成功)
        """
        # 获取图案信息
        image_path = pattern['path']
        width_cm = pattern['width_cm']
        height_cm = pattern['height_cm']
        pattern_name = pattern['pattern_name']
        need_rotation = pattern.get('need_rotation', False)

        # 使用通用方法生成唯一标识符
        unique_id = self.generate_unique_id(pattern)

        # 更新图片数据中的唯一标识符
        pattern['unique_id'] = unique_id

        # 检查是否已处理过该图片
        if unique_id in self.processed_image_ids:
            self.emit_log(f"跳过已处理的B类图片: {unique_id}")
            return None, False

        # 根据是否需要旋转设置宽高像素值
        if need_rotation:  # 需要旋转
            # 交换宽高 - 旋转90度意味着宽高互换
            width_px = self._cm_to_px(height_cm)  # 原高度变为宽度
            height_px = self._cm_to_px(width_cm)  # 原宽度变为高度

            # 记录交换后的值用于日志显示和调试
            self.emit_log(f"B类图片旋转90度: {pattern_name} 原尺寸({width_cm}x{height_cm}cm) → 旋转后({height_cm}x{width_cm}cm), 像素值({width_px}x{height_px}px)")
        else:  # 不需要旋转
            width_px = self._cm_to_px(width_cm)  # 宽度保持不变
            height_px = self._cm_to_px(height_cm)  # 高度保持不变

        # 准备图片数据
        image_data = {
            'path': image_path,
            'name': pattern_name,
            'width_cm': width_cm,
            'height_cm': height_cm,
            'original_width_px': width_px,
            'original_height_px': height_px,
            'width_larger': width_cm >= height_cm,  # 宽度是否大于等于高度（原始状态）
            'swapped_dimensions': need_rotation,  # 是否交换了宽高（旋转状态）
            'need_rotation': need_rotation,  # 是否需要旋转
            'image_class': 'B',  # 图片分类
            'multiplier': pattern.get('multiplier', 1),  # 倍数
            'group_index': pattern.get('group_index', 0),  # 组索引
            'in_group_index': pattern.get('in_group_index', 0),  # 组内索引
            'group_size': pattern.get('group_size', 0),  # 组大小（总图片数）
            'is_last_in_row': pattern.get('is_last_in_row', False),  # 是否是行中的最后一个图片
            'max_height_limit': self.max_height_px,  # 最大高度限制（像素）
            'unique_id': unique_id  # 唯一标识符
        }

        # 根据配置选择排列算法
        if self.use_rectpack and self.rectpack_arranger:
            # 使用RectPack算法放置图片
            x, y, success = self.rectpack_arranger.find_position(width_px, height_px, image_data)
        else:
            # 使用俄罗斯方块式算法放置图片
            x, y, success = self.tetris_packer.find_position(width_px, height_px, image_data)

        if not success:
            self.emit_log(f"无法放置B类图片 {unique_id}")
            return None, False

        # 更新已排列图片数量
        self.images_arranged += 1
        self._update_arrangement_speed()

        # 添加到已处理图片集合
        self.processed_image_ids.add(unique_id)

        # 收集图像信息
        image_info = {
            'x': x,
            'y': y,
            'width': width_px,
            'height': height_px,
            'path': image_path,
            'name': pattern_name,
            'original_width_cm': width_cm,
            'original_height_cm': height_cm,
            'need_rotation': need_rotation,  # 添加旋转信息
            'image_class': 'B',  # 添加图片分类信息
            'table_width_cm': pattern.get('width_cm', width_cm),  # 添加表格里的宽数据
            'table_height_cm': pattern.get('height_cm', height_cm),  # 添加表格里的长数据
            'unique_id': unique_id  # 唯一标识符
        }

        return image_info, True

    def arrange_c_class_image(self, pattern: Dict[str, Any]) -> Tuple[Dict[str, Any], bool]:
        """排列C类图片

        Args:
            pattern: C类图片数据

        Returns:
            Tuple[Dict[str, Any], bool]: (图片信息, 是否成功)
        """
        # 获取图案信息
        image_path = pattern['path']
        width_cm = pattern['width_cm']
        height_cm = pattern['height_cm']
        pattern_name = pattern['pattern_name']
        need_rotation = pattern.get('need_rotation', False)

        # 使用通用方法生成唯一标识符
        unique_id = self.generate_unique_id(pattern)

        # 更新图片数据中的唯一标识符
        pattern['unique_id'] = unique_id

        # 检查是否已处理过该图片
        if unique_id in self.processed_image_ids:
            self.emit_log(f"跳过已处理的C类图片: {unique_id}")
            return None, False

        # 根据是否需要旋转设置宽高像素值
        if need_rotation:  # 需要旋转
            # 交换宽高 - 旋转90度意味着宽高互换
            width_px = self._cm_to_px(height_cm)  # 原高度变为宽度
            height_px = self._cm_to_px(width_cm)  # 原宽度变为高度

            # 记录交换后的值用于日志显示和调试
            self.emit_log(f"C类图片旋转90度: {pattern_name} 原尺寸({width_cm}x{height_cm}cm) → 旋转后({height_cm}x{width_cm}cm), 像素值({width_px}x{height_px}px)")
        else:  # 不需要旋转
            width_px = self._cm_to_px(width_cm)  # 宽度保持不变
            height_px = self._cm_to_px(height_cm)  # 高度保持不变

        # 准备图片数据
        image_data = {
            'path': image_path,
            'name': pattern_name,
            'width_cm': width_cm,
            'height_cm': height_cm,
            'original_width_px': width_px,
            'original_height_px': height_px,
            'width_larger': width_cm >= height_cm,  # 宽度是否大于等于高度（原始状态）
            'swapped_dimensions': need_rotation,  # 是否交换了宽高（旋转状态）
            'need_rotation': need_rotation,  # 是否需要旋转
            'image_class': 'C',  # 图片分类
            'max_height_limit': self.max_height_px,  # 最大高度限制（像素）
            'unique_id': unique_id  # 唯一标识符
        }

        # 根据配置选择排列算法
        if self.use_rectpack and self.rectpack_arranger:
            # 使用RectPack算法放置图片
            x, y, success = self.rectpack_arranger.find_position(width_px, height_px, image_data)
        else:
            # 使用俄罗斯方块式算法放置图片
            x, y, success = self.tetris_packer.find_position(width_px, height_px, image_data)

        if not success:
            self.emit_log(f"无法放置C类图片 {unique_id}")
            return None, False

        # 更新已排列图片数量
        self.images_arranged += 1
        self._update_arrangement_speed()

        # 添加到已处理图片集合
        self.processed_image_ids.add(unique_id)

        # 收集图像信息
        image_info = {
            'x': x,
            'y': y,
            'width': width_px,
            'height': height_px,
            'path': image_path,
            'name': pattern_name,
            'original_width_cm': width_cm,
            'original_height_cm': height_cm,
            'need_rotation': need_rotation,  # 添加旋转信息
            'image_class': 'C',  # 添加图片分类信息
            'table_width_cm': pattern.get('width_cm', width_cm),  # 添加表格里的宽数据
            'table_height_cm': pattern.get('height_cm', height_cm),  # 添加表格里的长数据
            'unique_id': unique_id  # 唯一标识符
        }

        return image_info, True

    def arrange_images(self, class_a_patterns: List[Dict[str, Any]],
                      class_b_patterns: List[Dict[str, Any]],
                      class_c_patterns: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """按照A类->B类->C类的顺序排列图片

        Args:
            class_a_patterns: A类图片列表
            class_b_patterns: B类图片列表
            class_c_patterns: C类图片列表

        Returns:
            List[Dict[str, Any]]: 排列后的图片信息列表
        """
        # 重置排列状态
        self.images_arranged = 0
        self.arrangement_start_time = time.time()
        self.last_speed_update_time = time.time()
        self.processed_image_ids.clear()

        # 重置排列器
        if self.use_rectpack and self.rectpack_arranger:
            # 重置RectPack排列器
            self.rectpack_arranger.reset()
            self.emit_log("重置RectPack排列器")
        elif self.tetris_packer:
            # 重置俄罗斯方块式装箱器
            self.tetris_packer.reset()

            # 确保最大高度限制正确设置
            if hasattr(self, 'max_height_px') and self.max_height_px > 0:
                self.tetris_packer.max_height = self.max_height_px
                self.emit_log(f"设置最大高度限制: {self.max_height_px}像素")

            # 确保画布截断处理器正确设置
            if hasattr(self, 'canvas_truncation_handler') and self.canvas_truncation_handler:
                self.canvas_truncation_handler.tetris_packer = self.tetris_packer
                self.emit_log("重置画布截断处理器")

        # 排列结果
        arranged_images = []

        # 创建输入数据的副本，避免修改原始数据
        class_a_patterns_copy = [pattern.copy() for pattern in class_a_patterns]
        class_b_patterns_copy = [pattern.copy() for pattern in class_b_patterns]
        class_c_patterns_copy = [pattern.copy() for pattern in class_c_patterns]

        # 记录总图片数量
        total_images = len(class_a_patterns_copy) + len(class_b_patterns_copy) + len(class_c_patterns_copy)
        self.emit_log(f"开始排列图片: A类 {len(class_a_patterns_copy)}个, B类 {len(class_b_patterns_copy)}个, C类 {len(class_c_patterns_copy)}个, 总计 {total_images}个")

        # 1. 首先排列A类图片 - 遵循A类排列规则
        self.emit_log("开始排列A类图片...")
        self.emit_log("A类图片排列规则: 宽幅类图片，直接放置或旋转90度")
        for i, pattern in enumerate(class_a_patterns_copy):
            # 发送进度信号
            progress = int((i / total_images) * 100)
            self.progress_signal.emit(progress)

            # 检查画布是否已满
            if hasattr(self.tetris_packer, 'canvas_is_full') and self.tetris_packer.canvas_is_full:
                self.emit_log("检测到画布已满标志，停止排列A类图片")
                break

            # 排列A类图片
            image_info, success = self.arrange_a_class_image(pattern)
            if success:
                arranged_images.append(image_info)
                self.emit_log(f"成功排列A类图片 {i+1}/{len(class_a_patterns_copy)}: {pattern.get('pattern_name', '')}")
            else:
                self.emit_log(f"无法排列A类图片 {pattern.get('pattern_name', '')}_{pattern.get('width_cm', 0)}_{pattern.get('height_cm', 0)}")
                # 检查是否因为画布高度限制而无法排列
                if hasattr(self.tetris_packer, 'current_max_height') and hasattr(self.tetris_packer, 'max_height'):
                    current_height = self.tetris_packer.current_max_height
                    max_height = self.tetris_packer.max_height
                    if max_height > 0 and current_height + pattern.get('height_px', 0) > max_height:
                        self.emit_log(f"当前高度 {current_height}px + 图片高度 {pattern.get('height_px', 0)}px 会超过最大高度限制 {max_height}px")
                        # 画布已满，停止排列
                        break

                # 检查画布是否已满
                if hasattr(self.tetris_packer, 'canvas_is_full') and self.tetris_packer.canvas_is_full:
                    self.emit_log("检测到画布已满标志，停止排列A类图片")
                    break

        # 2. 然后排列B类图片 - 遵循B类排列规则
        self.emit_log("开始排列B类图片...")
        self.emit_log("B类图片排列规则: 宽幅约束类图片，按约数关系分组排列")
        for i, pattern in enumerate(class_b_patterns_copy):
            # 发送进度信号
            progress = int(((len(class_a_patterns_copy) + i) / total_images) * 100)
            self.progress_signal.emit(progress)

            # 检查画布是否已满
            if hasattr(self.tetris_packer, 'canvas_is_full') and self.tetris_packer.canvas_is_full:
                self.emit_log("检测到画布已满标志，停止排列B类图片")
                break

            # 排列B类图片
            image_info, success = self.arrange_b_class_image(pattern)
            if success:
                arranged_images.append(image_info)
                self.emit_log(f"成功排列B类图片 {i+1}/{len(class_b_patterns_copy)}: {pattern.get('pattern_name', '')}")
            else:
                self.emit_log(f"无法排列B类图片 {pattern.get('pattern_name', '')}_{pattern.get('width_cm', 0)}_{pattern.get('height_cm', 0)}")
                # 检查是否因为画布高度限制而无法排列
                if hasattr(self.tetris_packer, 'current_max_height') and hasattr(self.tetris_packer, 'max_height'):
                    current_height = self.tetris_packer.current_max_height
                    max_height = self.tetris_packer.max_height
                    if max_height > 0 and current_height + pattern.get('height_px', 0) > max_height:
                        self.emit_log(f"当前高度 {current_height}px + 图片高度 {pattern.get('height_px', 0)}px 会超过最大高度限制 {max_height}px")
                        # 画布已满，停止排列
                        break

                # 检查画布是否已满
                if hasattr(self.tetris_packer, 'canvas_is_full') and self.tetris_packer.canvas_is_full:
                    self.emit_log("检测到画布已满标志，停止排列B类图片")
                    break

        # 3. 最后排列C类图片 - 遵循C类排列规则
        self.emit_log("开始排列C类图片...")
        self.emit_log("C类图片排列规则: 俄罗斯方块类图片，使用俄罗斯方块算法排列")
        for i, pattern in enumerate(class_c_patterns_copy):
            # 发送进度信号
            progress = int(((len(class_a_patterns_copy) + len(class_b_patterns_copy) + i) / total_images) * 100)
            self.progress_signal.emit(progress)

            # 检查画布是否已满
            if hasattr(self.tetris_packer, 'canvas_is_full') and self.tetris_packer.canvas_is_full:
                self.emit_log("检测到画布已满标志，停止排列C类图片")
                break

            # 排列C类图片
            image_info, success = self.arrange_c_class_image(pattern)
            if success:
                arranged_images.append(image_info)
                self.emit_log(f"成功排列C类图片 {i+1}/{len(class_c_patterns_copy)}: {pattern.get('pattern_name', '')}")
            else:
                self.emit_log(f"无法排列C类图片 {pattern.get('pattern_name', '')}_{pattern.get('width_cm', 0)}_{pattern.get('height_cm', 0)}")
                # 检查是否因为画布高度限制而无法排列
                if hasattr(self.tetris_packer, 'current_max_height') and hasattr(self.tetris_packer, 'max_height'):
                    current_height = self.tetris_packer.current_max_height
                    max_height = self.tetris_packer.max_height
                    if max_height > 0 and current_height + pattern.get('height_px', 0) > max_height:
                        self.emit_log(f"当前高度 {current_height}px + 图片高度 {pattern.get('height_px', 0)}px 会超过最大高度限制 {max_height}px")
                        # 画布已满，停止排列
                        break

                # 检查画布是否已满
                if hasattr(self.tetris_packer, 'canvas_is_full') and self.tetris_packer.canvas_is_full:
                    self.emit_log("检测到画布已满标志，停止排列C类图片")
                    break

        # 发送100%进度信号
        self.progress_signal.emit(100)

        # 记录排列结果
        self.emit_log(f"图片排列完成: 共排列 {len(arranged_images)}/{total_images} 个图片")

        # 设置所有图片都已排列完成标志
        if hasattr(self, 'tetris_packer') and self.tetris_packer:
            self.tetris_packer.is_all_images_arranged = True
            self.tetris_packer.is_last_canvas = True  # 设置为最后一个画布
            self.emit_log("设置所有图片都已排列完成标志，画布将以最底部图片的底部为实际高度")

            # 处理底部单图片 - 使用统一画布截断处理函数
            if hasattr(self, 'unified_canvas_truncation') and self.unified_canvas_truncation:
                # 获取剩余图片列表
                remaining_patterns = []
                # 调用统一画布截断处理函数，设置is_last_batch为True
                truncation_result = self.unified_canvas_truncation.truncate_canvas(
                    self.tetris_packer,
                    remaining_patterns,
                    is_last_batch=True
                )

                if truncation_result.get('success', False):
                    self.emit_log(f"画布截断处理成功: {truncation_result.get('message', '')}")
                    self.emit_log(f"画布截断高度: {truncation_result.get('height', 0)}像素")

                    # 检查是否有移动的图片
                    moved_patterns = truncation_result.get('moved_patterns', [])
                    if moved_patterns:
                        self.emit_log(f"有 {len(moved_patterns)} 个图片被移动到下一画布")
            # 保留向后兼容性
            elif hasattr(self, 'canvas_truncation_handler') and self.canvas_truncation_handler:
                # 获取剩余图片列表
                remaining_patterns = []
                # 调用画布截断处理函数，设置is_last_batch为True
                truncation_result = self.canvas_truncation_handler.handle_canvas_truncation(
                    self.tetris_packer,
                    remaining_patterns,
                    is_last_batch=True
                )

                if truncation_result.get('success', False):
                    self.emit_log(f"画布截断处理成功: {truncation_result.get('message', '')}")

        return arranged_images

    def get_canvas_height(self) -> int:
        """获取当前画布高度

        使用统一画布截断处理函数获取画布高度，确保在所有情况下都以最底部图片的底边为截断高度。

        Returns:
            int: 画布高度（像素）
        """
        if not self.tetris_packer:
            # 如果没有初始化装箱器，返回默认高度
            self.emit_log("警告: 装箱器未初始化，无法获取画布高度，使用默认高度")
            return 1000  # 默认高度

        try:
            # 获取当前高度
            current_height = self.tetris_packer.get_current_height()

            # 检查高度是否合理
            if current_height <= 0:
                self.emit_log("警告: 获取到的画布高度为0或负值，使用默认最小高度")
                return 1000  # 默认最小高度

            # 检查高度是否超过最大限制
            if current_height > self.max_height_px:
                self.emit_log(f"警告: 获取到的画布高度 ({current_height}) 超过最大限制 ({self.max_height_px})，将使用最大限制值")
                return self.max_height_px

            # 返回当前高度
            self.emit_log(f"获取画布高度: {current_height}像素")
            return current_height

        except Exception as e:
            # 如果出现异常，返回默认高度
            self.emit_log(f"警告: 获取画布高度时出现异常: {str(e)}，使用默认高度")
            return 1000  # 默认高度
