#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
图片分类模块

提供图片分类功能，将图片分为A类、B类和C类：
1. A类（宽幅类）：当图片宽度（长边）与画布宽度相等或达到95%以上时，归为A类
2. B类（宽幅约束类）：当图片宽度（长边）或高度（短边）的倍数和画布宽度相等或达到95%以上时，归为B类
3. C类（其他图片）：不符合A类和B类条件的图片归为C类

注意：默认宽是长边，高是短边

高内聚低耦合设计，便于维护和扩展
"""

import logging
from typing import List, Dict, Any, Tuple

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
log = logging.getLogger("ImageClassifier")

class ImageClassifier:
    """图片分类器，提供图片分类功能"""

    def __init__(self, log_signal=None):
        """
        初始化图片分类器

        Args:
            log_signal: 日志信号，用于向UI发送日志信息
        """
        self.log_signal = log_signal

    def determine_divisor_edge(self, width_cm: float, height_cm: float, b_class_canvas_width: float, multiplier: int = 1) -> dict:
        """
        统一的约数边判断函数，确定图片的约数边类型和旋转状态

        注意：默认宽是长边，高是短边

        Args:
            width_cm: 图片宽度（厘米，长边）
            height_cm: 图片高度（厘米，短边）
            b_class_canvas_width: B类图片分组使用的画布宽度（厘米）
            multiplier: 倍数，默认为1

        Returns:
            dict: 包含约数边信息的字典，包括：
                - divisor_edge_type: 约数边类型（'width'或'height'）
                - divisor_edge_name: 约数边名称（'width'或'height'）
                - divisor_edge_length: 约数边长度
                - need_rotation: 是否需要旋转
        """
        # 默认宽是长边，高是短边
        # 检查哪个边与画布宽度形成约数关系
        width_multiplied = width_cm * multiplier
        height_multiplied = height_cm * multiplier

        # 计算与画布宽度的差值百分比
        width_diff_percent = abs(width_multiplied - b_class_canvas_width) / b_class_canvas_width * 100
        height_diff_percent = abs(height_multiplied - b_class_canvas_width) / b_class_canvas_width * 100

        # 使用5%作为默认误差范围，允许更多图片归为B类
        class_b_error_range = 0.05  # 5%的误差范围

        # 确定约数边和旋转状态
        if height_multiplied <= b_class_canvas_width and height_diff_percent <= class_b_error_range * 100:
            # 高度（短边）是约数边
            divisor_edge_type = 'height'
            divisor_edge_name = 'height'
            divisor_edge_length = height_cm
            # 约数边是高度（短边）时，需要旋转90度
            need_rotation = True
            self.emit_log(f"约数边是高度(短边)，长度: {height_cm}厘米，倍数: {multiplier}，乘积: {height_multiplied}厘米，需要旋转90度")
        elif width_multiplied <= b_class_canvas_width and width_diff_percent <= class_b_error_range * 100:
            # 宽度（长边）是约数边
            divisor_edge_type = 'width'
            divisor_edge_name = 'width'
            divisor_edge_length = width_cm
            # 约数边是宽度（长边）时，不需要旋转
            need_rotation = False
            self.emit_log(f"约数边是宽度(长边)，长度: {width_cm}厘米，倍数: {multiplier}，乘积: {width_multiplied}厘米，不需要旋转")
        else:
            # 无法确定约数边，使用默认设置
            divisor_edge_type = 'unknown'
            divisor_edge_name = 'width'  # 默认使用宽度作为约数边
            divisor_edge_length = width_cm
            need_rotation = False  # 默认不旋转
            self.emit_log(f"无法确定约数边，使用默认设置：宽度(长边)，长度: {width_cm}厘米，不需要旋转")

        return {
            'divisor_edge_type': divisor_edge_type,
            'divisor_edge_name': divisor_edge_name,
            'divisor_edge_length': divisor_edge_length,
            'need_rotation': need_rotation
        }

    def emit_log(self, message: str):
        """发送日志信息

        Args:
            message: 日志信息
        """
        if self.log_signal:
            self.log_signal.emit(message)
        log.info(message)

    def extract_class_a_patterns(self, pattern_items: List[Dict[str, Any]], canvas_width_cm: float,
                               class_a_threshold: float = 0.95) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
        """
        从所有图片中提取A类图片

        A类图片定义：
        1. 图片宽度（长边）与宽幅相等或相近，则为A类图片，默认横向放置，不需要旋转
        2. 图片高度（短边）与宽幅相等或相近，则为A类图片，旋转90度，竖着放置

        Args:
            pattern_items: 图片列表，每个图片是一个字典，包含width_cm、height_cm等信息
            canvas_width_cm: 画布宽度（厘米）
            class_a_threshold: A类阈值，默认为0.95（95%）

        Returns:
            Tuple[List[Dict], List[Dict]]: A类图片列表和剩余图片列表
        """
        class_a_patterns = []
        remaining_patterns = []

        # 计算A类阈值
        width_threshold = canvas_width_cm * class_a_threshold
        width_max_threshold = canvas_width_cm

        # 计算误差范围（5%）
        error_range = 0.05

        self.emit_log(f"第一步：从所有图片中提取A类图片...")
        self.emit_log(f"A类阈值: 宽度或高度 >= {width_threshold:.2f}cm 且 <= {width_max_threshold:.2f}cm")
        self.emit_log(f"误差范围: {error_range*100:.0f}%")

        # 遍历图片，提取A类图片
        for pattern in pattern_items:
            width_cm = pattern['width_cm']  # 宽度（长边）
            height_cm = pattern['height_cm']  # 高度（短边）
            pattern_name = pattern.get('pattern_name', '')

            # 计算宽度和高度与画布宽度的差值百分比
            width_diff_percent = abs(width_cm - canvas_width_cm) / canvas_width_cm
            height_diff_percent = abs(height_cm - canvas_width_cm) / canvas_width_cm

            # 情况1: 图片宽度与宽幅相等或相近（误差在5%以内）
            if width_cm >= width_threshold and width_diff_percent <= error_range:
                pattern['image_class'] = 'A'
                pattern['need_rotation'] = False  # 不需要旋转
                class_a_patterns.append(pattern)
                self.emit_log(f"图片 {pattern_name} 分类为A类(宽幅类): 宽度({width_cm:.2f}cm)与画布宽度({canvas_width_cm:.2f}cm)相近，横向放置")

            # 情况2: 图片高度与宽幅相等或相近（误差在5%以内）
            elif height_cm >= width_threshold and height_diff_percent <= error_range:
                pattern['image_class'] = 'A'
                pattern['need_rotation'] = True  # 需要旋转90度
                class_a_patterns.append(pattern)
                self.emit_log(f"图片 {pattern_name} 分类为A类(宽幅类): 高度({height_cm:.2f}cm)与画布宽度({canvas_width_cm:.2f}cm)相近，旋转90度竖着放置")

            else:
                # 不符合A类条件，添加到剩余列表
                remaining_patterns.append(pattern)

        self.emit_log(f"提取出 {len(class_a_patterns)} 个A类图片，剩余 {len(remaining_patterns)} 个图片")
        return class_a_patterns, remaining_patterns

    def group_by_size(self, pattern_items: List[Dict[str, Any]]) -> Dict[Tuple[float, float], List[Dict[str, Any]]]:
        """
        按照相同宽高尺寸对图片进行分组
        同时为每个图片添加唯一标识符，格式为"图片名_宽_高"

        Args:
            pattern_items: 图片列表

        Returns:
            Dict[Tuple[float, float], List[Dict]]: 按尺寸分组的图片字典，键为(width, height)元组
        """
        size_groups = {}

        for pattern in pattern_items:
            width_cm = pattern['width_cm']
            height_cm = pattern['height_cm']
            pattern_name = pattern.get('pattern_name', '')

            # 创建唯一标识符，格式为"图片名_宽_高"
            unique_id = f"{pattern_name}_{width_cm:.1f}_{height_cm:.1f}"
            pattern['unique_id'] = unique_id

            # 使用四舍五入到0.1厘米作为分组键，使相似尺寸的图片能分到同一组
            size_key = (round(width_cm * 10) / 10, round(height_cm * 10) / 10)

            if size_key not in size_groups:
                size_groups[size_key] = []

            size_groups[size_key].append(pattern)

        # 记录分组结果
        self.emit_log(f"按照相同尺寸分组，共 {len(size_groups)} 个尺寸组")
        for size_key, group in size_groups.items():
            self.emit_log(f"尺寸组 {size_key}: 包含 {len(group)} 个图片")
            # 记录前几个图片的唯一ID，便于调试
            if len(group) > 0:
                sample_ids = [p.get('unique_id', '') for p in group[:min(3, len(group))]]
                self.emit_log(f"  - 样本图片ID: {', '.join(sample_ids)}")

        return size_groups

    def check_b_class_condition(self, width_cm: float, height_cm: float, canvas_width_cm: float,
                               class_b_error_range: float = 0.05) -> Tuple[bool, bool, int]:
        """
        检查图片是否满足B类条件，优化版本
        严格检查约数关系，确保倍数不超过画布宽度

        Args:
            width_cm: 图片宽度（长边）
            height_cm: 图片高度（短边）
            canvas_width_cm: 画布宽度
            class_b_error_range: B类误差范围，默认为0.05（5%）

        Returns:
            Tuple[bool, bool, int]: 是否满足B类条件，是否需要旋转，倍数值
        """
        # 首先检查图片尺寸是否超过画布宽度
        if width_cm > canvas_width_cm and height_cm > canvas_width_cm:
            # 如果宽度和高度都超过画布宽度，则不满足B类条件
            return False, False, 0

        # 检查宽度（长边）是否可以形成约数关系
        for multiplier in range(2, 6):  # 检查从2倍到6倍
            width_multiplied = width_cm * multiplier

            # 确保宽度的倍数不超过画布宽度
            if width_multiplied > canvas_width_cm:
                continue

            # 计算与画布宽度的差值百分比
            width_diff_percent = abs(width_multiplied - canvas_width_cm) / canvas_width_cm * 100

            # 确保差值百分比在允许范围内
            if width_diff_percent <= class_b_error_range * 100:
                # 检查是否是合理的约数关系
                ratio = canvas_width_cm / width_cm
                if abs(ratio - round(ratio)) > 0.01:  # 如果比率不接近整数
                    # 不是合理的约数关系，跳过
                    self.emit_log(f"宽度 {width_cm}cm 与画布宽度 {canvas_width_cm}cm 不构成合理的约数关系，比率为 {ratio:.2f}")
                    continue

                # 宽度（长边）满足约数关系，不需要旋转
                return True, False, multiplier

        # 检查高度（短边）是否可以形成约数关系
        for multiplier in range(2, 6):
            height_multiplied = height_cm * multiplier

            # 确保高度的倍数不超过画布宽度
            if height_multiplied > canvas_width_cm:
                continue

            # 计算与画布宽度的差值百分比
            height_diff_percent = abs(height_multiplied - canvas_width_cm) / canvas_width_cm * 100

            # 确保差值百分比在允许范围内
            if height_diff_percent <= class_b_error_range * 100:
                # 检查是否是合理的约数关系
                ratio = canvas_width_cm / height_cm
                if abs(ratio - round(ratio)) > 0.01:  # 如果比率不接近整数
                    # 不是合理的约数关系，跳过
                    self.emit_log(f"高度 {height_cm}cm 与画布宽度 {canvas_width_cm}cm 不构成合理的约数关系，比率为 {ratio:.2f}")
                    continue

                # 高度（短边）满足约数关系，需要旋转
                return True, True, multiplier

        # 不满足B类条件
        return False, False, 0

    def extract_class_b_patterns(self, pattern_items: List[Dict[str, Any]], canvas_width_cm: float,
                               class_b_error_range: float = 0.05,
                               original_canvas_width_cm: float = None) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
        """
        从剩余图片中提取B类图片，优化版本
        使用唯一标识符"图片名_宽_高"来区分不同尺寸的同名图片

        重构版本：
        1. 首先按照相同宽高尺寸对图片进行分组
        2. 对每组相同尺寸的图片，检查它们是否满足B类图片的约数关系条件
        3. 例如：在画布宽度为200cm的情况下，如果有6个40x120cm的图片，则其中5个可以组成一组B类图片
           （因为5×40=200，正好是画布宽度），剩余的1个40x120cm图片则归类为C类图片

        注意：此方法仅进行图片分类，不涉及画布排列逻辑
        分类仅基于图片边长与宽幅的关系，不考虑画布排列

        Args:
            pattern_items: 剩余图片列表
            canvas_width_cm: 画布宽度
            class_b_error_range: B类误差范围，默认为0.05（5%）
            original_canvas_width_cm: 原始画布宽度

        Returns:
            Tuple[List[Dict], List[Dict]]: B类图片列表和剩余图片列表
        """
        # 使用原始画布宽度进行B类图片分组
        b_class_canvas_width = original_canvas_width_cm if original_canvas_width_cm is not None else canvas_width_cm
        self.emit_log(f"第二步：从剩余图片中提取B类图片...")
        self.emit_log(f"B类图片分组使用原始画布宽度: {b_class_canvas_width}厘米（不包含水平扩展）")

        # 按照相同尺寸分组，同时为每个图片添加唯一标识符
        size_groups = self.group_by_size(pattern_items)
        self.emit_log(f"按照相同尺寸分组，共 {len(size_groups)} 个尺寸组")

        # 初始化结果
        class_b_patterns = []
        remaining_patterns = []

        # 处理每个尺寸组
        for size_key, group in size_groups.items():
            width_cm, height_cm = size_key
            group_size = len(group)

            # 获取组内图片的唯一ID样本，用于日志
            sample_ids = [p.get('unique_id', '') for p in group[:min(3, len(group))]]
            self.emit_log(f"处理尺寸组 ({width_cm:.1f}, {height_cm:.1f})，包含 {group_size} 个图片，样本ID: {', '.join(sample_ids)}")

            # 检查是否满足B类条件
            is_b_class, need_rotation, multiplier = self.check_b_class_condition(
                width_cm, height_cm, b_class_canvas_width, class_b_error_range)

            if is_b_class:
                # 计算约数边长度和名称
                divisor_edge_length = height_cm if need_rotation else width_cm
                divisor_edge_type = 'height' if need_rotation else 'width'
                divisor_edge_name = 'height' if need_rotation else 'width'

                # 计算约数边乘以倍数的结果
                divisor_edge_multiplied = divisor_edge_length * multiplier

                # 计算与画布宽度的差值百分比
                edge_diff_percent = abs(divisor_edge_multiplied - b_class_canvas_width) / b_class_canvas_width * 100

                # 增加额外验证：确保约数边乘以倍数不超过画布宽度
                if divisor_edge_multiplied > b_class_canvas_width:
                    self.emit_log(f"警告: 尺寸组 ({width_cm:.1f}, {height_cm:.1f}) 的约数边乘以倍数 ({divisor_edge_multiplied:.2f}cm) 超过画布宽度 ({b_class_canvas_width:.2f}cm)，不满足B类条件")
                    remaining_patterns.extend(group)
                    continue

                # 增加额外验证：确保差值百分比在允许范围内
                if edge_diff_percent > class_b_error_range * 100:
                    self.emit_log(f"警告: 尺寸组 ({width_cm:.1f}, {height_cm:.1f}) 的约数边乘以倍数与画布宽度的差值百分比 ({edge_diff_percent:.2f}%) 超过允许范围 ({class_b_error_range*100:.2f}%)，不满足B类条件")
                    remaining_patterns.extend(group)
                    continue

                # 增加额外验证：检查约数关系是否合理
                # 例如，对于200宽幅，180不是有效的约数边（因为200/180不是整数）
                if divisor_edge_multiplied != b_class_canvas_width:  # 如果不是精确匹配
                    # 检查是否是合理的约数关系
                    ratio = b_class_canvas_width / divisor_edge_length
                    if abs(ratio - round(ratio)) > 0.01:  # 如果比率不接近整数
                        self.emit_log(f"警告: 尺寸组 ({width_cm:.1f}, {height_cm:.1f}) 的约数边 ({divisor_edge_length:.2f}cm) 与画布宽度 ({b_class_canvas_width:.2f}cm) 不构成合理的约数关系，比率为 {ratio:.2f}，不满足B类条件")
                        remaining_patterns.extend(group)
                        continue

                # 增加额外验证：确保倍数是整数
                if multiplier != round(multiplier):
                    self.emit_log(f"警告: 尺寸组 ({width_cm:.1f}, {height_cm:.1f}) 的倍数 ({multiplier}) 不是整数，不满足B类条件")
                    remaining_patterns.extend(group)
                    continue

                # 增加额外验证：确保倍数至少为2
                if multiplier < 2:
                    self.emit_log(f"警告: 尺寸组 ({width_cm:.1f}, {height_cm:.1f}) 的倍数 ({multiplier}) 小于2，不满足B类条件")
                    remaining_patterns.extend(group)
                    continue

                self.emit_log(f"尺寸组 ({width_cm:.1f}, {height_cm:.1f}) 满足B类条件:")
                self.emit_log(f"  - 约数边: {divisor_edge_name}({divisor_edge_length:.2f}cm)")
                self.emit_log(f"  - 倍数: {multiplier}")
                self.emit_log(f"  - 乘积: {divisor_edge_multiplied:.2f}cm")
                self.emit_log(f"  - 与画布宽度({b_class_canvas_width:.2f}cm)的差值百分比: {edge_diff_percent:.2f}%")
                self.emit_log(f"  - 需要旋转: {need_rotation}")

                # 检查组内图片数量是否足够形成至少一个完整组
                if group_size >= multiplier:
                    # 计算可以形成的完整组数
                    complete_groups = group_size // multiplier
                    remaining_count = group_size % multiplier

                    self.emit_log(f"尺寸组 ({width_cm:.1f}, {height_cm:.1f}) 数量足够，可以形成 {complete_groups} 个完整组，每组 {multiplier} 个图片，剩余 {remaining_count} 个图片")

                    # 将图片分为B类和剩余
                    for i, pattern in enumerate(group):
                        unique_id = pattern.get('unique_id', '')
                        if i < complete_groups * multiplier:
                            # B类图片
                            pattern['image_class'] = 'B'
                            pattern['need_rotation'] = need_rotation
                            pattern['multiplier'] = multiplier
                            pattern['divisor_edge_type'] = divisor_edge_type
                            pattern['divisor_edge_name'] = divisor_edge_name
                            pattern['divisor_edge_length'] = divisor_edge_length
                            pattern['is_confirmed_b'] = True
                            pattern['b_group_id'] = f"{divisor_edge_name}_{divisor_edge_length:.1f}_{multiplier}"
                            pattern['b_group_size'] = multiplier
                            pattern['b_group_index'] = i // multiplier
                            pattern['b_in_group_index'] = i % multiplier
                            class_b_patterns.append(pattern)
                            self.emit_log(f"图片 {unique_id} 分类为B类，约数边: {divisor_edge_name}，倍数: {multiplier}")
                        else:
                            # 剩余图片
                            self.emit_log(f"图片 {unique_id} 为剩余图片，将归类为C类")
                            remaining_patterns.append(pattern)
                else:
                    # 数量不足，全部添加到剩余列表
                    self.emit_log(f"尺寸组 ({width_cm:.1f}, {height_cm:.1f}) 数量不足，需要 {multiplier} 个图片，实际只有 {group_size} 个，全部归类为C类")
                    remaining_patterns.extend(group)
            else:
                # 不满足B类条件，全部添加到剩余列表
                self.emit_log(f"尺寸组 ({width_cm:.1f}, {height_cm:.1f}) 不满足B类条件，全部归类为C类")
                remaining_patterns.extend(group)

        self.emit_log(f"B类图片提取完成，共 {len(class_b_patterns)} 个B类图片，{len(remaining_patterns)} 个剩余图片")

        # 记录B类图片的尺寸分布
        b_class_size_groups = {}
        for pattern in class_b_patterns:
            width_cm = pattern['width_cm']
            height_cm = pattern['height_cm']
            size_key = (round(width_cm * 10) / 10, round(height_cm * 10) / 10)
            if size_key not in b_class_size_groups:
                b_class_size_groups[size_key] = []
            b_class_size_groups[size_key].append(pattern)

        self.emit_log(f"B类图片尺寸分布:")
        for size_key, group in b_class_size_groups.items():
            width_cm, height_cm = size_key
            self.emit_log(f"  - 尺寸 ({width_cm:.1f}, {height_cm:.1f}): {len(group)} 个图片")

        return class_b_patterns, remaining_patterns

    def classify_remaining_as_c(self, pattern_items: List[Dict[str, Any]], canvas_width_cm: float = None) -> List[Dict[str, Any]]:
        """
        将剩余图片归类为C类
        使用唯一标识符"图片名_宽_高"来区分不同尺寸的同名图片

        C类图片处理规则：
        1. 普通C类图片根据需要旋转，尽可能填充画布，减少空隙
        2. C类的超宽图片（宽度超过宽幅的图片）旋转90度，竖着放置

        Args:
            pattern_items: 剩余图片列表
            canvas_width_cm: 画布宽度（厘米），用于判断超宽图片

        Returns:
            List[Dict]: C类图片列表
        """
        self.emit_log(f"第三步：将剩余的 {len(pattern_items)} 个图片归类为C类...")

        class_c_patterns = []

        for pattern in pattern_items:
            # 确保图片有唯一标识符
            if 'unique_id' not in pattern:
                width_cm = pattern['width_cm']
                height_cm = pattern['height_cm']
                pattern_name = pattern.get('pattern_name', '')
                pattern['unique_id'] = f"{pattern_name}_{width_cm:.1f}_{height_cm:.1f}"

            unique_id = pattern.get('unique_id', '')
            width_cm = pattern['width_cm']
            height_cm = pattern['height_cm']
            pattern_name = pattern.get('pattern_name', '')

            # 设置为C类
            pattern['image_class'] = 'C'

            # 检查是否为超宽图片（宽度超过画布宽度）
            if canvas_width_cm and width_cm > canvas_width_cm:
                # 检查旋转后是否适合画布
                if height_cm <= canvas_width_cm:
                    pattern['need_rotation'] = True  # 需要旋转90度
                    self.emit_log(f"图片 {pattern_name} 分类为C类(超宽图片): 宽度({width_cm:.2f}cm)超过画布宽度({canvas_width_cm:.2f}cm)，旋转90度竖着放置")
                else:
                    # 如果旋转后仍然超过画布宽度，则不设置旋转标志，后续处理中可能会进行缩放
                    pattern['need_rotation'] = False
                    self.emit_log(f"图片 {pattern_name} 分类为C类(超宽图片): 宽度({width_cm:.2f}cm)和高度({height_cm:.2f}cm)都超过画布宽度({canvas_width_cm:.2f}cm)，可能需要缩放")
            else:
                # 普通C类图片，不预设旋转标志，由排列算法决定是否旋转
                if 'need_rotation' not in pattern:
                    pattern['need_rotation'] = False
                self.emit_log(f"图片 {unique_id} 分类为C类(其他图片)")

            class_c_patterns.append(pattern)

        return class_c_patterns

    def classify_images(self, pattern_items: List[Dict[str, Any]], canvas_width_cm: float,
                        class_a_threshold: float = 0.95, class_b_error_range: float = 0.05,
                        original_canvas_width_cm: float = None) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]], List[Dict[str, Any]]]:
        """
        将图片分类为A类、B类和C类，严格按照先A类，再B类，最后C类的顺序

        重构版本：
        1. 首先从所有图片中识别并提取所有符合A类条件的图片
        2. 然后从剩余图片中识别并提取所有符合B类条件的图片
        3. 最后将剩余的所有图片归类为C类图片
        4. 使用"图片名_宽_高"作为图片版型的唯一标识符

        注意：本方法中，宽度(width_cm)始终表示长边，高度(height_cm)始终表示短边，默认图片为横向放置

        重要：此方法仅进行图片分类，不涉及画布排列逻辑
        分类仅基于图片边长与宽幅的关系，不考虑画布排列
        分类完成后，所有图片都会被标记为A/B/C类，但不会进行任何排列操作

        Args:
            pattern_items: 图片列表，每个图片是一个字典，包含width_cm、height_cm等信息
                           其中width_cm是长边，height_cm是短边
            canvas_width_cm: 画布宽度（厘米）
            class_a_threshold: A类阈值，默认为0.95（95%）
            class_b_error_range: B类误差范围，默认为0.05（5%）
            original_canvas_width_cm: 原始画布宽度（厘米），不包含水平扩展，用于B类图片分组

        Returns:
            Tuple[List[Dict], List[Dict], List[Dict]]: A类图片列表、B类图片列表和C类图片列表
        """
        self.emit_log("开始一次循环完成A/B/C类图片分类...")
        self.emit_log(f"图片总数: {len(pattern_items)}")
        self.emit_log("图片分类流程: 先A类，再B类，最后C类")

        # 确保所有图片都有唯一标识符
        for pattern in pattern_items:
            if 'unique_id' not in pattern:
                width_cm = pattern['width_cm']
                height_cm = pattern['height_cm']
                pattern_name = pattern.get('pattern_name', '')
                pattern['unique_id'] = f"{pattern_name}_{width_cm:.1f}_{height_cm:.1f}"
                self.emit_log(f"为图片添加唯一标识符: {pattern['unique_id']}")

        # 第一步：从所有图片中提取A类图片
        class_a_patterns, remaining_patterns = self.extract_class_a_patterns(
            pattern_items=pattern_items,
            canvas_width_cm=canvas_width_cm,
            class_a_threshold=class_a_threshold
        )

        self.emit_log(f"第一步完成: 提取出 {len(class_a_patterns)} 个A类图片，剩余 {len(remaining_patterns)} 个图片")

        # 第二步：从剩余图片中提取B类图片
        class_b_patterns, remaining_patterns = self.extract_class_b_patterns(
            pattern_items=remaining_patterns,
            canvas_width_cm=canvas_width_cm,
            class_b_error_range=class_b_error_range,
            original_canvas_width_cm=original_canvas_width_cm
        )

        self.emit_log(f"第二步完成: 提取出 {len(class_b_patterns)} 个B类图片，剩余 {len(remaining_patterns)} 个图片")

        # 第三步：将剩余图片归类为C类
        class_c_patterns = self.classify_remaining_as_c(remaining_patterns, canvas_width_cm)

        self.emit_log(f"第三步完成: 将剩余的 {len(class_c_patterns)} 个图片归类为C类")

        # 记录分类结果
        self.emit_log(f"图片分类完成: A类图片: {len(class_a_patterns)}个, B类图片: {len(class_b_patterns)}个, C类图片: {len(class_c_patterns)}个")

        # 返回分类结果
        return class_a_patterns, class_b_patterns, class_c_patterns

    def sort_c_class_patterns(self, class_c_patterns: List[Dict[str, Any]], canvas_width_cm: float) -> List[Dict[str, Any]]:
        """
        对C类图片进行排序，优化排列效果

        C类图片排序规则：
        1. 当宽高都小于等于宽幅时，宽度倒序
        2. 当宽大于宽幅时，用高度倒序

        Args:
            class_c_patterns: C类图片列表
            canvas_width_cm: 画布宽度（厘米）

        Returns:
            List[Dict[str, Any]]: 排序后的C类图片列表
        """
        if not class_c_patterns:
            return []

        self.emit_log(f"对 {len(class_c_patterns)} 个C类图片进行排序...")

        # 对C类图片排序
        # 当宽高都小于等于宽幅时，宽度倒序
        # 当宽大于宽幅时，用高度倒序
        for pattern in class_c_patterns:
            width_cm = pattern.get('width_cm', 0)
            height_cm = pattern.get('height_cm', 0)

            # 标记排序键
            if width_cm <= canvas_width_cm and height_cm <= canvas_width_cm:
                # 当宽高都小于等于宽幅时，使用宽度倒序
                pattern['sort_key'] = -width_cm
                self.emit_log(f"图片 {pattern.get('pattern_name', '')} 宽高都小于等于宽幅，使用宽度倒序排序")
            else:
                # 当宽大于宽幅时，使用高度倒序
                pattern['sort_key'] = -height_cm
                self.emit_log(f"图片 {pattern.get('pattern_name', '')} 宽大于宽幅，使用高度倒序排序")

        # 按排序键排序
        sorted_patterns = sorted(class_c_patterns, key=lambda p: p.get('sort_key', 0))

        self.emit_log(f"C类图片排序完成: 共 {len(sorted_patterns)} 个图片")

        return sorted_patterns

    def process_b_class_groups(self, class_b_patterns: List[Dict[str, Any]], canvas_width_cm: float,
                              original_canvas_width_cm: float = None) -> Tuple[List[List[Dict[str, Any]]], List[Dict[str, Any]]]:
        """
        处理B类图片分组，组织为二维数组结构
        使用唯一标识符"图片名_宽_高"来区分不同尺寸的同名图片

        B类图片定义：宽幅约数类图片，按相同尺寸约数倍数组的方式存在
        例如：160宽幅，5个40x130的图片，因为 40x4 = 160，所以4个相同尺寸的40x130图片为一组，归为B类
        剩余的1个40x130归到到C组

        实现二维数组结构：
        - 外层数组存储所有B类图片组
        - 内层数组存储具有相同约数关系的图片组
        - 每个内层数组必须包含完整的一组满足约数关系的图片

        注意：默认宽是长边，高是短边

        重要：此方法仅处理B类图片分组，不涉及画布排列逻辑
        分组仅基于图片边长与宽幅的约数关系，不考虑画布排列
        分组完成后，不会进行任何排列操作

        Args:
            class_b_patterns: B类图片列表
            canvas_width_cm: 画布宽度（厘米）
            original_canvas_width_cm: 原始画布宽度（厘米），不包含水平扩展，用于B类图片分组

        Returns:
            Tuple[List[List[Dict]], List[Dict]]: 处理后的B类图片二维数组和需要降级为C类的图片列表
        """
        processed_b_groups = []  # 二维数组结构
        downgraded_to_c_patterns = []

        # 如果没有B类图片，直接返回空列表
        if not class_b_patterns:
            return processed_b_groups, downgraded_to_c_patterns

        # 使用原始画布宽度进行B类图片分组
        b_class_canvas_width = original_canvas_width_cm if original_canvas_width_cm is not None else canvas_width_cm
        self.emit_log(f"处理B类图片分组，组织为二维数组结构...")
        self.emit_log(f"B类图片分组使用原始画布宽度: {b_class_canvas_width}厘米（不包含水平扩展）")

        # 确保所有图片都有唯一标识符
        for pattern in class_b_patterns:
            if 'unique_id' not in pattern:
                width_cm = pattern['width_cm']
                height_cm = pattern['height_cm']
                pattern_name = pattern.get('pattern_name', '')
                pattern['unique_id'] = f"{pattern_name}_{width_cm:.1f}_{height_cm:.1f}"

        # 按照约数关系和旋转状态分组
        b_class_groups = {}
        for pattern in class_b_patterns:
            multiplier = pattern.get('multiplier', 0)
            need_rotation = pattern.get('need_rotation', False)
            divisor_edge_type = pattern.get('divisor_edge_type', 'width')
            width_cm = pattern['width_cm']
            height_cm = pattern['height_cm']
            unique_id = pattern.get('unique_id', '')

            # 使用四舍五入到0.1厘米，避免浮点数精度问题
            rounded_width = round(width_cm * 10) / 10
            rounded_height = round(height_cm * 10) / 10

            # 分组键包含：倍数、约数边类型、宽度和高度
            group_key = f"{multiplier}_{divisor_edge_type}_{rounded_width}_{rounded_height}"

            # 将图片添加到对应的组
            if group_key not in b_class_groups:
                b_class_groups[group_key] = []

            b_class_groups[group_key].append(pattern)

            self.emit_log(f"B类图片 {unique_id} 添加到组 {group_key}，约数边: {divisor_edge_type}，倍数: {multiplier}，旋转: {need_rotation}")

        # 处理每个B类图片组，组织为二维数组结构
        group_index = 0

        # 处理每个B类图片组
        for group_key, group in b_class_groups.items():
            # 获取组中第一个图片的信息
            first_pattern = group[0]
            multiplier = first_pattern.get('multiplier', 0)
            need_rotation = first_pattern.get('need_rotation', False)
            divisor_edge_type = first_pattern.get('divisor_edge_type', 'width')
            divisor_edge_length = first_pattern.get('divisor_edge_length', 0)

            # 计算每组应该包含的图片数量 - 必须等于倍数值
            required_group_size = multiplier

            # 检查组内图片数量是否等于倍数值
            if len(group) != required_group_size:
                self.emit_log(f"警告: B类图片组 {group_key} 的图片数量 {len(group)} 不等于倍数值 {required_group_size}，可能存在问题")

            # 再次验证约数关系是否合理
            if divisor_edge_length > 0:
                divisor_edge_multiplied = divisor_edge_length * multiplier

                # 验证约数边乘以倍数不超过画布宽度
                if divisor_edge_multiplied > b_class_canvas_width:
                    self.emit_log(f"警告: B类图片组 {group_key} 的约数边乘以倍数 ({divisor_edge_multiplied:.2f}cm) 超过画布宽度 ({b_class_canvas_width:.2f}cm)，将降级为C类")
                    downgraded_to_c_patterns.extend(group)
                    continue

                # 验证约数关系是否合理
                ratio = b_class_canvas_width / divisor_edge_length
                if abs(ratio - round(ratio)) > 0.01:  # 如果比率不接近整数
                    self.emit_log(f"警告: B类图片组 {group_key} 的约数边 ({divisor_edge_length:.2f}cm) 与画布宽度 ({b_class_canvas_width:.2f}cm) 不构成合理的约数关系，比率为 {ratio:.2f}，将降级为C类")
                    downgraded_to_c_patterns.extend(group)
                    continue

                # 验证倍数是否是整数
                if multiplier != round(multiplier):
                    self.emit_log(f"警告: B类图片组 {group_key} 的倍数 ({multiplier}) 不是整数，将降级为C类")
                    downgraded_to_c_patterns.extend(group)
                    continue

                # 验证倍数是否至少为2
                if multiplier < 2:
                    self.emit_log(f"警告: B类图片组 {group_key} 的倍数 ({multiplier}) 小于2，将降级为C类")
                    downgraded_to_c_patterns.extend(group)
                    continue

                # 验证与画布宽度的差值百分比是否在允许范围内
                edge_diff_percent = abs(divisor_edge_multiplied - b_class_canvas_width) / b_class_canvas_width * 100
                if edge_diff_percent > 5:  # 使用5%作为默认误差范围
                    self.emit_log(f"警告: B类图片组 {group_key} 的约数边乘以倍数与画布宽度的差值百分比 ({edge_diff_percent:.2f}%) 超过允许范围 (5%)，将降级为C类")
                    downgraded_to_c_patterns.extend(group)
                    continue

            # 对组内图片按唯一ID排序，确保相同ID的图片不会被重复处理
            group.sort(key=lambda p: p.get('unique_id', ''))

            # 为每个图片设置组索引和组内索引
            for j, pattern in enumerate(group):
                pattern['group_index'] = group_index
                pattern['in_group_index'] = j
                pattern['group_size'] = len(group)  # 添加组大小信息
                pattern['is_last_in_row'] = (j == len(group) - 1)  # 标记是否是行中的最后一个图片
                pattern['complete_group'] = True  # 标记为完整组的一部分

                # 确保旋转状态正确
                if divisor_edge_type == 'height':
                    # 约数边是高度（短边），需要旋转90度
                    pattern['need_rotation'] = True
                else:
                    # 约数边是宽度（长边），不需要旋转
                    pattern['need_rotation'] = False

            # 标记是否为最后一组B类图片
            for pattern in group:
                pattern['is_last_b_group'] = (group_index == len(b_class_groups) - 1)

            # 添加到二维数组
            processed_b_groups.append(group)

            # 记录日志
            sample_ids = [p.get('unique_id', '') for p in group[:min(3, len(group))]]
            self.emit_log(f"B类图片组 {group_key} 添加到二维数组，包含 {len(group)} 个图片，约数边类型: {divisor_edge_type}，旋转: {need_rotation}")
            self.emit_log(f"  - 样本图片ID: {', '.join(sample_ids)}")

            # 增加组索引
            group_index += 1

        # 记录处理结果
        total_b_images = sum(len(group) for group in processed_b_groups)
        self.emit_log(f"B类图片分组处理完成: {len(processed_b_groups)} 个组，共 {total_b_images} 个图片，降级为C类的图片: {len(downgraded_to_c_patterns)} 个")

        # 按照组内图片数量降序排序（优先排列大组）
        processed_b_groups.sort(key=lambda group: len(group), reverse=True)

        return processed_b_groups, downgraded_to_c_patterns

    def sort_c_class_patterns(self, class_c_patterns: List[Dict[str, Any]], canvas_width_cm: float) -> List[Dict[str, Any]]:
        """
        极度优化版C类图片排序算法
        极度重视水平空间利用率和扁平化布局，谨慎考虑旋转，提高整体布局效率

        Args:
            class_c_patterns: C类图片列表，每个图片是一个字典，包含width_cm（长边）、height_cm（短边）等信息
            canvas_width_cm: 画布宽度（厘米）

        Returns:
            List[Dict]: 排序后的C类图片列表
        """
        if not class_c_patterns:
            return []

        self.emit_log(f"对 {len(class_c_patterns)} 个C类图片进行排序...")

        # 预处理：计算每个图片的关键指标
        for pattern in class_c_patterns:
            width_cm = pattern['width_cm']
            height_cm = pattern['height_cm']

            # 计算面积
            area = width_cm * height_cm
            pattern['area'] = area

            # 计算宽度与画布宽度的比例
            width_ratio = width_cm / canvas_width_cm if canvas_width_cm > 0 else 0
            pattern['width_ratio'] = width_ratio

            # 计算高度与画布宽度的比例（用于评估旋转后的宽度比）
            height_ratio = height_cm / canvas_width_cm if canvas_width_cm > 0 else 0
            pattern['height_ratio'] = height_ratio

            # 计算宽高比
            aspect_ratio = width_cm / height_cm if height_cm > 0 else 0
            pattern['aspect_ratio'] = aspect_ratio

            # 计算宽度组合潜力 - 评估图片在水平方向组合的潜力
            pattern['combination_potential'] = 0
            for divisor in [2, 3, 4, 5]:
                ideal_width = canvas_width_cm / divisor
                if abs(width_cm - ideal_width) / ideal_width < 0.05:  # 误差在5%以内，更严格
                    pattern['combination_potential'] = divisor
                    pattern['combination_quality'] = 1.0 - abs(width_cm - ideal_width) / ideal_width
                    break

            # 计算高度组合潜力（用于评估旋转后的组合潜力）
            pattern['height_combination_potential'] = 0
            for divisor in [2, 3, 4, 5]:
                ideal_width = canvas_width_cm / divisor
                if abs(height_cm - ideal_width) / ideal_width < 0.05:  # 误差在5%以内，更严格
                    pattern['height_combination_potential'] = divisor
                    pattern['height_combination_quality'] = 1.0 - abs(height_cm - ideal_width) / ideal_width
                    break

            # 评估旋转潜力 - 判断旋转后是否能提高水平空间利用率
            pattern['rotation_benefit'] = 0

            # 考虑多种因素评估旋转收益
            # 1. 如果高度（短边）接近画布宽度的整数分数，且宽度（长边）不是
            if (pattern.get('height_combination_potential', 0) > 0 and
                pattern.get('combination_potential', 0) == 0):
                # 旋转可能有益，计算收益
                pattern['rotation_benefit'] = pattern.get('height_combination_quality', 0) * 100

            # 2. 如果高度（短边）比宽度（长边）更接近画布宽度
            elif (height_ratio <= 1.0 and width_ratio > 1.0) or (
                  height_ratio <= 1.0 and width_ratio <= 1.0 and
                  abs(1.0 - height_ratio) < abs(1.0 - width_ratio) * 0.7):  # 高度明显更接近画布宽度
                # 旋转可能有益，计算收益
                pattern['rotation_benefit'] = (1.0 - abs(1.0 - height_ratio)) * 100

            # 3. 考虑宽高比 - 如果旋转后宽高比更接近1:1，可能有益于布局
            if aspect_ratio > 2.0 or aspect_ratio < 0.5:
                # 宽高比过大或过小，旋转可能有益
                rotated_aspect_ratio = 1.0 / aspect_ratio if aspect_ratio > 0 else 0
                if abs(1.0 - rotated_aspect_ratio) < abs(1.0 - aspect_ratio):
                    pattern['rotation_benefit'] += 20  # 增加旋转收益

        # 优化排序策略 - 综合考虑多种因素
        def sort_key(p):
            # 1. 宽度（长边）完美匹配画布宽度的图片最优先
            width_ratio = p.get('width_ratio', 0)
            if 0.98 <= width_ratio <= 1.0:
                # 几乎完美匹配画布宽度，给予极高优先级
                return (-1000000, 0, 0, 0, 0)

            # 2. 组合潜力评分 - 极度重视水平空间组合能力
            combination_score = 0

            # 基础组合潜力
            combination_potential = p.get('combination_potential', 0)
            if combination_potential > 0:
                # 有组合潜力的图片优先
                combination_quality = p.get('combination_quality', 0)
                # 2和3的分母最优先（最容易组合）
                if combination_potential in [2, 3]:
                    combination_score = -10000 * combination_quality
                # 4和5的分母次优先
                elif combination_potential in [4, 5]:
                    combination_score = -8000 * combination_quality
            else:
                # 无组合潜力，检查是否有旋转收益
                rotation_benefit = p.get('rotation_benefit', 0)
                if rotation_benefit > 50:  # 只有旋转收益显著时才考虑
                    height_combination_potential = p.get('height_combination_potential', 0)
                    if height_combination_potential in [2, 3]:
                        # 旋转后有高组合潜力
                        combination_score = -5000 * p.get('height_combination_quality', 0)
                    elif height_combination_potential in [4, 5]:
                        # 旋转后有中等组合潜力
                        combination_score = -4000 * p.get('height_combination_quality', 0)

            # 3. 宽度（长边）比评分 - 优先选择宽度接近但不超过画布宽度的图片
            width_score = 0
            if width_ratio <= 1.0:
                # 宽度不超过画布宽度，优先选择接近画布宽度的图片
                width_score = abs(1.0 - width_ratio) * 1000
            else:
                # 宽度超过画布宽度，大幅降低优先级
                width_score = (width_ratio - 1.0) * 10000

            # 4. 面积评分 - 大面积图片优先，但权重较低
            area_score = -p.get('area', 0) * 0.005

            # 5. 宽高比评分 - 优先选择宽高比接近1:1的图片，有利于紧凑排列
            aspect_ratio = p.get('aspect_ratio', 0)
            aspect_score = abs(1.0 - aspect_ratio) * 500 if aspect_ratio > 0 else 1000

            # 6. 旋转收益评分 - 只有在旋转收益显著时才考虑
            rotation_score = 0
            rotation_benefit = p.get('rotation_benefit', 0)
            if rotation_benefit > 70:  # 旋转收益非常高
                rotation_score = -rotation_benefit

            return (combination_score, width_score, area_score, aspect_score, rotation_score)

        # 应用优化排序
        sorted_c_patterns = sorted(class_c_patterns, key=sort_key)

        # 记录排序结果
        for i, pattern in enumerate(sorted_c_patterns[:10]):  # 只记录前10个，避免日志过多
            # 计算更详细的排序信息
            width_ratio = pattern.get('width_ratio', 0)
            height_ratio = pattern.get('height_ratio', 0)
            rotation_benefit = pattern.get('rotation_benefit', 0)
            aspect_ratio = pattern.get('aspect_ratio', 0)
            combination_info = f"组合潜力: {pattern.get('combination_potential', 0)}"
            if pattern.get('height_combination_potential', 0) > 0:
                combination_info += f", 旋转后组合潜力: {pattern.get('height_combination_potential', 0)}"

            self.emit_log(f"C类图片排序 #{i+1}: {pattern['pattern_name']} - "
                         f"宽度(长边): {pattern['width_cm']:.2f}cm ({width_ratio:.2f}), "
                         f"高度(短边): {pattern['height_cm']:.2f}cm ({height_ratio:.2f}), "
                         f"宽高比: {aspect_ratio:.2f}, "
                         f"{combination_info}, 旋转收益: {rotation_benefit:.1f}")

        return sorted_c_patterns
