#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
图片索引器DuckDB实现模块

提供基于DuckDB的图片索引和检索功能：
1. 使用DuckDB作为后端存储
2. 统一使用UTF-8编码
3. 优化Windows路径处理
4. 多级索引策略
5. 规范化文件名处理
6. 支持进度显示和批量处理
7. 多线程并行处理，提高索引速度
8. 异步IO操作，减少等待时间
"""

import os
import sys
import logging
import duckdb
import time
import concurrent.futures
import threading
import queue
from datetime import datetime
from PyQt6.QtCore import QObject, pyqtSignal
from typing import Optional, List, Dict, Any, Tuple

# 导入自定义模块
from utils.parallel_manager import ParallelManager

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
log = logging.getLogger("ImageIndexerDuckDB")

class ImageIndexerDuckDB(QObject):
    """图片索引器，用于为图片建立索引并快速检索

    优化特性：
    1. 使用DuckDB作为后端存储
    2. 统一使用UTF-8编码
    3. 优化Windows路径处理
    4. 多级索引策略
    5. 规范化文件名处理
    6. 支持进度显示和批量处理
    """

    # 进度信号
    progress_signal = pyqtSignal(int)  # 进度值 (0-100)
    status_signal = pyqtSignal(str)    # 状态信息
    error_signal = pyqtSignal(str)     # 错误信息

    # 批处理相关常量
    BATCH_SIZE = 100  # 每批处理的文件数
    COMMIT_INTERVAL = 1000  # 每多少条记录提交一次事务

    def __init__(self, fast_mode=False):
        """初始化图片索引器

        Args:
            fast_mode: 是否使用快速模式，跳过图片尺寸和EXIF数据的获取
        """
        super().__init__()
        self.library_path = None
        self._indexed = False
        self._supported_extensions = ('.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif')
        self.db = None
        self._stop_requested = False
        self._lock = threading.Lock()
        self.fast_mode = fast_mode

        # 创建并行管理器
        self.parallel_manager = ParallelManager()

        # 批处理相关参数
        self.batch_queue = queue.Queue()
        self.result_queue = queue.Queue()
        self.worker_threads = []
        self.max_workers = min(32, os.cpu_count() * 2) if os.cpu_count() else 8

    def stop_indexing(self):
        """请求停止索引过程"""
        self._stop_requested = True
        self.status_signal.emit("正在停止索引...")

    def _init_db(self):
        """初始化DuckDB数据库"""
        try:
            if not self.library_path:
                self.error_signal.emit("图库路径未设置")
                return False

            # 规范化路径并创建.index目录
            index_dir = os.path.join(self.library_path, '.index')
            os.makedirs(index_dir, exist_ok=True)

            # 设置数据库文件路径
            db_path = os.path.join(index_dir, 'image_index.db')

            # 关闭已存在的连接
            if self.db:
                try:
                    self.db.close()
                except:
                    pass

            # 连接数据库
            self.db = duckdb.connect(db_path)

            # 创建图片信息表
            self.db.execute("""
                CREATE SEQUENCE IF NOT EXISTS image_files_id_seq;
                CREATE TABLE IF NOT EXISTS image_files (
                    id INTEGER PRIMARY KEY DEFAULT nextval('image_files_id_seq'),
                    filename VARCHAR NOT NULL,
                    name_without_ext VARCHAR NOT NULL,
                    extension VARCHAR NOT NULL,
                    relative_path VARCHAR NOT NULL,
                    full_path VARCHAR NOT NULL,
                    has_chinese BOOLEAN NOT NULL,
                    normalized_filename VARCHAR NOT NULL,
                    normalized_name VARCHAR NOT NULL,
                    file_size BIGINT NOT NULL,
                    modified_time TIMESTAMP NOT NULL,
                    created_time TIMESTAMP NOT NULL,
                    indexed_time TIMESTAMP NOT NULL,
                    width INTEGER DEFAULT 0,
                    height INTEGER DEFAULT 0,
                    has_exif BOOLEAN DEFAULT FALSE,
                    orientation INTEGER DEFAULT 1,
                    UNIQUE(relative_path)
                )
            """)

            # 检查是否需要添加新列
            try:
                # 检查是否存在width列
                result = self.db.execute("SELECT width FROM image_files LIMIT 1").fetchone()
            except Exception:
                # 如果不存在，添加新列
                self.db.execute("ALTER TABLE image_files ADD COLUMN width INTEGER DEFAULT 0")
                self.db.execute("ALTER TABLE image_files ADD COLUMN height INTEGER DEFAULT 0")
                self.db.execute("ALTER TABLE image_files ADD COLUMN has_exif BOOLEAN DEFAULT FALSE")
                self.db.execute("ALTER TABLE image_files ADD COLUMN orientation INTEGER DEFAULT 1")
                self.status_signal.emit("已添加图像维度和EXIF数据列")

            # 创建索引
            self.db.execute("CREATE INDEX IF NOT EXISTS idx_filename ON image_files(filename)")
            self.db.execute("CREATE INDEX IF NOT EXISTS idx_name_without_ext ON image_files(name_without_ext)")
            self.db.execute("CREATE INDEX IF NOT EXISTS idx_normalized_name ON image_files(normalized_name)")
            self.db.execute("CREATE INDEX IF NOT EXISTS idx_relative_path ON image_files(relative_path)")
            self.db.execute("CREATE INDEX IF NOT EXISTS idx_full_path ON image_files(full_path)")

            # 提交更改
            self.db.commit()

            self.status_signal.emit(f"成功初始化DuckDB数据库: {db_path}")
            return True

        except Exception as e:
            self.error_signal.emit(f"初始化数据库失败: {str(e)}")
            if self.db:
                try:
                    self.db.close()
                except:
                    pass
                self.db = None
            return False

    def normalize_path(self, path):
        """规范化路径，统一使用正斜杠，处理编码问题

        优化点：
        1. 更好地处理Windows路径分隔符
        2. 处理相对路径和绝对路径
        3. 处理中文路径的编码问题
        4. 移除路径中的非法字符
        """
        if not path:
            return path

        try:
            # 处理编码问题
            if isinstance(path, bytes):
                try:
                    # 尝试UTF-8解码
                    path = path.decode('utf-8')
                except UnicodeDecodeError:
                    # 如果UTF-8解码失败，尝试系统默认编码
                    path = path.decode(sys.getfilesystemencoding())
            elif not isinstance(path, str):
                path = str(path)

            # 统一使用正斜杠，处理Windows路径
            path = path.replace('\\', '/')

            # 处理连续的斜杠
            while '//' in path:
                path = path.replace('//', '/')

            # 移除路径首尾的空白字符
            path = path.strip()

            # 移除路径末尾的斜杠（除非是根路径）
            if path.endswith('/') and len(path) > 1:
                path = path.rstrip('/')

            return path

        except Exception as e:
            log.error(f"路径规范化失败: {str(e)}")
            # 返回原始路径而不是None，避免完全失败
            return str(path) if path else None

    def normalize_filename(self, filename):
        """规范化文件名，处理大小写和特殊字符

        优化点：
        1. 更好地处理中文文件名
        2. 处理特殊字符和非法字符
        3. 统一编码和格式
        4. 保留原始文件名信息用于调试
        """
        if not filename:
            return filename

        try:
            # 确保文件名是字符串类型
            if not isinstance(filename, str):
                filename = str(filename)

            # 处理编码问题
            try:
                # 确保是UTF-8编码
                if isinstance(filename, bytes):
                    filename = filename.decode('utf-8')
            except UnicodeDecodeError:
                # 如果UTF-8解码失败，尝试系统默认编码
                if isinstance(filename, bytes):
                    filename = filename.decode(sys.getfilesystemencoding())

            # 保存原始文件名（用于日志和调试）
            original_filename = filename

            # 记录原始文件名（用于调试）
            log.debug(f"规范化文件名前: '{original_filename}'")

            # 转换为小写并去除首尾空格
            filename = filename.lower().strip()

            # 移除不必要的空格和控制字符
            filename = ' '.join(filename.split())

            # 移除Windows文件系统不允许的字符（< > : " / \ | ? *）
            for char in '<>:"\/|?*':
                filename = filename.replace(char, '')

            # 如果规范化后文件名为空，使用原始文件名的哈希值
            if not filename:
                import hashlib
                filename = hashlib.md5(original_filename.encode('utf-8')).hexdigest()
                log.warning(f"文件名规范化后为空，使用哈希值代替: {original_filename} -> {filename}")

            # 记录规范化后的文件名（用于调试）
            log.debug(f"规范化文件名后: '{filename}'")

            # 对于中文文件名，保留原始文件名作为备用索引
            has_chinese = any('\u4e00' <= char <= '\u9fff' for char in original_filename)
            if has_chinese:
                # 返回规范化后的文件名，但在索引时会同时使用原始文件名
                log.debug(f"检测到中文文件名: '{original_filename}'")
                return filename, original_filename.lower().strip()

            return filename

        except Exception as e:
            log.error(f"文件名规范化失败: {str(e)}")
            # 返回原始文件名而不是None，避免完全失败
            return str(filename) if filename else None

    def set_library_path(self, path):
        """设置图片库路径"""
        self.library_path = self.normalize_path(path)
        if self.library_path:
            # 初始化数据库
            if self._init_db():
                self._indexed = True
                log.info(f"成功设置图库路径: {self.library_path}")
                return True
        return False

    def _get_file_info(self, file_path, fast_mode=None):
        """获取文件的详细信息，包括图像维度和EXIF数据

        Args:
            file_path: 文件的完整路径
            fast_mode: 是否使用快速模式，跳过图片尺寸和EXIF数据的获取

        Returns:
            dict: 文件信息字典，如果出错则返回None
        """
        try:
            # 获取文件基本信息
            stat = os.stat(file_path)
            filename = os.path.basename(file_path)
            name_without_ext = os.path.splitext(filename)[0]
            ext = os.path.splitext(filename)[1].lower()

            # 检查文件扩展名
            if ext not in self._supported_extensions:
                return None

            # 检测是否包含中文字符
            has_chinese = any('\u4e00' <= char <= '\u9fff' for char in filename)

            # 规范化文件名
            norm_filename = self.normalize_filename(filename)
            if isinstance(norm_filename, tuple):
                norm_filename = norm_filename[0]

            norm_name = self.normalize_filename(name_without_ext)
            if isinstance(norm_name, tuple):
                norm_name = norm_name[0]

            # 获取图像维度和EXIF数据
            width, height, has_exif, orientation = self._get_image_dimensions(file_path, fast_mode)

            # 构建文件信息
            file_info = {
                'filename': filename,
                'name_without_ext': name_without_ext,
                'extension': ext,
                'has_chinese': has_chinese,
                'normalized_filename': norm_filename,
                'normalized_name': norm_name,
                'file_size': stat.st_size,
                'modified_time': datetime.fromtimestamp(stat.st_mtime),
                'created_time': datetime.fromtimestamp(stat.st_mtime),  # 使用修改时间代替创建时间，避免弃用警告
                'indexed_time': datetime.now(),
                'width': width,
                'height': height,
                'has_exif': has_exif,
                'orientation': orientation
            }

            return file_info

        except Exception as e:
            log.error(f"获取文件信息失败 {file_path}: {str(e)}")
            return None

    def _get_image_dimensions(self, file_path, fast_mode=None):
        """获取图像维度和EXIF数据

        Args:
            file_path: 图像文件路径
            fast_mode: 是否使用快速模式，跳过图片尺寸和EXIF数据的获取

        Returns:
            Tuple[int, int, bool, int]: (宽度, 高度, 是否有EXIF数据, 方向)
        """
        # 如果未指定fast_mode，使用类的fast_mode属性
        if fast_mode is None:
            fast_mode = self.fast_mode

        # 如果是快速模式，直接返回默认值
        if fast_mode:
            return 0, 0, False, 1

        try:
            # 默认值
            width = 0
            height = 0
            has_exif = False
            orientation = 1  # 1表示正常方向

            # 尝试使用PIL获取图像信息
            try:
                from PIL import Image, ExifTags, ImageFile

                # 允许加载截断的图片
                ImageFile.LOAD_TRUNCATED_IMAGES = True

                # 打开图像但不完全加载
                with Image.open(file_path) as img:
                    # 只加载图片头部信息
                    img.load(0)

                    # 获取图像尺寸
                    width, height = img.size

                    # 尝试获取EXIF数据，但只在需要时才读取
                    try:
                        # 检查是否有EXIF数据
                        if hasattr(img, '_getexif') and callable(img._getexif):
                            exif = img._getexif()
                            if exif:
                                has_exif = True

                                # 获取方向信息
                                orientation_key = None
                                for key, value in ExifTags.TAGS.items():
                                    if value == 'Orientation':
                                        orientation_key = key
                                        break

                                if orientation_key and orientation_key in exif:
                                    orientation = exif[orientation_key]
                    except Exception as exif_error:
                        log.debug(f"获取EXIF数据失败: {str(exif_error)}")
            except ImportError:
                log.warning("PIL库未安装，无法获取图像尺寸和EXIF数据")
            except Exception as pil_error:
                log.debug(f"使用PIL获取图像信息失败: {str(pil_error)}")

            return width, height, has_exif, orientation

        except Exception as e:
            log.error(f"获取图像维度失败: {str(e)}")
            return 0, 0, False, 1

    def index_image(self, rel_path, fast_mode=None):
        """索引单个图片

        Args:
            rel_path: 相对于图片库的路径
            fast_mode: 是否使用快速模式，跳过图片尺寸和EXIF数据的获取

        Returns:
            bool: 是否成功索引
        """
        # 如果未指定fast_mode，使用类的fast_mode属性
        if fast_mode is None:
            fast_mode = self.fast_mode

        if not self.library_path or not self.db:
            return False

        try:
            # 规范化路径
            rel_path = self.normalize_path(rel_path)
            full_path = os.path.join(self.library_path, rel_path)

            if not os.path.exists(full_path):
                return False

            # 获取文件信息
            file_info = self._get_file_info(full_path, fast_mode)
            if not file_info:
                return False

            # 添加路径信息
            file_info['relative_path'] = rel_path
            file_info['full_path'] = full_path

            # 开始事务
            self.db.begin()

            try:
                # 检查是否已存在相同路径的记录
                result = self.db.execute("""
                    SELECT id FROM image_files WHERE relative_path = ?
                """, [rel_path]).fetchone()

                if result:
                    # 更新现有记录
                    self.db.execute("""
                        UPDATE image_files SET
                            filename = ?,
                            name_without_ext = ?,
                            extension = ?,
                            full_path = ?,
                            has_chinese = ?,
                            normalized_filename = ?,
                            normalized_name = ?,
                            file_size = ?,
                            modified_time = ?,
                            created_time = ?,
                            indexed_time = ?,
                            width = ?,
                            height = ?,
                            has_exif = ?,
                            orientation = ?
                        WHERE relative_path = ?
                    """, [
                        file_info['filename'],
                        file_info['name_without_ext'],
                        file_info['extension'],
                        file_info['full_path'],
                        file_info['has_chinese'],
                        file_info['normalized_filename'],
                        file_info['normalized_name'],
                        file_info['file_size'],
                        file_info['modified_time'],
                        file_info['created_time'],
                        file_info['indexed_time'],
                        file_info.get('width', 0),
                        file_info.get('height', 0),
                        file_info.get('has_exif', False),
                        file_info.get('orientation', 1),
                        rel_path
                    ])
                else:
                    # 插入新记录
                    self.db.execute("""
                        INSERT INTO image_files (
                            filename, name_without_ext, extension, relative_path, full_path,
                            has_chinese, normalized_filename, normalized_name,
                            file_size, modified_time, created_time, indexed_time,
                            width, height, has_exif, orientation
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, [
                        file_info['filename'],
                        file_info['name_without_ext'],
                        file_info['extension'],
                        file_info['relative_path'],
                        file_info['full_path'],
                        file_info['has_chinese'],
                        file_info['normalized_filename'],
                        file_info['normalized_name'],
                        file_info['file_size'],
                        file_info['modified_time'],
                        file_info['created_time'],
                        file_info['indexed_time'],
                        file_info.get('width', 0),
                        file_info.get('height', 0),
                        file_info.get('has_exif', False),
                        file_info.get('orientation', 1)
                    ])

                # 提交事务
                self.db.commit()
                return True

            except Exception as e:
                # 回滚事务
                self.db.rollback()
                raise e

        except Exception as e:
            log.error(f"索引图片失败 {rel_path}: {str(e)}")
            return False

    def scan_library(self, library_path, fast_mode=None):
        """扫描图库目录，建立图案索引

        使用多线程并行处理，显著提高索引速度

        Args:
            library_path: 图库目录路径
            fast_mode: 是否使用快速模式，跳过图片尺寸和EXIF数据的获取

        Returns:
            (成功标志, 消息, 图片数量)
        """
        # 如果未指定fast_mode，使用类的fast_mode属性
        if fast_mode is None:
            fast_mode = self.fast_mode

        try:
            # 重置停止标志
            self._stop_requested = False

            # 设置图库路径并初始化数据库
            self.set_library_path(library_path)
            if not self.db:
                return False, "初始化数据库失败", 0

            # 清空现有数据
            self.db.execute("DELETE FROM image_files")
            self.db.commit()

            # 收集所有图片文件
            self.status_signal.emit(f"正在扫描图库: {library_path}")

            image_files = self._collect_image_files(library_path)

            total_files = len(image_files)
            if total_files == 0:
                self.status_signal.emit("未找到支持的图片文件")
                return False, "未找到支持的图片文件", 0

            # 开始索引
            mode_str = "快速模式" if fast_mode else "完整模式"
            self.status_signal.emit(f"开始并行索引 {total_files} 个图片文件（{mode_str}）...")

            # 确定最佳线程数
            optimal_workers = self.parallel_manager.get_optimal_workers(ParallelManager.TASK_TYPE_IO)
            workers_count = min(optimal_workers, self.max_workers)

            # 计算每个批次的大小
            batch_size = max(100, min(1000, total_files // (workers_count * 4)))

            self.status_signal.emit(f"使用 {workers_count} 个工作线程，批次大小: {batch_size}")

            # 初始化计数器
            success_count = 0
            error_count = 0
            processed_count = 0

            # 创建线程池
            with concurrent.futures.ThreadPoolExecutor(max_workers=workers_count) as executor:
                # 分批处理文件
                batches = [image_files[i:i+batch_size] for i in range(0, len(image_files), batch_size)]
                batch_count = len(batches)

                self.status_signal.emit(f"将 {total_files} 个文件分为 {batch_count} 个批次进行处理")

                # 提交所有批次任务
                futures = []
                for i, batch in enumerate(batches):
                    future = executor.submit(self._process_file_batch, batch, library_path, i+1, batch_count, fast_mode)
                    futures.append(future)

                # 处理结果
                for future in concurrent.futures.as_completed(futures):
                    if self._stop_requested:
                        for f in futures:
                            f.cancel()
                        self.db.commit()
                        message = f"用户取消索引，已处理 {processed_count}/{total_files} 个文件"
                        self.status_signal.emit(message)
                        return False, "用户取消索引", success_count

                    try:
                        batch_success, batch_error = future.result()
                        success_count += batch_success
                        error_count += batch_error
                        processed_count = success_count + error_count

                        # 更新进度
                        progress = int(processed_count / total_files * 100)
                        self.progress_signal.emit(progress)

                        # 定期提交事务
                        if processed_count % self.COMMIT_INTERVAL == 0:
                            with self._lock:
                                self.db.commit()
                            self.status_signal.emit(f"已处理 {processed_count}/{total_files} 个文件...")

                    except Exception as e:
                        error_count += batch_size  # 估计值
                        self.error_signal.emit(f"批次处理失败: {str(e)}")

            # 最终提交
            with self._lock:
                self.db.commit()

            if success_count > 0:
                self._indexed = True
                message = (f"索引完成: 成功 {success_count} 个，失败 {error_count} 个")
                self.status_signal.emit(message)

                return True, message, success_count
            else:
                message = "索引失败: 没有成功处理任何文件"
                self.status_signal.emit(message)
                return False, message, 0

        except Exception as e:
            self.error_signal.emit(f"扫描图库失败: {str(e)}")
            return False, f"扫描图库失败: {str(e)}", 0

    def _collect_image_files(self, library_path):
        """收集图库中的所有图片文件

        Args:
            library_path: 图库路径

        Returns:
            List[str]: 相对路径列表
        """
        image_files = []

        for root, _, files in os.walk(library_path):
            if self._stop_requested:
                break

            for file in files:
                if file.lower().endswith(self._supported_extensions):
                    rel_path = os.path.relpath(os.path.join(root, file), library_path)
                    image_files.append(rel_path)

        return image_files

    def _process_file_batch(self, file_batch, library_path, batch_index, total_batches, fast_mode=None):
        """处理一批文件

        Args:
            file_batch: 文件相对路径列表
            library_path: 图库路径
            batch_index: 批次索引
            total_batches: 总批次数
            fast_mode: 是否使用快速模式，跳过图片尺寸和EXIF数据的获取

        Returns:
            Tuple[int, int]: (成功数, 失败数)
        """
        # 如果未指定fast_mode，使用类的fast_mode属性
        if fast_mode is None:
            fast_mode = self.fast_mode

        success_count = 0
        error_count = 0
        batch_data = []

        try:
            for rel_path in file_batch:
                if self._stop_requested:
                    break

                try:
                    # 获取文件信息
                    full_path = os.path.join(library_path, rel_path)
                    file_info = self._get_file_info(full_path, fast_mode)

                    if file_info:
                        # 添加路径信息
                        file_info['relative_path'] = rel_path
                        file_info['full_path'] = full_path
                        batch_data.append(file_info)
                        success_count += 1
                    else:
                        error_count += 1

                except Exception as e:
                    error_count += 1
                    log.error(f"处理文件失败 {rel_path}: {str(e)}")

            # 批量插入数据
            if batch_data:
                with self._lock:
                    self._batch_insert(batch_data)

            mode_str = "快速模式" if fast_mode else "完整模式"
            log.info(f"批次 {batch_index}/{total_batches} 处理完成({mode_str}): 成功 {success_count}, 失败 {error_count}")
            return success_count, error_count

        except Exception as e:
            log.error(f"批次 {batch_index}/{total_batches} 处理失败: {str(e)}")
            return success_count, error_count + (len(file_batch) - success_count)

    def validate_library(self):
        """验证图库是否已经建立索引"""
        if not self.db or not self._indexed:
            return False

        try:
            # 检查是否有图片记录
            result = self.db.execute("SELECT COUNT(*) FROM image_files").fetchone()
            return result[0] > 0
        except Exception as e:
            log.error(f"验证图库索引失败: {str(e)}")
            return False

    def is_indexed(self, library_path):
        """检查指定图库路径是否已经建立索引

        Args:
            library_path: 图库路径

        Returns:
            bool: 是否已建立索引
        """
        try:
            # 检查索引数据库是否存在
            index_dir = os.path.join(library_path, '.index')
            db_path = os.path.join(index_dir, 'image_index.db')

            if not os.path.exists(db_path):
                return False

            # 如果当前连接的是别的库，先关闭连接
            if self.library_path != library_path and self.db:
                try:
                    self.db.close()
                    self.db = None
                except:
                    pass

            # 如果没有连接，尝试连接数据库
            if not self.db:
                self.set_library_path(library_path)

            # 验证索引
            return self.validate_library()

        except Exception as e:
            log.error(f"检查图库索引状态失败: {str(e)}")
            return False

    def find_image(self, pattern_name: str, exact_match: bool = True) -> Optional[str]:
        """查找指定图案号的图片

        Args:
            pattern_name: 图案号或文件名
            exact_match: 是否进行精确匹配

        Returns:
            图片完整路径，如果找不到则返回None
        """
        if not self.db or not pattern_name:
            return None

        try:
            # 检查数据库连接状态
            if not hasattr(self.db, 'execute'):
                log.error("数据库连接已失效")
                return None

            # 规范化搜索词
            search_term = self.normalize_filename(pattern_name)
            if isinstance(search_term, tuple):
                search_term, _ = search_term  # 忽略第二个返回值

            if exact_match:
                # 精确匹配
                result = self.db.execute("""
                    SELECT full_path FROM image_files
                    WHERE name_without_ext = ?
                       OR normalized_name = ?
                       OR filename = ?
                       OR normalized_filename = ?
                    LIMIT 1
                """, [pattern_name, search_term, pattern_name, search_term]).fetchone()
            else:
                # 模糊匹配
                pattern_name = f"%{pattern_name}%"
                search_term = f"%{search_term}%"
                result = self.db.execute("""
                    SELECT full_path FROM image_files
                    WHERE name_without_ext LIKE ?
                       OR normalized_name LIKE ?
                       OR filename LIKE ?
                       OR normalized_filename LIKE ?
                    LIMIT 1
                """, [pattern_name, search_term, pattern_name, search_term]).fetchone()

            if result:
                full_path = result[0]
                if os.path.exists(full_path):
                    log.info(f"找到图片: {full_path}")
                    return full_path

            log.warning(f"未找到图片: {pattern_name}")
            return None

        except Exception as e:
            # 尝试重新连接数据库
            if "database connection" in str(e).lower() or "closed" in str(e).lower():
                log.warning(f"数据库连接问题，尝试重新连接: {str(e)}")
                try:
                    self._reconnect_database()
                    # 重试查找
                    return self.find_image(pattern_name, exact_match)
                except Exception as retry_e:
                    log.error(f"重新连接数据库失败: {str(retry_e)}")

            log.error(f"查找图片失败 {pattern_name}: {str(e)}")
            return None

    def _reconnect_database(self):
        """重新连接数据库"""
        try:
            if self.db:
                self.db.close()
            self.db = duckdb.connect(self.db_path)
            log.info("数据库重新连接成功")
        except Exception as e:
            log.error(f"重新连接数据库失败: {str(e)}")
            raise

    def _batch_insert(self, batch):
        """批量插入记录

        Args:
            batch: 文件信息字典列表
        """
        if not batch:
            return

        try:
            # 准备批量插入的数据
            values = []
            for file_info in batch:
                values.append((
                    file_info['filename'],
                    file_info['name_without_ext'],
                    file_info['extension'],
                    file_info['relative_path'],
                    file_info['full_path'],
                    file_info['has_chinese'],
                    file_info['normalized_filename'],
                    file_info['normalized_name'],
                    file_info['file_size'],
                    file_info['modified_time'],
                    file_info['created_time'],
                    file_info['indexed_time'],
                    file_info.get('width', 0),
                    file_info.get('height', 0),
                    file_info.get('has_exif', False),
                    file_info.get('orientation', 1)
                ))

            # 执行批量插入
            self.db.executemany("""
                INSERT INTO image_files (
                    filename, name_without_ext, extension, relative_path, full_path,
                    has_chinese, normalized_filename, normalized_name,
                    file_size, modified_time, created_time, indexed_time,
                    width, height, has_exif, orientation
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ON CONFLICT (relative_path) DO UPDATE SET
                    filename = excluded.filename,
                    name_without_ext = excluded.name_without_ext,
                    extension = excluded.extension,
                    full_path = excluded.full_path,
                    has_chinese = excluded.has_chinese,
                    normalized_filename = excluded.normalized_filename,
                    normalized_name = excluded.normalized_name,
                    file_size = excluded.file_size,
                    modified_time = excluded.modified_time,
                    created_time = excluded.created_time,
                    indexed_time = excluded.indexed_time,
                    width = excluded.width,
                    height = excluded.height,
                    has_exif = excluded.has_exif,
                    orientation = excluded.orientation
            """, values)

        except Exception as e:
            self.error_signal.emit(f"批量插入失败: {str(e)}")
            raise e