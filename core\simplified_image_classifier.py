#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
简化图片分类器模块
为了保持API兼容性而创建，但实际上不再进行复杂的A/B/C分类
所有图片都将由RectPack算法统一处理

特性：
1. 保持与现有API的兼容性
2. 简化分类逻辑，所有图片标记为统一类型
3. 为RectPack算法提供预处理
4. 保留必要的图片元数据
"""

import logging
from typing import List, Dict, Any, Tuple
from PyQt6.QtCore import QObject, pyqtSignal

# 配置日志
log = logging.getLogger(__name__)

class SimplifiedImageClassifier(QObject):
    """
    简化图片分类器类
    
    保持API兼容性，但简化分类逻辑，所有图片都由RectPack算法统一处理
    """
    
    # 信号定义
    log_signal = pyqtSignal(str)  # 日志信号
    
    def __init__(self, log_signal=None):
        """
        初始化简化图片分类器
        
        Args:
            log_signal: 日志信号，用于向UI发送日志信息
        """
        super().__init__()
        self.log_signal = log_signal
    
    def emit_log(self, message: str):
        """
        发送日志信息
        
        Args:
            message: 日志信息
        """
        if self.log_signal:
            self.log_signal.emit(message)
        log.info(message)
    
    def preprocess_patterns(self, pattern_items: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        预处理图片数据，添加必要的元数据
        
        Args:
            pattern_items: 图片列表
            
        Returns:
            List[Dict[str, Any]]: 预处理后的图片列表
        """
        processed_patterns = []
        
        for i, pattern in enumerate(pattern_items):
            # 创建副本避免修改原始数据
            processed_pattern = pattern.copy()
            
            # 确保必要的字段存在
            width_cm = processed_pattern.get('width_cm', 0)
            height_cm = processed_pattern.get('height_cm', 0)
            pattern_name = processed_pattern.get('pattern_name', '')
            
            # 添加索引和行号（如果不存在）
            if 'index' not in processed_pattern:
                processed_pattern['index'] = i
            if 'row_number' not in processed_pattern:
                processed_pattern['row_number'] = i + 1
            
            # 生成唯一标识符
            if 'unique_id' not in processed_pattern:
                unique_id = f"{pattern_name}_{width_cm:.1f}_{height_cm:.1f}_{i}_{processed_pattern['row_number']}"
                processed_pattern['unique_id'] = unique_id
            
            # 计算图片面积（用于排序）
            area = width_cm * height_cm
            processed_pattern['area'] = area
            
            # 计算长宽比
            aspect_ratio = width_cm / height_cm if height_cm > 0 else 1.0
            processed_pattern['aspect_ratio'] = aspect_ratio
            
            # 标记为统一类型（保持兼容性）
            processed_pattern['image_class'] = 'UNIFIED'
            processed_pattern['need_rotation'] = False  # 让RectPack算法决定是否旋转
            
            processed_patterns.append(processed_pattern)
        
        self.emit_log(f"预处理完成: {len(processed_patterns)} 个图片")
        return processed_patterns
    
    def classify_images(self, pattern_items: List[Dict[str, Any]], canvas_width_cm: float,
                       class_a_threshold: float = 0.95, class_b_error_range: float = 0.05,
                       original_canvas_width_cm: float = None) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]], List[Dict[str, Any]]]:
        """
        图片分类方法（保持API兼容性）
        
        为了保持与现有代码的兼容性，此方法仍然返回三个列表，
        但实际上所有图片都会被放在一个列表中，由RectPack算法统一处理
        
        Args:
            pattern_items: 图片列表
            canvas_width_cm: 画布宽度（厘米）
            class_a_threshold: A类阈值（保持兼容性，实际不使用）
            class_b_error_range: B类误差范围（保持兼容性，实际不使用）
            original_canvas_width_cm: 原始画布宽度（保持兼容性，实际不使用）
            
        Returns:
            Tuple[List[Dict], List[Dict], List[Dict]]: (A类图片列表, B类图片列表, C类图片列表)
            注意：为了兼容性，所有图片都会放在C类列表中，A类和B类列表为空
        """
        self.emit_log("开始简化图片分类...")
        self.emit_log(f"图片总数: {len(pattern_items)}")
        self.emit_log("注意: 使用RectPack统一算法，不再进行A/B/C分类")
        
        # 预处理图片数据
        processed_patterns = self.preprocess_patterns(pattern_items)
        
        # 按面积排序（大图片优先，有利于装箱效果）
        processed_patterns.sort(key=lambda p: p.get('area', 0), reverse=True)
        
        # 为了保持API兼容性，返回三个列表
        # 但所有图片都放在"C类"列表中，由RectPack算法统一处理
        class_a_patterns = []  # 空列表
        class_b_patterns = []  # 空列表
        class_c_patterns = processed_patterns  # 所有图片
        
        # 更新图片的分类标记（保持兼容性）
        for pattern in class_c_patterns:
            pattern['image_class'] = 'C'  # 标记为C类以保持兼容性
        
        self.emit_log(f"分类完成: A类={len(class_a_patterns)}, B类={len(class_b_patterns)}, C类={len(class_c_patterns)}")
        self.emit_log("所有图片将由RectPack算法统一处理以获得最优排列效果")
        
        return class_a_patterns, class_b_patterns, class_c_patterns
    
    def extract_class_a_patterns(self, pattern_items: List[Dict[str, Any]], 
                                canvas_width_cm: float, class_a_threshold: float = 0.95) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
        """
        提取A类图片（保持API兼容性）
        
        Args:
            pattern_items: 图片列表
            canvas_width_cm: 画布宽度
            class_a_threshold: A类阈值
            
        Returns:
            Tuple[List[Dict], List[Dict]]: (A类图片列表, 剩余图片列表)
        """
        # 为了保持兼容性，返回空的A类列表和完整的剩余列表
        return [], pattern_items.copy()
    
    def extract_class_b_patterns(self, pattern_items: List[Dict[str, Any]], 
                                canvas_width_cm: float, class_b_error_range: float = 0.05,
                                original_canvas_width_cm: float = None) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
        """
        提取B类图片（保持API兼容性）
        
        Args:
            pattern_items: 图片列表
            canvas_width_cm: 画布宽度
            class_b_error_range: B类误差范围
            original_canvas_width_cm: 原始画布宽度
            
        Returns:
            Tuple[List[Dict], List[Dict]]: (B类图片列表, 剩余图片列表)
        """
        # 为了保持兼容性，返回空的B类列表和完整的剩余列表
        return [], pattern_items.copy()
    
    def classify_remaining_as_c(self, pattern_items: List[Dict[str, Any]], 
                               canvas_width_cm: float = None) -> List[Dict[str, Any]]:
        """
        将剩余图片归类为C类（保持API兼容性）
        
        Args:
            pattern_items: 剩余图片列表
            canvas_width_cm: 画布宽度
            
        Returns:
            List[Dict]: C类图片列表
        """
        # 预处理并返回所有图片
        processed_patterns = self.preprocess_patterns(pattern_items)
        
        # 标记为C类
        for pattern in processed_patterns:
            pattern['image_class'] = 'C'
        
        return processed_patterns
    
    def get_classification_summary(self, class_a_patterns: List[Dict[str, Any]], 
                                  class_b_patterns: List[Dict[str, Any]], 
                                  class_c_patterns: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        获取分类摘要信息
        
        Args:
            class_a_patterns: A类图片列表
            class_b_patterns: B类图片列表
            class_c_patterns: C类图片列表
            
        Returns:
            Dict[str, Any]: 分类摘要信息
        """
        total_count = len(class_a_patterns) + len(class_b_patterns) + len(class_c_patterns)
        total_area = sum(p.get('area', 0) for p in class_c_patterns)  # 只计算C类（实际上是所有图片）
        
        return {
            'total_count': total_count,
            'class_a_count': len(class_a_patterns),
            'class_b_count': len(class_b_patterns),
            'class_c_count': len(class_c_patterns),
            'total_area': total_area,
            'classification_method': 'RectPack统一算法',
            'note': '所有图片由RectPack算法统一处理以获得最优排列效果'
        }
    
    def optimize_for_rectpack(self, pattern_items: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        为RectPack算法优化图片数据
        
        Args:
            pattern_items: 图片列表
            
        Returns:
            List[Dict[str, Any]]: 优化后的图片列表
        """
        optimized_patterns = self.preprocess_patterns(pattern_items)
        
        # 按不同策略排序以测试最佳效果
        # 策略1: 按面积降序（大图片优先）
        area_sorted = sorted(optimized_patterns, key=lambda p: p.get('area', 0), reverse=True)
        
        # 策略2: 按长宽比排序（相似形状的图片聚集）
        ratio_sorted = sorted(optimized_patterns, key=lambda p: p.get('aspect_ratio', 1.0))
        
        # 策略3: 按周长排序
        perimeter_sorted = sorted(optimized_patterns, 
                                key=lambda p: 2 * (p.get('width_cm', 0) + p.get('height_cm', 0)), 
                                reverse=True)
        
        # 默认使用面积排序策略
        self.emit_log("使用面积降序排序策略优化图片顺序")
        return area_sorted
