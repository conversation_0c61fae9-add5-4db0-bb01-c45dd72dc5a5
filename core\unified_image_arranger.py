#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
统一图片排列器模块
使用RectPack算法替换现有的复杂分类逻辑，实现最优的画布空间利用率

特性：
1. 使用rectpack算法统一处理所有图片
2. 移除复杂的A/B/C分类逻辑
3. 自动优化图片旋转和排列
4. 保持与现有API的兼容性
5. 提供更高的画布利用率
"""

import logging
import time
from typing import List, Dict, Any, Tuple
from PyQt6.QtCore import QObject, pyqtSignal

# 导入新的RectPack排列器
from core.rectpack_arranger import RectPackArranger

# 配置日志
log = logging.getLogger(__name__)

class UnifiedImageArranger(QObject):
    """
    统一图片排列器类
    
    使用RectPack算法替换现有的复杂分类逻辑，实现最优的画布空间利用率
    """
    
    # 信号定义
    progress_signal = pyqtSignal(int)  # 进度值 (0-100)
    log_signal = pyqtSignal(str)       # 日志信息
    error_signal = pyqtSignal(str)     # 错误信息
    
    def __init__(self, log_signal=None):
        """
        初始化统一图片排列器
        
        Args:
            log_signal: 日志信号，用于向UI发送日志信息
        """
        super().__init__()
        self.log_signal = log_signal
        
        # 初始化RectPack排列器
        self.rectpack_arranger = None
        
        # 初始化画布设置
        self.canvas_width_px = 0
        self.canvas_height_px = 0
        self.image_spacing_px = 0
        self.max_height_px = 0
        self.ppi = 72  # 默认PPI值
        
        # 初始化排列状态
        self.images_arranged = 0
        self.arrangement_start_time = None
        self.last_speed_update_time = None
        self.speed_update_interval = 2.0  # 速度更新间隔（秒）
        
        # 初始化已处理图片ID集合
        self.processed_image_ids = set()
    
    def emit_log(self, message: str):
        """
        发送日志信息
        
        Args:
            message: 日志信息
        """
        if self.log_signal:
            self.log_signal.emit(message)
        log.info(message)
    
    def initialize(self, canvas_width_px: int, max_height_px: int, image_spacing_px: int, ppi: float = 72):
        """
        初始化画布设置
        
        Args:
            canvas_width_px: 画布宽度（像素）
            max_height_px: 最大高度（像素）
            image_spacing_px: 图片间距（像素）
            ppi: 每英寸像素数
        """
        self.canvas_width_px = canvas_width_px
        self.max_height_px = max_height_px
        self.image_spacing_px = image_spacing_px
        self.ppi = ppi
        
        # 初始化RectPack排列器
        self.rectpack_arranger = RectPackArranger(
            container_width=canvas_width_px,
            image_spacing=image_spacing_px,
            max_height=max_height_px,
            log_signal=self.log_signal
        )
        
        self.emit_log(f"初始化统一图片排列器: 画布宽度={canvas_width_px}像素, 最大高度={max_height_px}像素, 图片间距={image_spacing_px}像素, PPI={ppi}")
        
        # 重置排列状态
        self.images_arranged = 0
        self.arrangement_start_time = time.time()
        self.last_speed_update_time = time.time()
        self.processed_image_ids.clear()
    
    def _cm_to_px(self, cm_value: float) -> int:
        """
        将厘米转换为像素
        
        Args:
            cm_value: 厘米值
            
        Returns:
            int: 像素值
        """
        # 1厘米 = 0.393701英寸
        inches = cm_value * 0.393701
        # 像素 = 英寸 * PPI
        pixels = int(inches * self.ppi)
        return pixels
    
    def _update_arrangement_speed(self):
        """更新排列速度信息"""
        current_time = time.time()
        elapsed_time = current_time - self.arrangement_start_time
        
        # 每隔一段时间更新一次速度信息
        if current_time - self.last_speed_update_time >= self.speed_update_interval:
            if elapsed_time > 0:
                images_per_second = self.images_arranged / elapsed_time
                self.emit_log(f"已排列 {self.images_arranged} 个图片，速度: {images_per_second:.2f} 图片/秒")
            
            self.last_speed_update_time = current_time
    
    def generate_unique_id(self, pattern: Dict[str, Any]) -> str:
        """
        生成图片的唯一标识符
        
        Args:
            pattern: 图片数据字典
            
        Returns:
            str: 唯一标识符
        """
        # 获取基本信息
        width_cm = pattern.get('width_cm', 0)
        height_cm = pattern.get('height_cm', 0)
        pattern_name = pattern.get('pattern_name', '')
        index = pattern.get('index', -1)
        row_number = pattern.get('row_number', -1)
        
        # 如果已有唯一标识符，则直接返回
        if 'unique_id' in pattern and pattern['unique_id']:
            return pattern['unique_id']
        
        # 创建基础唯一标识符
        base_unique_id = f"{pattern_name}_{width_cm:.1f}_{height_cm:.1f}"
        
        # 创建完整唯一标识符，包含索引和行号
        unique_id = f"{base_unique_id}_{index}_{row_number}"
        
        return unique_id
    
    def arrange_image(self, pattern: Dict[str, Any]) -> Tuple[Dict[str, Any], bool]:
        """
        排列单个图片（统一处理，不再区分A/B/C类）
        
        Args:
            pattern: 图片数据
            
        Returns:
            Tuple[Dict[str, Any], bool]: (图片信息, 是否成功)
        """
        # 获取图案信息
        image_path = pattern['path']
        width_cm = pattern['width_cm']
        height_cm = pattern['height_cm']
        pattern_name = pattern['pattern_name']
        
        # 使用通用方法生成唯一标识符
        unique_id = self.generate_unique_id(pattern)
        
        # 更新图片数据中的唯一标识符
        pattern['unique_id'] = unique_id
        
        # 检查是否已处理过该图片
        if unique_id in self.processed_image_ids:
            self.emit_log(f"跳过已处理的图片: {unique_id}")
            return None, False
        
        # 转换为像素值（不考虑旋转，让RectPack算法自动决定）
        width_px = self._cm_to_px(width_cm)
        height_px = self._cm_to_px(height_cm)
        
        # 准备图片数据
        image_data = {
            'path': image_path,
            'name': pattern_name,
            'width_cm': width_cm,
            'height_cm': height_cm,
            'original_width_px': width_px,
            'original_height_px': height_px,
            'unique_id': unique_id,
            'pattern_data': pattern  # 保存原始图案数据
        }
        
        # 使用RectPack算法放置图片
        x, y, success = self.rectpack_arranger.place_image(width_px, height_px, image_data)
        
        if not success:
            self.emit_log(f"无法放置图片 {unique_id}")
            return None, False
        
        # 更新已排列图片数量
        self.images_arranged += 1
        self._update_arrangement_speed()
        
        # 添加到已处理图片集合
        self.processed_image_ids.add(unique_id)
        
        # 从已放置的图片中获取实际信息（可能包含旋转）
        placed_image = None
        for img in self.rectpack_arranger.placed_images:
            if img.get('unique_id') == unique_id:
                placed_image = img
                break
        
        if not placed_image:
            self.emit_log(f"无法找到已放置的图片信息: {unique_id}")
            return None, False
        
        # 收集图像信息
        image_info = {
            'x': x,
            'y': y,
            'width': placed_image['width'],
            'height': placed_image['height'],
            'path': image_path,
            'name': pattern_name,
            'original_width_cm': width_cm,
            'original_height_cm': height_cm,
            'need_rotation': placed_image.get('rotated', False),
            'table_width_cm': pattern.get('width_cm', width_cm),
            'table_height_cm': pattern.get('height_cm', height_cm),
            'unique_id': unique_id
        }
        
        return image_info, True
    
    def arrange_images(self, pattern_items: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        排列所有图片（统一处理，不再区分A/B/C类）
        
        Args:
            pattern_items: 图片列表
            
        Returns:
            List[Dict[str, Any]]: 排列后的图片信息列表
        """
        # 重置排列状态
        self.images_arranged = 0
        self.arrangement_start_time = time.time()
        self.last_speed_update_time = time.time()
        self.processed_image_ids.clear()
        
        # 重置RectPack排列器
        if self.rectpack_arranger:
            self.rectpack_arranger.reset()
        
        self.emit_log(f"开始统一排列 {len(pattern_items)} 个图片...")
        
        # 排列结果
        arranged_images = []
        
        # 创建输入数据的副本，避免修改原始数据
        pattern_items_copy = [pattern.copy() for pattern in pattern_items]
        
        # 按面积排序图片（大图片优先），这样可以获得更好的装箱效果
        pattern_items_copy.sort(key=lambda p: p.get('width_cm', 0) * p.get('height_cm', 0), reverse=True)
        
        # 逐个排列图片
        for i, pattern in enumerate(pattern_items_copy):
            try:
                # 排列图片
                image_info, success = self.arrange_image(pattern)
                if success and image_info:
                    arranged_images.append(image_info)
                    self.emit_log(f"成功排列图片 {i+1}/{len(pattern_items_copy)}: {pattern.get('pattern_name', '')}")
                else:
                    self.emit_log(f"无法排列图片 {pattern.get('pattern_name', '')}_{pattern.get('width_cm', 0)}_{pattern.get('height_cm', 0)}")
                    
                    # 检查是否因为画布高度限制而无法排列
                    if self.rectpack_arranger and self.rectpack_arranger.canvas_is_full:
                        self.emit_log("画布已满，停止排列")
                        break
                
                # 更新进度
                progress = int((i + 1) / len(pattern_items_copy) * 100)
                if self.progress_signal:
                    self.progress_signal.emit(progress)
                    
            except Exception as e:
                self.emit_log(f"排列图片时发生错误: {str(e)}")
                log.error(f"排列图片时发生错误: {str(e)}", exc_info=True)
                continue
        
        # 获取布局统计信息
        if self.rectpack_arranger:
            layout_info = self.rectpack_arranger.get_layout_info()
            self.emit_log(f"排列完成: 成功排列 {len(arranged_images)} 个图片")
            self.emit_log(f"画布利用率: {layout_info['utilization_percent']:.2f}%")
            self.emit_log(f"画布尺寸: {layout_info['container_width']}x{layout_info['container_height']}像素")
        
        return arranged_images
    
    def optimize_layout(self) -> bool:
        """
        优化布局以提高画布利用率
        
        Returns:
            bool: 是否成功优化
        """
        if not self.rectpack_arranger:
            return False
        
        self.emit_log("开始优化布局...")
        success = self.rectpack_arranger.optimize_for_utilization()
        
        if success:
            layout_info = self.rectpack_arranger.get_layout_info()
            self.emit_log(f"布局优化完成，当前利用率: {layout_info['utilization_percent']:.2f}%")
        else:
            self.emit_log("布局优化未找到更好的方案")
        
        return success
    
    def get_layout_statistics(self) -> Dict[str, Any]:
        """
        获取布局统计信息
        
        Returns:
            Dict[str, Any]: 布局统计信息
        """
        if not self.rectpack_arranger:
            return {}
        
        return self.rectpack_arranger.get_layout_info()
