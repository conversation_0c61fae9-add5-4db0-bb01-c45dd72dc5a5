#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一图片处理接口

确保测试模式和正式环境使用完全相同的数据结构和处理逻辑

作者: PS画布修复团队
日期: 2024-12-19
版本: 统一接口版
"""

import time
from typing import List, Dict, Any, Optional
from dataclasses import dataclass, field


@dataclass
class ImagePlacementData:
    """统一的图片放置数据结构"""

    # 基础信息
    image_name: str = ""
    image_path: str = ""

    # 位置信息（统一使用像素）
    x: int = 0
    y: int = 0
    width: int = 0
    height: int = 0

    # 旋转信息
    rotated: bool = False
    rotation_angle: int = 0

    # 图层信息
    layer_index: int = 0
    total_images: int = 1
    layer_name: str = ""

    # 验证信息
    validated: bool = False
    validation_errors: List[str] = field(default_factory=list)

    # 元数据
    source_row: int = 0
    image_class: str = "C"
    processing_timestamp: float = field(default_factory=time.time)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'image_name': self.image_name,
            'image_path': self.image_path,
            'x': self.x,
            'y': self.y,
            'width': self.width,
            'height': self.height,
            'rotated': self.rotated,
            'rotation_angle': self.rotation_angle,
            'layer_index': self.layer_index,
            'total_images': self.total_images,
            'layer_name': self.layer_name,
            'validated': self.validated,
            'validation_errors': self.validation_errors,
            'source_row': self.source_row,
            'image_class': self.image_class,
            'processing_timestamp': self.processing_timestamp
        }

    def get_bounds(self) -> tuple:
        """获取边界坐标"""
        return (self.x, self.y, self.x + self.width, self.y + self.height)

    def get_center(self) -> tuple:
        """获取中心点坐标"""
        return (self.x + self.width // 2, self.y + self.height // 2)

    def get_area(self) -> int:
        """获取面积"""
        return self.width * self.height


class StandardCoordinateProcessor:
    """标准化坐标处理器"""

    def __init__(self, canvas_width: int = 205, canvas_height: int = 5000, ppi: int = 72):
        self.canvas_width = canvas_width
        self.canvas_height = canvas_height
        self.ppi = ppi

    def standardize_coordinates(self, x, y, width, height,
                              x_cm=None, y_cm=None, width_cm=None, height_cm=None) -> tuple:
        """
        标准化坐标处理

        Args:
            x, y, width, height: 像素坐标和尺寸
            x_cm, y_cm, width_cm, height_cm: 厘米坐标和尺寸（可选）

        Returns:
            tuple: (std_x, std_y, std_width, std_height, is_valid)
        """
        try:
            # 步骤1: 单位转换
            if x_cm is not None and y_cm is not None and width_cm is not None and height_cm is not None:
                # 厘米转像素：1cm = 0.393701英寸，像素 = 英寸 * PPI
                x = int(round(x_cm * 0.393701 * self.ppi))
                y = int(round(y_cm * 0.393701 * self.ppi))
                width = int(round(width_cm * 0.393701 * self.ppi))
                height = int(round(height_cm * 0.393701 * self.ppi))

            # 步骤2: 类型标准化
            std_x = int(round(float(x))) if x is not None else 0
            std_y = int(round(float(y))) if y is not None else 0
            std_width = int(round(float(width))) if width is not None else 0
            std_height = int(round(float(height))) if height is not None else 0

            # 步骤3: 有效性验证
            is_valid = self.validate_coordinates(std_x, std_y, std_width, std_height)

            return std_x, std_y, std_width, std_height, is_valid

        except (ValueError, TypeError) as e:
            return 0, 0, 0, 0, False

    def validate_coordinates(self, x: int, y: int, width: int, height: int) -> bool:
        """验证坐标有效性"""
        # 基本有效性检查
        if x < 0 or y < 0 or width <= 0 or height <= 0:
            return False

        # 边界检查
        if x + width > self.canvas_width:
            return False

        # 高度检查（如果设置了高度限制）
        if self.canvas_height > 0 and y + height > self.canvas_height:
            return False

        return True

    def get_coordinate_info(self, x: int, y: int, width: int, height: int) -> Dict[str, Any]:
        """获取坐标信息"""
        return {
            'position': (x, y),
            'size': (width, height),
            'area': width * height,
            'bounds': (x, y, x + width, y + height),
            'center': (x + width // 2, y + height // 2),
            'is_valid': self.validate_coordinates(x, y, width, height)
        }


class UnifiedImageProcessor:
    """统一的图片处理接口"""

    def __init__(self, canvas_width: int = 205, canvas_height: int = 5000, ppi: int = 72):
        self.coordinate_processor = StandardCoordinateProcessor(canvas_width, canvas_height, ppi)
        self.canvas_width = canvas_width
        self.canvas_height = canvas_height
        self.ppi = ppi

        # 处理统计
        self.processed_count = 0
        self.success_count = 0
        self.error_count = 0

    def create_placement_data(self, image_info: Dict[str, Any], position_result: Dict[str, Any],
                            layer_info: Dict[str, Any]) -> ImagePlacementData:
        """创建统一的图片放置数据"""
        data = ImagePlacementData()

        # 基础信息
        data.image_name = image_info.get('name', '')
        data.image_path = image_info.get('path', '')

        # 位置信息（确保为整数像素）
        data.x = int(round(position_result['x']))
        data.y = int(round(position_result['y']))
        data.width = int(round(position_result['width']))
        data.height = int(round(position_result['height']))

        # 旋转信息
        data.rotated = position_result.get('rotated', False)
        data.rotation_angle = 90 if data.rotated else 0

        # 图层信息
        data.layer_index = layer_info.get('index', 0)
        data.total_images = layer_info.get('total', 1)
        data.layer_name = f"RectPack_Image_{data.layer_index + 1}_of_{data.total_images}"

        # 验证标记
        data.validated = False
        data.validation_errors = []

        # 元数据
        data.source_row = image_info.get('row', 0)
        data.image_class = image_info.get('class', 'C')
        data.processing_timestamp = time.time()

        return data

    def validate_data(self, data: ImagePlacementData) -> bool:
        """验证数据完整性"""
        errors = []

        # 检查基础信息
        if not data.image_name:
            errors.append("图片名称为空")

        # 检查坐标有效性
        if not self.coordinate_processor.validate_coordinates(data.x, data.y, data.width, data.height):
            errors.append(f"坐标无效: ({data.x}, {data.y}, {data.width}, {data.height})")

        # 检查图层信息
        if data.layer_index < 0:
            errors.append(f"图层索引无效: {data.layer_index}")

        if data.total_images <= 0:
            errors.append(f"总图片数无效: {data.total_images}")

        # 更新验证结果
        data.validation_errors = errors
        data.validated = len(errors) == 0

        return data.validated

    def standardize_coordinates(self, data: ImagePlacementData):
        """标准化坐标处理"""
        std_x, std_y, std_width, std_height, is_valid = self.coordinate_processor.standardize_coordinates(
            data.x, data.y, data.width, data.height
        )

        # 更新数据
        data.x = std_x
        data.y = std_y
        data.width = std_width
        data.height = std_height

        if not is_valid:
            data.validation_errors.append("坐标标准化失败")
            data.validated = False

    def process_image(self, data: ImagePlacementData, mode: str = 'production') -> bool:
        """
        统一的图片处理方法

        Args:
            data: 图片放置数据
            mode: 处理模式 ('test' 或 'production')

        Returns:
            bool: 处理是否成功
        """
        self.processed_count += 1

        try:
            # 步骤1: 数据验证
            if not self.validate_data(data):
                self.error_count += 1
                return False

            # 步骤2: 坐标标准化
            self.standardize_coordinates(data)

            # 步骤3: 再次验证标准化后的数据
            if not data.validated:
                self.error_count += 1
                return False

            # 步骤4: 根据模式选择处理方式
            if mode == 'test':
                success = self.process_test_mode(data)
            elif mode == 'production':
                success = self.process_production_mode(data)
            else:
                raise ValueError(f"未知的处理模式: {mode}")

            if success:
                self.success_count += 1
            else:
                self.error_count += 1

            return success

        except Exception as e:
            data.validation_errors.append(f"处理异常: {str(e)}")
            self.error_count += 1
            return False

    def process_test_mode(self, data: ImagePlacementData) -> bool:
        """测试模式处理"""
        try:
            # 在测试模式中，我们只需要验证数据的完整性
            # 实际的PIL绘制由调用方负责

            # 记录处理信息
            print(f"🧪 测试模式处理: {data.layer_name}")
            print(f"   位置: ({data.x}, {data.y})")
            print(f"   尺寸: {data.width}x{data.height}")
            print(f"   旋转: {'是' if data.rotated else '否'}")

            return True

        except Exception as e:
            data.validation_errors.append(f"测试模式处理失败: {str(e)}")
            return False

    def process_production_mode(self, data: ImagePlacementData) -> bool:
        """正式环境处理"""
        try:
            # 在正式环境中，调用PhotoshopHelper进行实际处理
            from utils.photoshop_helper import PhotoshopHelper

            # 准备参数
            kwargs = {
                'x': data.x,
                'y': data.y,
                'width': data.width,
                'height': data.height,
                'rotate': data.rotated,
                'rotation': data.rotation_angle
            }

            # 添加图层信息到image_data中
            image_data = {
                'layer_index': data.layer_index,
                'total_images': data.total_images,
                'layer_name': data.layer_name
            }

            # 调用PhotoshopHelper
            success = PhotoshopHelper.place_image(data.image_path, **kwargs)

            if success:
                print(f"🎯 正式环境处理成功: {data.layer_name}")
            else:
                data.validation_errors.append("PhotoshopHelper处理失败")

            return success

        except Exception as e:
            data.validation_errors.append(f"正式环境处理失败: {str(e)}")
            return False

    def process_batch(self, data_list: List[ImagePlacementData], mode: str = 'production') -> List[Dict[str, Any]]:
        """批量处理图片"""
        results = []

        for data in data_list:
            try:
                success = self.process_image(data, mode)
                results.append({
                    'data': data,
                    'success': success,
                    'mode': mode,
                    'errors': data.validation_errors
                })
            except Exception as e:
                results.append({
                    'data': data,
                    'success': False,
                    'mode': mode,
                    'error': str(e),
                    'errors': data.validation_errors
                })

        return results

    def get_statistics(self) -> Dict[str, Any]:
        """获取处理统计信息"""
        return {
            'processed_count': self.processed_count,
            'success_count': self.success_count,
            'error_count': self.error_count,
            'success_rate': self.success_count / max(1, self.processed_count),
            'canvas_info': {
                'width': self.canvas_width,
                'height': self.canvas_height,
                'ppi': self.ppi
            }
        }


def create_unified_placement_data(image_info: Dict[str, Any], position_result: Dict[str, Any],
                                layer_info: Dict[str, Any]) -> ImagePlacementData:
    """
    便捷函数：创建统一的图片放置数据

    Args:
        image_info: 图片信息字典
        position_result: 位置计算结果字典
        layer_info: 图层信息字典

    Returns:
        ImagePlacementData: 统一的图片放置数据
    """
    processor = UnifiedImageProcessor()
    return processor.create_placement_data(image_info, position_result, layer_info)


def process_with_unified_interface(data_list: List[ImagePlacementData], mode: str = 'production') -> List[Dict[str, Any]]:
    """
    便捷函数：使用统一接口处理图片列表

    Args:
        data_list: 图片放置数据列表
        mode: 处理模式 ('test' 或 'production')

    Returns:
        List[Dict]: 处理结果列表
    """
    processor = UnifiedImageProcessor()
    return processor.process_batch(data_list, mode)


# 导出主要类和函数
__all__ = [
    'ImagePlacementData',
    'StandardCoordinateProcessor',
    'UnifiedImageProcessor',
    'create_unified_placement_data',
    'process_with_unified_interface'
]
