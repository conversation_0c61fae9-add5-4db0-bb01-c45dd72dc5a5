# DeAI-智能排版PhotoShop自动化工具

## 项目介绍

DeAI-智能排版PhotoShop自动化工具是一款专为设计师和印刷行业开发的智能排版应用。它能够自动创建指定宽度的画布，从图库中检索图片并进行智能排列，最终保存为高质量的TIFF格式文件，大幅提高设计和排版效率。

### 核心功能

- **智能图库索引**：快速索引和检索本地图库中的图片资源
- **自动排版布局**：使用优化的二维装箱算法，实现画布空间的最大化利用
- **Photoshop集成**：无缝集成Adobe Photoshop，提供专业级图像处理能力
- **批量处理**：支持批量处理多个材质和图案，提高工作效率
- **高度可配置**：提供丰富的参数设置，满足不同场景的需求
- **内存优化**：针对大型图片集进行了内存使用优化，提高稳定性

## 系统要求

### 基本要求

- **操作系统**：Windows 10/11 (64位)
- **Python版本**：Python 3.8+
- **内存**：至少8GB RAM，推荐16GB或更高
- **存储空间**：至少1GB可用空间（不包括图库存储）

### 可选要求

- **Adobe Photoshop**：CS3 ~ CC 2024版本（推荐使用CC 2020或更高版本）

## 安装指南

### 1. 安装基础依赖

```bash
pip install pillow numpy pyqt6 pandas duckdb
```

### 2. 安装Photoshop API依赖（可选但推荐）

```bash
pip install photoshop-python-api
```

### 3. 下载并运行应用

```bash
# 克隆仓库
git clone https://gitee.com/your-username/robot_ps_smart.git

# 进入项目目录
cd robot_ps_smart

# 运行应用
python robot_ps_smart_app.py
```

## 使用指南

### 基本流程

1. **选择图库**：点击“选择图库”按钮，选择包含图片资源的文件夹
2. **索引图库**：点击“索引图库”按钮，系统将扫描并索引所有图片资源
3. **选择材质文件夹**：点击“选择材质文件夹”按钮，选择包含Excel材质表格的文件夹
4. **智能排版**：点击“智能排版”按钮，系统将自动检索图片并进行智能排版
5. **查看结果**：排版完成后，TIFF文件将保存在指定位置

### 高级设置

点击“高级设置”按钮可以配置以下参数：

- **画布宽度**：设置画布的宽度（默认为2米）
- **PPI**：设置图像的每英寸像素数（默认为72）
- **图片间距**：设置图片之间的间距（默认为0.1厘米）
- **水平扩展**：设置画布的水平扩展值（默认为0厘米）
- **最大高度**：设置画布的最大高度（默认为5000厘米）
- **Photoshop设置**：配置Photoshop相关选项

## 排版算法优化

本项目实现了高效的二维装箱算法，具有以下特点：

1. **从顶部往下，自左而右**排列图片，实现更智能的排版
2. **优化二维装箱算法**，实现画布利用率最大化
3. **智能旋转图片**：当图片高度与画布宽度接近时，自动旋转图片以提高利用率
4. **精确计算利用率**：考虑每个画布最后一块图片剪切后的整体利用率
5. **高效图片处理**：直接在画布插入图片，调整尺寸，修改坐标，减少内存占用

### 超宽图片处理优化

- **自动旋转**: 当图片宽度超过画布宽度时，自动尝试旋转90度
- **智能缩放**: 当旋转后仍然超宽时，自动缩放至画布宽度的95%
- **碰撞检测优化**: 对于超宽图片（宽度超过画布宽度1.5倍），强制返回碰撞，防止错误放置
- **旋转优先级提升**: 对于宽度超过画布宽度80%的图片，自动提高旋转优先级
- **B类图片特殊处理**: 对B类图片组中的超宽图片单独处理，避免与其他图片重叠

## 内存管理优化

为了处理大型图片集，本项目实现了以下内存优化策略：

1. **定期清理内存**：在处理过程中定期清理Photoshop内存
2. **关闭不需要的文档**：自动关闭不再需要的文档，减少内存占用
3. **优化图片处理流程**：使用更高效的方式处理图片，减少内存占用
4. **错误恢复机制**：当某个图片处理失败时能够继续处理其他图片

## 常见问题

### Q: 程序无法启动Photoshop怎么办？

A: 请确保已正确安装Photoshop，并且已安装photoshop-python-api库。如果问题仍然存在，可以尝试手动启动Photoshop，然后使用“启动Photoshop”按钮进行连接。

### Q: 图片排版不理想怎么调整？

A: 可以通过调整“高级设置”中的参数来优化排版效果，特别是图片间距和水平扩展参数。

### Q: 处理大量图片时程序崩溃怎么办？

A: 尝试减小批处理的图片数量，或增加系统内存。同时，确保已启用最新的内存优化功能。

## 技术支持

如有任何问题或建议，请通过以下方式联系我们：

- **邮箱**：<EMAIL>
- **问题追踪**：[提交Issue](https://gitee.com/your-username/robot_ps_smart/issues)

## 许可证

本项目采用 [MIT 许可证](LICENSE)。
