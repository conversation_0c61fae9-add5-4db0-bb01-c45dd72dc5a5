# 图片分类模块 (ImageClassifier)

## 概述

`ImageClassifier` 是一个专门用于图片分类的模块，将图片分为A类、B类和C类，以便使用不同的算法进行布局处理。该模块遵循高内聚低耦合的设计原则，便于维护和扩展。

## 分类规则

图片分类基于图片尺寸与画布宽度的数学关系：

1. **A类（宽幅类）**：
   - 当图片任一边长与画布宽度相等或达到95%以上时，归为A类
   - 当图片长边与画布宽度相近时，直接排列
   - 当图片短边与画布宽度相近时，图片旋转90度竖着放置
   - A类图片占满一整行

2. **B类（宽幅约数类）**：
   - 按相同尺寸约数倍数组的方式存在
   - 当图片任一边长的倍数与画布宽度相等或达到95%以上时，可能归为B类
   - 只有当相同尺寸的图片数量达到倍数值时，才能形成B类组
   - 例如：160宽幅，5个40x130的图片，因为40×4=160，所以4个相同尺寸的40x130图片为一组，归为B类，剩余的1个40x130归到C组
   - 例如：160宽幅，2个40x130的图片，则不能归到B类，应该归到C类
   - B类图片按组排列，每组占据一行空间，根据需要旋转
   - 对于超宽图片（宽度超过画布宽度1.5倍），会单独处理，避免与其他图片重叠

3. **C类（其他图片）**：
   - 不符合A类和B类条件的图片归为C类
   - C类图片使用俄罗斯方块式算法排列，最大化利用空间
   - 对于超宽图片，会自动旋转或缩放以适应画布宽度

## 核心功能

### 1. 图片分类

根据图片尺寸与画布宽度的关系，将图片分为A类、B类和C类。

```python
class_a_patterns, class_b_patterns, class_c_patterns = image_classifier.classify_images(
    pattern_items=pattern_items,
    canvas_width_cm=canvas_width_cm,
    class_a_threshold=0.95,
    class_b_error_range=0.05
)
```

### 2. B类图片分组处理

对B类图片进行分组处理，只有当相同尺寸的图片数量达到倍数值时，才能形成B类组，否则将降级为C类。

```python
processed_b_patterns, downgraded_c_patterns = image_classifier.process_b_class_groups(
    class_b_patterns=class_b_patterns,
    canvas_width_cm=canvas_width_cm
)
```

例如：
- 画布宽度160cm，有5个40x130cm的图片（40×4=160）
- 4个图片可以形成一个完整的B类组（因为4是倍数值）
- 剩余的1个图片会被降级为C类
- 如果只有2个40x130cm的图片，则全部降级为C类（因为数量不足以形成完整组）

### 3. C类图片排序

对C类图片进行排序，优先处理面积大的图片和宽高比接近画布宽度的图片。

```python
sorted_c_patterns = image_classifier.sort_c_class_patterns(
    class_c_patterns=class_c_patterns,
    canvas_width_cm=canvas_width_cm
)
```

## 使用方法

```python
from core.image_classifier import ImageClassifier

# 创建图片分类器
image_classifier = ImageClassifier(log_signal=log_signal)

# 分类图片
class_a_patterns, class_b_patterns, class_c_patterns = image_classifier.classify_images(
    pattern_items=pattern_items,
    canvas_width_cm=canvas_width_cm,
    class_a_threshold=0.95,
    class_b_error_range=0.05
)

# 处理B类图片分组
processed_b_patterns, downgraded_c_patterns = image_classifier.process_b_class_groups(
    class_b_patterns=class_b_patterns,
    canvas_width_cm=canvas_width_cm
)

# 将降级的B类图片添加到C类图片列表
class_c_patterns.extend(downgraded_c_patterns)

# 对C类图片进行排序
class_c_patterns = image_classifier.sort_c_class_patterns(
    class_c_patterns=class_c_patterns,
    canvas_width_cm=canvas_width_cm
)

# 使用不同的算法处理不同类别的图片
```

## 设计原则

1. **高内聚**：图片分类模块只负责图片分类相关的功能，不涉及其他业务逻辑
2. **低耦合**：图片分类模块与其他模块之间的依赖关系最小化，便于维护和扩展
3. **单一职责**：每个方法只负责一个功能，便于理解和维护
4. **开放封闭**：模块设计遵循开放封闭原则，对扩展开放，对修改封闭

## 注意事项

1. 图片分类基于图片尺寸与画布宽度的数学关系，不依赖于图片名称或其他信息
2. 图库中的图片都是横着放置的（长边水平），与图片名字无关
3. A类图片使用简化的横向放置算法，B类和C类图片使用俄罗斯方块式算法(tetris_packer.py)处理
4. B类图片必须按相同尺寸约数倍数组的方式存在，数量不足的图片会降级为C类
5. 图片分类参数（如class_a_threshold和class_b_error_range）可以根据需要进行调整
6. B类图片分组时会考虑图片尺寸，确保相同尺寸的图片被分到同一组
7. 超宽图片（宽度超过画布宽度）会自动旋转或缩放以适应画布宽度
8. B类图片组中的超宽图片会单独处理，避免与其他图片重叠
