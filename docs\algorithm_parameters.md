# 算法参数详细说明文档

## 概述

本文档详细说明了图片排列算法中的各项高级参数设置，包括图片分类参数、旋转类策略参数、行空隙填充参数以及C类俄罗斯方块算法参数。这些参数直接影响图片的排列效果、画布利用率和处理效率。

## 图片分类参数

图片分类参数决定了不同类型图片的处理优先级和排列策略。

| 参数名称 | 默认值 | 取值范围 | 说明 |
|---------|-------|---------|------|
| A类图片边长阈值 | 90% | 0-100% | 决定图片是否归类为A类的边长阈值。当图片的宽度或高度超过画布宽度的此百分比时，图片被归类为A类。A类图片通常是大型图片，会单独占用一个画布。 |
| B类图片装载阈值 | 5% | 0-100% | 决定图片是否归类为B类的阈值。当图片面积占画布面积的百分比超过此值，但又不满足A类条件时，图片被归类为B类。B类图片通常是中等大小的图片，使用2D装箱算法排列。 |

## 旋转类策略参数

旋转类策略参数控制不同类型图片的旋转行为，影响空间利用率。

| 参数名称 | 默认值 | 取值范围 | 说明 |
|---------|-------|---------|------|
| A类旋转扩展阈值 | 20% | 0-100% | A类图片的旋转判断阈值。当旋转后的图片宽度减少超过此百分比时，系统会考虑旋转图片。较高的值意味着更倾向于保持原始方向。 |
| B类旋转扩展阈值 | 20% | 0-100% | B类图片的旋转判断阈值。当旋转后的图片宽度减少超过此百分比时，系统会考虑旋转图片。较高的值意味着更倾向于保持原始方向。 |
| C类旋转扩展阈值 | 15% | 0-100% | C类图片的旋转判断阈值。当旋转后的图片宽度减少超过此百分比时，系统会考虑旋转图片。较高的值意味着更倾向于保持原始方向。 |
| 极端宽高比阈值 | 3.0 | 1.0-10.0 | 定义何为极端宽高比。当图片的宽高比或高宽比超过此值时，被视为极端宽高比图片，可能会触发特殊处理逻辑。 |
| 极端比利用率阈值 | 60% | 0-100% | 极端宽高比图片的利用率阈值。当极端宽高比图片旋转后的利用率提升超过此百分比时，系统会优先考虑旋转该图片。 |

## 行空隙填充参数

行空隙填充参数控制图片排列过程中的空隙填充策略，影响水平利用率。

| 参数名称 | 默认值 | 取值范围 | 说明 |
|---------|-------|---------|------|
| 行利用率阈值 | 95% | 0-100% | 行利用率的目标阈值。当一行的利用率达到此值时，系统会认为该行已经充分利用，可以开始新的一行。较高的值会导致系统更努力地填充当前行。 |
| C类空隙填充阈值 | 1% | 0-100% | C类图片的空隙填充判断阈值。当行中的空隙面积占比低于此值时，系统可能会跳过填充该空隙。较低的值意味着系统会尝试填充更小的空隙。 |
| 启用行空隙填充 | 是 | 是/否 | 是否启用行空隙填充功能。启用后，系统会尝试用小图片填充行中的空隙，提高水平利用率。 |

## C类俄罗斯方块算法参数

C类俄罗斯方块算法参数控制C类图片的排列策略，直接影响水平利用率和垂直空隙。

| 参数名称 | 默认值 | 取值范围 | 说明 |
|---------|-------|---------|------|
| 最小利用率提升 | 30% | 0-100% | 放置图片时的最小利用率提升阈值。当放置图片后的利用率提升超过此值时，该位置被视为良好的放置位置。较高的值会导致系统更严格地选择放置位置。 |
| 高利用率阈值 | 95% | 0-100% | 高利用率的判断阈值。当行的利用率达到此值时，系统会认为该行已经达到高利用率，可能会触发特殊处理逻辑。 |
| 极端宽高比最小值 | 5.0 | 1.0-10.0 | C类算法中极端宽高比的最小值。当图片的宽高比或高宽比超过此值时，被视为极端宽高比图片，可能会触发特殊的放置策略。 |
| 水平优先级 | 80% | 0-100% | 水平放置的优先级。此值越高，系统越倾向于优化水平利用率，而不是垂直高度。这是俄罗斯方块算法中最关键的参数之一，直接影响水平空间的利用效率。 |
| 空隙填充优先级 | 70% | 0-100% | 空隙填充的优先级。此值越高，系统越倾向于填充现有的空隙，而不是创建新的行。较高的值有助于减少垂直空隙，提高整体利用率。 |
| 旋转优先级 | 60% | 0-100% | 图片旋转的优先级。此值越高，系统越倾向于旋转图片以获得更好的放置效果。较高的值可能会导致更多的图片被旋转90度放置。对于宽度超过画布宽度80%的图片，系统会自动提高旋转优先级至80%以上。 |
| 超宽图片缩放比例 | 95% | 80-100% | 当超宽图片（宽度超过画布宽度）旋转后仍然超宽时，系统会将其缩放至画布宽度的此百分比。较高的值会使图片更接近画布边缘，较低的值会留出更多边距。 |
| 超宽图片检测阈值 | 150% | 100-200% | 当图片宽度超过画布宽度的此百分比时，系统会在碰撞检测中强制返回碰撞，防止超宽图片被错误放置。较高的值允许更宽的图片通过正常处理，较低的值会使更多图片被视为超宽图片。 |

## 参数调优建议

1. **水平优先级**是影响C类图片排列效果最显著的参数。增加此值可以提高水平利用率，但可能会增加画布高度。

2. **空隙填充优先级**对减少垂直空隙非常重要。增加此值可以使算法更积极地填充现有空隙，创造更紧凑的布局。

3. **旋转优先级**对于处理宽图片很重要。对于宽度接近画布宽度的图片，系统会自动提高旋转优先级，但您也可以手动调整此参数以影响所有图片的旋转决策。

4. **超宽图片缩放比例**和**超宽图片检测阈值**是处理超宽图片的关键参数。如果您的图库中有许多超宽图片，可能需要调整这些参数以获得最佳效果。

5. **极端宽高比相关参数**主要影响特殊形状图片的处理。如果您的图片集中包含许多极端宽高比的图片，应特别关注这些参数。

## 自动调优

系统提供了自动调优功能，可以根据您的实际图片集自动寻找最优参数组合。自动调优会考虑以下因素：

1. **水平利用率**：每一行的平均利用率，权重为50%
2. **画布利用率**：整个画布的利用率，权重为20%
3. **处理速度**：每秒处理图片数量，权重为20%
4. **成功率**：成功放置图片的比例，权重为10%

要运行自动调优，可以使用以下命令：

```bash
python tests/run_auto_tuning.py --width 1600 --count 50 --method advanced --output-dir results
```

```bash
python tests/run_auto_tuning.py --width 1600 --count 100 --method advanced --output-dir results
```


自动调优完成后，最佳参数会保存在`results/best_params.json`文件中，并可以通过`apply_best_params.py`工具应用到系统配置中。

## 注意事项

1. 这些参数相互影响，调整一个参数可能会影响其他参数的效果。

2. 参数的最佳值可能因图片集的特性而异。具有不同尺寸分布的图片集可能需要不同的参数设置。

3. 在生产环境中应用新参数前，建议先在测试环境中验证效果。

4. 极端参数值（接近0%或100%）可能会导致意外的排列结果或性能问题。

5. 如果您不确定如何设置参数，建议使用自动调优功能或保持默认值。
