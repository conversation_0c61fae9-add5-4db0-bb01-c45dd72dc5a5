# 配置系统说明

## DuckDB配置系统

本应用使用DuckDB数据库作为配置存储系统，替代了之前的JSON文件配置方式。

### 主要优势

1. **更好的数据一致性**：使用事务确保配置更新的原子性
2. **类型支持**：自动保存和恢复适当的数据类型
3. **扩展性**：便于添加新的配置项和管理多组配置
4. **与图库索引统一**：使用同样的DuckDB技术，保持技术栈统一
5. **更好的验证**：内置数据验证和错误处理

### 配置项说明

目前系统中包含以下主要配置：

| 配置键 | 类型 | 描述 | 默认值 |
|-------|-----|------|------|
| canvas_width_m | float | 画布宽度（米） | 2.0 |
| max_height_cm | int | 最大高度（厘米） | 1000 |
| ppi | int | 每英寸像素数 | 72 |
| image_spacing_cm | float | 图像间距（厘米） | 1.0 |
| horizontal_expansion_cm | float | 水平扩展（厘米） | 0 |
| last_library_path | string | 上次使用的图库路径 | "" |
| last_material_folder | string | 上次使用的材质文件夹 | "" |
| use_photoshop | bool | 是否使用Photoshop | true |
| auto_start_photoshop | bool | 是否自动启动Photoshop | true |
| save_format | string | 保存格式 | "TIFF" |
| compression | string | 压缩方式 | "LZW" |

### Supabase同步

系统支持从Supabase云平台自动同步配置：

1. 应用启动时会尝试从Supabase获取最新配置
2. 获取的配置会自动与本地DuckDB配置合并
3. 支持的远程配置项包括：
   - img_ppi -> ppi
   - wide_add -> horizontal_expansion_cm
   - img_gap -> image_spacing_cm
   - max_height -> max_height_cm

### 首次迁移

如果检测到之前的config.json文件但没有DuckDB配置数据库，系统会自动执行迁移：

1. 读取旧的JSON配置
2. 将所有配置项迁移到DuckDB数据库
3. 将原JSON文件备份为config.json.bak

### 开发者接口

配置管理器提供以下主要接口：

```python
# 初始化配置管理器
config_manager = ConfigManagerDuckDB()

# 获取和设置配置
value = config_manager.get('key', default_value)
config_manager.set('key', value)

# 批量更新配置
config_manager.update({'key1': value1, 'key2': value2})

# 从Supabase同步配置
config_manager.sync_from_supabase()

# 获取特定组配置
canvas_settings = config_manager.get_canvas_settings()
ps_settings = config_manager.get_photoshop_settings()
``` 