# DeAI-智能排版 产品说明

## 1. 产品简介

DeAI-智能排版是一款专为设计师和印刷行业开发的智能排版工具，能够自动创建指定宽度的画布，从图库中检索图片并进行智能排列，最终保存为高质量的TIFF格式文件。本产品大幅提高设计和排版效率，是印刷行业的理想助手。

## 2. 产品特性

### 2.1 核心功能

- **智能图库索引**: 快速索引和检索本地图库中的图片资源
- **自动排版布局**: 使用优化的二维装箱算法，实现画布空间的最大化利用
- **Photoshop集成**: 无缝集成Adobe Photoshop，提供专业级图像处理能力
- **批量处理**: 支持批量处理多个材质和图案，提高工作效率
- **高度可配置**: 提供丰富的参数设置，满足不同场景的需求
- **内存优化**: 针对大型图片集进行了内存使用优化，提高稳定性

### 2.2 技术优势

- **先进排版算法**: 采用增强版二维装箱算法和俄罗斯方块式算法，实现最优排版
- **智能图像分类**: 根据图像尺寸与画布关系，自动分类并应用不同算法
- **高效内存管理**: 优化的内存使用策略，支持大规模图片处理
- **云端配置同步**: 支持从云端同步最新配置，保持应用最新状态
- **专业输出质量**: 通过Photoshop输出专业级TIFF文件，满足印刷需求

## 3. 适用场景

### 3.1 印刷行业

- **布料印刷**: 排版布料图案，最大化利用印刷面积
- **壁纸设计**: 排列壁纸图案，创建连续的设计
- **包装设计**: 优化包装图案排列，减少材料浪费

### 3.2 设计工作室

- **产品目录**: 快速创建产品图片目录
- **样品展示**: 高效排版样品图片
- **设计集合**: 整理和展示设计作品集

### 3.3 制造业

- **零部件图纸**: 排版工业零部件图纸
- **产品标签**: 批量处理产品标签设计
- **材料优化**: 最大化材料利用率

## 4. 使用指南

### 4.1 基本流程

1. **选择图库**
   - 点击"选择图库"按钮
   - 选择包含图片资源的文件夹
   - 系统将记住上次使用的图库路径

2. **索引图库**
   - 点击"索引图库"按钮
   - 系统将扫描并索引所有图片资源
   - 索引过程中显示进度和状态

3. **选择材质文件夹**
   - 点击"选择材质文件夹"按钮
   - 选择包含Excel材质表格的文件夹
   - 系统将记住上次使用的材质文件夹路径

4. **智能排版**
   - 点击"智能排版"按钮
   - 系统将自动检索图片并进行智能排版
   - 排版过程中显示进度和状态

5. **查看结果**
   - 排版完成后，TIFF文件将保存在指定位置
   - 日志区域显示详细的处理信息
   - 可以直接打开输出文件夹查看结果

### 4.2 高级设置

点击"高级设置"按钮可以配置以下参数：

#### 画布设置

- **画布宽度**: 设置画布的宽度（默认为2米）
- **PPI**: 设置图像的每英寸像素数（默认为72）
- **图片间距**: 设置图片之间的间距（默认为0.1厘米）
- **水平扩展**: 设置画布的水平扩展值（默认为0厘米）
- **最大高度**: 设置画布的最大高度（默认为5000厘米）

#### Photoshop设置

- **使用Photoshop**: 是否使用Photoshop处理图像
- **自动启动Photoshop**: 是否在需要时自动启动Photoshop
- **保存格式**: 选择保存格式（TIFF或JPEG）
- **压缩方式**: 选择压缩方式（LZW、ZIP或JPEG）

#### 排列设置

- **精确查询图案全称**: 是否仅精确查询"图案全称"字段
- **图片分类参数**: 设置A类和B类图片的分类阈值
- **旋转决策参数**: 控制图片旋转的决策阈值
- **行空隙填充参数**: 设置行空隙填充的参数
- **C类算法参数**: 设置俄罗斯方块式算法的参数

## 5. 图像分类与排版算法

### 5.1 图像分类

系统根据图像尺寸与画布宽度的关系，将图像分为三类：

1. **A类（宽幅类）**
   - 当图像任一边长与画布宽度相等或达到95%以上时，归为A类
   - A类图像占满一整行
   - 当图像长边与画布宽度相近时，直接排列
   - 当图像短边与画布宽度相近时，图像旋转90度竖着放置

2. **B类（宽幅约束类）**
   - 当图像任一边长的倍数与画布宽度相等或达到95%以上时，归为B类
   - B类图像按组排列，每组占满一整行
   - 当图像长边的倍数与画布宽度相近时，直接排列
   - 当图像短边的倍数与画布宽度相近时，图像旋转90度竖着放置
   - 对于超宽图片（宽度超过画布宽度1.5倍），会单独处理，避免与其他图片重叠

3. **C类（其他图像）**
   - 不符合A类和B类条件的图像归为C类
   - C类图像使用俄罗斯方块式算法排列，最大化利用空间
   - 对于超宽图片，会自动旋转或缩放以适应画布宽度

### 5.2 排版算法

系统使用两种核心算法进行图像排版：

1. **增强版二维装箱算法 (EnhancedBinPacker)**
   - 用于A类和B类图像的排列
   - 智能行策略：根据图像尺寸决定是单独占一行还是多个图像共享一行
   - 前瞻性决策：考虑未来将要放置的图像，做出更优的当前决策
   - 自适应布局：根据图像特性动态调整布局策略
   - 高空间利用率：减少画布高度，提高空间利用率

2. **俄罗斯方块式算法 (TetrisPacker)**
   - 用于C类图像的排列
   - 最大化水平利用率：类似俄罗斯方块，从左到右、从上到下填充
   - 智能旋转决策：根据空间情况决定是否旋转图片
   - 空隙填充：识别并填充现有行中的空隙
   - 智能重分配：在排列完成后，尝试将底部图片移动到中间空隙处
   - 超宽图片处理：自动旋转或缩放超宽图片，确保适应画布宽度
   - 旋转优先级动态调整：对于接近画布宽度的图片，自动提高旋转优先级

## 6. 输出文件说明

### 6.1 TIFF文件

系统输出的TIFF文件具有以下特点：

- **命名格式**: 材质-宽幅-序号.TIFF
- **分辨率**: 根据设置的PPI值（默认72PPI）
- **色彩模式**: RGB模式
- **压缩方式**: 可选LZW、ZIP或JPEG压缩
- **嵌入颜色配置**: 是
- **图层**: 无（扁平化）

### 6.2 日志文件

系统会为每个TIFF文件创建对应的日志文件，包含以下信息：

- **画布信息**: 尺寸、分辨率、间距等
- **图像列表**: 包含每个图像的名称、位置、尺寸等
- **利用率**: 画布空间利用率
- **处理时间**: 排版和处理的时间
- **算法参数**: 使用的算法和参数

## 7. 系统要求

### 7.1 基本要求

- **操作系统**: Windows 10/11 (64位)
- **Python版本**: Python 3.8+
- **内存**: 至少8GB RAM，推荐16GB或更高
- **存储空间**: 至少1GB可用空间（不包括图库存储）

### 7.2 可选要求

- **Adobe Photoshop**: CS3 ~ CC 2024版本（推荐使用CC 2020或更高版本）

## 8. 常见问题

### 8.1 程序无法启动Photoshop怎么办？

- 确保已正确安装Photoshop，并且已安装photoshop-python-api库
- 尝试手动启动Photoshop，然后使用"启动Photoshop"按钮进行连接
- 检查Photoshop版本是否兼容（支持CS3 ~ CC 2024）

### 8.2 图片排版不理想怎么调整？

- 通过"高级设置"调整图片间距和水平扩展参数
- 调整图片分类参数，改变A类和B类图片的分类阈值
- 调整C类算法参数，如水平优先级、空隙填充优先级等
- 考虑调整图片尺寸，使其更适合画布宽度

### 8.3 处理大量图片时程序崩溃怎么办？

- 尝试减小批处理的图片数量
- 增加系统内存
- 确保已启用最新的内存优化功能
- 关闭其他内存占用大的应用程序

### 8.4 如何提高画布利用率？

- 调整图片间距，减少间距可提高利用率
- 优化图片尺寸，使其更适合画布宽度
- 调整C类算法参数，提高水平优先级和空隙填充优先级
- 启用智能重分配功能，优化整体布局

### 8.5 如何处理特殊尺寸的图片？

- A类和B类图片会自动处理特殊尺寸
- 对于极端宽高比的图片，可以调整旋转决策参数
- 考虑预处理图片，调整尺寸使其更适合排版

## 9. 技术支持

如有任何问题或建议，请通过以下方式联系我们：

- **邮箱**: <EMAIL>
- **问题追踪**: [提交Issue](https://gitee.com/your-username/robot_ps_smart/issues)

## 10. 版权声明

© 2023-2024 DeAI-智能排版团队。保留所有权利。

本产品受版权法和国际版权条约保护。未经授权，不得复制、分发或传播本产品的任何部分。

Adobe和Photoshop是Adobe Inc.在美国和/或其他国家的注册商标或商标。
