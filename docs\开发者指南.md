# DeAI-智能排版 开发者指南

## 1. 开发环境搭建

### 1.1 基础环境准备

1. **安装Python 3.8+**
   - 从[Python官网](https://www.python.org/downloads/)下载并安装Python 3.8或更高版本
   - 安装时勾选"Add Python to PATH"选项

2. **安装Git**
   - 从[Git官网](https://git-scm.com/downloads)下载并安装Git
   - 配置基本的用户信息
     ```bash
     git config --global user.name "Your Name"
     git config --global user.email "<EMAIL>"
     ```

3. **安装IDE**
   - 推荐使用[Visual Studio Code](https://code.visualstudio.com/)或[PyCharm](https://www.jetbrains.com/pycharm/)
   - 安装Python相关插件（如Python, Pylance等）

### 1.2 获取代码

```bash
# 克隆仓库
git clone https://gitee.com/peixiaolei/robot_ps_smart

# 进入项目目录
cd robot_ps_smart
```

### 1.3 安装依赖

```bash
# 安装基础依赖
pip install pillow numpy pyqt6 pandas duckdb

# 安装Photoshop API依赖
pip install photoshop-python-api

# 安装云服务依赖
pip install supabase

# 安装开发工具依赖
pip install pytest pytest-qt mypy pylint black
```

### 1.4 配置开发环境

1. **配置VSCode设置**
   - 创建`.vscode/settings.json`文件
   ```json
   {
     "python.linting.enabled": true,
     "python.linting.pylintEnabled": true,
     "python.formatting.provider": "black",
     "python.formatting.blackArgs": ["--line-length", "100"],
     "editor.formatOnSave": true,
     "python.analysis.typeCheckingMode": "basic"
   }
   ```

2. **配置PyCharm设置**
   - 设置项目解释器为Python 3.8+
   - 启用PEP 8代码风格检查
   - 配置Black作为代码格式化工具

## 2. 项目结构详解

### 2.1 目录结构

```
robot_ps_smart/
├── core/                   # 核心算法和功能模块
│   ├── excel_processor.py      # Excel处理器
│   ├── image_classifier.py     # 图像分类器
│   ├── image_indexer_duckdb.py # 图像索引器
│   └── rectpack_arranger.py    # RectPack矩形装箱算法
├── docs/                   # 文档目录
│   ├── config_system.md        # 配置系统说明
│   ├── 技术架构.md              # 技术架构文档
│   ├── 项目说明.md              # 项目说明文档
│   └── 产品说明.md              # 产品说明文档
├── ui/                     # 用户界面组件
│   ├── index_library_worker.py # 索引库工作线程
│   ├── layout_worker.py        # 布局工作线程
│   ├── password_dialog.py      # 密码对话框
│   ├── retrieve_images_worker.py # 检索图像工作线程
│   └── settings_dialog.py      # 设置对话框
├── utils/                  # 工具类和辅助功能
│   ├── advanced_settings_auth.py # 高级设置授权
│   ├── config_manager_duckdb.py  # 配置管理器
│   ├── json_to_duckdb_migrator.py # JSON到DuckDB迁移工具
│   ├── log_file_creator.py       # 日志文件创建器
│   ├── photoshop_helper.py       # Photoshop辅助工具
│   └── supabase_helper.py        # Supabase云端同步
├── tests/                  # 测试目录
│   ├── test_excel_processor.py  # Excel处理器测试
│   └── test_image_indexer.py    # 图像索引器测试
├── robot_ps_smart_app.py   # 应用主程序
└── README.md               # 项目说明文件
```

### 2.2 核心模块说明

#### 2.2.1 图像索引器 (image_indexer_duckdb.py)

图像索引器负责扫描图像库，建立索引，支持快速检索。主要类和方法：

```python
class ImageIndexerDuckDB(QObject):
    # 信号定义
    progress_signal = pyqtSignal(int)  # 进度值 (0-100)
    status_signal = pyqtSignal(str)    # 状态信息
    error_signal = pyqtSignal(str)     # 错误信息

    def __init__(self):
        """初始化图片索引器"""

    def init_database(self, library_path):
        """初始化数据库"""

    def index_library(self, library_path):
        """索引图库"""

    def find_image(self, pattern_name, exact_match=False):
        """查找图片"""

    def close(self):
        """关闭数据库连接"""
```

#### 2.2.2 Excel处理器 (excel_processor.py)

Excel处理器负责读取Excel材质表格，提取图案信息。主要类和方法：

```python
class ExcelProcessor(QObject):
    # 信号定义
    progress_signal = pyqtSignal(int)  # 进度值 (0-100)
    status_signal = pyqtSignal(str)    # 状态信息
    error_signal = pyqtSignal(str)     # 错误信息

    def __init__(self):
        """初始化Excel处理器"""

    def process_material_folder(self, folder_path, image_indexer, exact_pattern_search=False):
        """处理材质表格文件夹"""

    def process_excel_file(self, file_path, image_indexer, exact_pattern_search=False):
        """处理单个Excel文件"""
```

#### 2.2.3 图像分类器 (image_classifier.py)

图像分类器负责将图像分为A类、B类和C类。主要类和方法：

```python
class ImageClassifier:
    def __init__(self, log_signal=None):
        """初始化图像分类器"""

    def classify_images(self, pattern_items, canvas_width_cm, class_a_threshold=0.95, class_b_error_range=0.05):
        """分类图像"""

    def process_b_class_groups(self, class_b_patterns, canvas_width_cm):
        """处理B类图像分组"""

    def sort_c_class_patterns(self, class_c_patterns, canvas_width_cm):
        """排序C类图像"""
```

#### 2.2.4 增强版装箱算法 (enhanced_bin_packer.py)

增强版装箱算法用于A类和B类图像的排列。主要类和方法：

```python
class EnhancedBinPacker:
    def __init__(self, container_width, image_spacing=0):
        """初始化增强版装箱器"""

    def add_image(self, width, height, image_data=None, future_items=None):
        """添加图像"""

    def find_position(self, width, height, image_data=None):
        """查找位置"""

    def get_layout_info(self):
        """获取布局信息"""

    def optimize_layout(self, items=None):
        """优化布局"""
```

#### 2.2.5 RectPack矩形装箱算法 (rectpack_arranger.py)

RectPack算法用于所有图像的统一排列。主要类和方法：

```python
class RectPackArranger:
    def __init__(self, bin_width, bin_height=0, image_spacing=0):
        """初始化RectPack装箱器"""

    def place_image(self, width, height, image_data=None):
        """放置图像"""

    def get_layout_info(self):
        """获取布局信息"""

    def optimize_layout(self):
        """优化布局"""
```

### 2.3 工具模块说明

#### 2.3.1 配置管理器 (config_manager_duckdb.py)

配置管理器负责管理应用配置。主要类和方法：

```python
class ConfigManagerDuckDB:
    def __init__(self, db_file="config.db"):
        """初始化配置管理器"""

    def get(self, key, default=None):
        """获取配置值"""

    def set(self, key, value):
        """设置配置值"""

    def update(self, config_dict):
        """批量更新配置"""

    def sync_from_supabase(self):
        """从Supabase同步配置"""

    def get_canvas_settings(self):
        """获取画布设置"""

    def get_photoshop_settings(self):
        """获取Photoshop设置"""

    def get_algorithm_settings(self):
        """获取算法设置"""
```

#### 2.3.2 Photoshop辅助工具 (photoshop_helper.py)

Photoshop辅助工具负责与Photoshop交互。主要类和方法：

```python
class PhotoshopHelper:
    @staticmethod
    def check_photoshop():
        """检查Photoshop是否可用"""

    @staticmethod
    def start_photoshop():
        """启动Photoshop"""

    @staticmethod
    def purge_memory():
        """清理内存"""

    @staticmethod
    def create_canvas(width, height, name, ppi=72):
        """创建画布"""

    @staticmethod
    def place_image(image_path, x, y, width, height, rotate=False):
        """放置图像"""

    @staticmethod
    def save_document(file_path, format='TIFF'):
        """保存文档"""

    @staticmethod
    def close_all_documents(save=False):
        """关闭所有文档"""
```

## 3. 开发流程

### 3.1 功能开发流程

1. **创建功能分支**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **编写代码**
   - 遵循PEP 8编码规范
   - 使用类型注解
   - 编写详细的文档字符串
   - 保持模块的高内聚低耦合

3. **编写测试**
   - 为新功能编写单元测试
   - 确保测试覆盖率达到要求

4. **本地测试**
   ```bash
   # 运行单元测试
   pytest tests/

   # 运行类型检查
   mypy core/ utils/ ui/

   # 运行代码风格检查
   pylint core/ utils/ ui/
   ```

5. **提交代码**
   ```bash
   git add .
   git commit -m "feat: 添加新功能描述"
   ```

6. **推送分支**
   ```bash
   git push origin feature/your-feature-name
   ```

7. **创建Pull Request**
   - 在Gitee上创建Pull Request
   - 等待代码审查和合并

### 3.2 Bug修复流程

1. **创建修复分支**
   ```bash
   git checkout -b fix/bug-description
   ```

2. **修复Bug**
   - 定位Bug原因
   - 编写修复代码
   - 添加测试用例防止回归

3. **本地测试**
   ```bash
   # 运行单元测试
   pytest tests/
   ```

4. **提交代码**
   ```bash
   git add .
   git commit -m "fix: 修复Bug描述"
   ```

5. **推送分支**
   ```bash
   git push origin fix/bug-description
   ```

6. **创建Pull Request**
   - 在Gitee上创建Pull Request
   - 等待代码审查和合并

## 4. 编码规范

### 4.1 Python编码规范

- 遵循[PEP 8](https://www.python.org/dev/peps/pep-0008/)编码规范
- 使用4个空格缩进
- 行长度不超过100个字符
- 使用类型注解提高代码可读性
- 编写详细的文档字符串

### 4.2 命名规范

- **类名**: 使用驼峰命名法，如`ImageIndexer`
- **方法名和变量名**: 使用小写字母和下划线，如`index_library`
- **常量**: 使用大写字母和下划线，如`MAX_HEIGHT`
- **私有方法和变量**: 使用下划线前缀，如`_connect_db`

### 4.3 文档字符串规范

使用Google风格的文档字符串：

```python
def function_name(param1, param2):
    """函数简短描述

    详细描述，可以多行。

    Args:
        param1: 参数1的描述
        param2: 参数2的描述

    Returns:
        返回值的描述

    Raises:
        ValueError: 异常描述
    """
```

### 4.4 注释规范

- 使用中文注释，确保清晰明了
- 注释应该解释"为什么"而不是"是什么"
- 复杂的算法或逻辑应该有详细的注释
- 避免无用或过时的注释

## 5. 测试指南

### 5.1 单元测试

使用pytest编写单元测试：

```python
# tests/test_bin_packer.py
import pytest
from core.enhanced_bin_packer import EnhancedBinPacker

def test_add_image():
    """测试添加图像功能"""
    bin_packer = EnhancedBinPacker(container_width=1000, image_spacing=5)
    result = bin_packer.add_image(width=100, height=100)
    assert result is not None
    assert len(bin_packer.items) == 1
```

### 5.2 UI测试

使用pytest-qt编写UI测试：

```python
# tests/test_settings_dialog.py
import pytest
from PyQt6.QtCore import Qt
from ui.settings_dialog import SettingsDialog

def test_settings_dialog(qtbot):
    """测试设置对话框"""
    dialog = SettingsDialog()
    qtbot.addWidget(dialog)

    # 测试初始值
    assert dialog.canvas_width.value() == 2.0

    # 测试修改值
    dialog.canvas_width.setValue(3.0)
    assert dialog.canvas_width.value() == 3.0
```

### 5.3 运行测试

```bash
# 运行所有测试
pytest

# 运行特定测试文件
pytest tests/test_bin_packer.py

# 运行特定测试函数
pytest tests/test_bin_packer.py::test_add_image

# 生成测试覆盖率报告
pytest --cov=core --cov=utils --cov=ui
```

## 6. 调试技巧

### 6.1 日志调试

使用logging模块进行日志调试：

```python
import logging

# 配置日志
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
log = logging.getLogger("YourModule")

# 使用日志
log.debug("调试信息")
log.info("一般信息")
log.warning("警告信息")
log.error("错误信息")
log.critical("严重错误信息")
```

### 6.2 断点调试

在VSCode中使用断点调试：

1. 在代码行号前点击设置断点
2. 按F5启动调试
3. 使用F10逐行执行，F11步入函数，F9继续执行

### 6.3 性能分析

使用cProfile进行性能分析：

```python
import cProfile

# 分析函数性能
cProfile.run('your_function()', 'profile_stats')

# 分析结果
import pstats
p = pstats.Stats('profile_stats')
p.sort_stats('cumulative').print_stats(20)
```

## 7. 发布流程

### 7.1 版本号规范

使用语义化版本号：`主版本号.次版本号.修订号`

- **主版本号**: 不兼容的API修改
- **次版本号**: 向下兼容的功能性新增
- **修订号**: 向下兼容的问题修正

### 7.2 发布步骤

1. **更新版本号**
   - 在`robot_ps_smart_app.py`中更新`APP_VERSION`

2. **更新CHANGELOG**
   - 记录版本更新内容

3. **创建发布分支**
   ```bash
   git checkout -b release/v1.x.x
   ```

4. **提交更改**
   ```bash
   git add .
   git commit -m "chore: 准备发布v1.x.x"
   ```

5. **创建标签**
   ```bash
   git tag -a v1.x.x -m "版本v1.x.x"
   ```

6. **推送到远程仓库**
   ```bash
   git push origin release/v1.x.x
   git push origin v1.x.x
   ```

7. **合并到主分支**
   - 在Gitee上创建Pull Request
   - 合并到主分支

8. **构建发布包**
   ```bash
   # 使用PyInstaller构建可执行文件
   pyinstaller --onefile --windowed robot_ps_smart_app.py
   ```

9. **发布到Gitee**
   - 在Gitee上创建新的Release
   - 上传构建好的可执行文件

## 8. 常见问题与解决方案

### 8.1 开发环境问题

#### Q: 安装依赖时出现错误

A: 尝试使用以下命令更新pip并重新安装依赖：
```bash
python -m pip install --upgrade pip
pip install -r requirements.txt
```

#### Q: PyQt6安装失败

A: 确保已安装Visual C++ 构建工具，或尝试使用预编译的wheel：
```bash
pip install PyQt6 --only-binary=PyQt6
```

### 8.2 代码问题

#### Q: DuckDB操作失败

A: 检查数据库连接是否正确初始化，以及是否有并发访问问题：
```python
# 确保在使用前连接数据库
if self.db is None:
    self._connect_db()
```

#### Q: Photoshop API调用失败

A: 检查Photoshop是否正在运行，以及版本是否兼容：
```python
# 先检查Photoshop状态
is_available, message = PhotoshopHelper.check_photoshop()
if not is_available:
    log.error(f"Photoshop不可用: {message}")
    return False
```

### 8.3 算法问题

#### Q: 图像排列效果不理想

A: 调整算法参数，如行利用率阈值、宽度阈值比例等：
```python
# 调整参数示例
bin_packer.width_threshold_ratio = 0.75  # 降低宽度阈值，减少单独占行的图像
bin_packer.min_row_utilization = 0.65    # 降低行利用率要求，允许更多空间浪费
```

## 9. 性能优化指南

### 9.1 数据库优化

- **批量操作**: 使用批量插入而不是单条插入
- **索引优化**: 为常用查询字段创建索引
- **连接管理**: 及时关闭不需要的数据库连接

### 9.2 内存优化

- **大文件处理**: 使用分块读取处理大文件
- **图像处理**: 及时释放不需要的图像资源
- **缓存管理**: 定期清理缓存数据

### 9.3 算法优化

- **算法选择**: 根据数据特性选择合适的算法
- **参数调优**: 根据实际情况调整算法参数
- **并行处理**: 利用多线程处理独立任务

## 10. 扩展开发指南

### 10.1 添加新的排版算法

1. **创建算法类**
   - 在`core`目录下创建新的算法文件
   - 实现必要的接口方法

2. **集成到现有系统**
   - 在`layout_worker.py`中添加新算法的支持
   - 在设置对话框中添加算法选择选项

3. **编写测试**
   - 为新算法编写单元测试
   - 进行性能比较测试

### 10.2 添加新的UI功能

1. **设计UI组件**
   - 使用Qt Designer设计UI界面
   - 或直接编写PyQt6代码

2. **实现功能逻辑**
   - 编写事件处理函数
   - 连接信号和槽

3. **集成到主界面**
   - 在主界面中添加新组件
   - 处理组件间的交互

### 10.3 添加新的配置项

1. **更新配置管理器**
   - 在`config_manager_duckdb.py`中添加新配置项
   - 设置默认值和类型

2. **更新设置对话框**
   - 在`settings_dialog.py`中添加新配置项的UI控件
   - 实现配置项的读取和保存

3. **使用新配置项**
   - 在相关模块中使用新配置项
   - 确保配置项的类型安全
