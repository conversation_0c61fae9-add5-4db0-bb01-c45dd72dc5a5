# DeAI-智能排版 技术架构

## 1. 系统架构概述

DeAI-智能排版是一款基于Python开发的桌面应用程序，采用模块化设计，主要由以下几个核心组件构成：

```
┌─────────────────────────────────────────────────────────────┐
│                      应用主程序层                            │
│                  robot_ps_smart_app.py                      │
└───────────────┬─────────────────┬───────────────────────────┘
                │                 │
                ▼                 ▼
┌───────────────────────┐ ┌─────────────────────────────────┐
│       核心算法层       │ │           工具层                │
│                       │ │                                 │
│ ├─ 图像索引器         │ │ ├─ 配置管理器                   │
│ ├─ Excel处理器        │ │ ├─ Photoshop辅助工具            │
│ ├─ 图像分类器         │ │ ├─ Supabase云端同步             │
│ ├─ 增强版装箱算法     │ │ ├─ 日志文件创建器               │
│ └─ 俄罗斯方块式算法   │ │ └─ 高级设置授权                 │
└───────────────────────┘ └─────────────────────────────────┘
                ▲                 ▲
                │                 │
                ▼                 ▼
┌───────────────────────┐ ┌─────────────────────────────────┐
│        UI组件层        │ │          数据存储层             │
│                       │ │                                 │
│ ├─ 设置对话框         │ │ ├─ DuckDB数据库                 │
│ ├─ 密码对话框         │ │ ├─ 图像索引数据                 │
│ ├─ 布局工作线程       │ │ ├─ 配置数据                     │
│ ├─ 索引库工作线程     │ │ └─ Supabase云端数据             │
│ └─ 检索图像工作线程   │ │                                 │
└───────────────────────┘ └─────────────────────────────────┘
```

## 2. 核心技术栈

### 2.1 前端技术

- **PyQt6**: 用于构建跨平台桌面应用界面
- **多线程处理**: 使用Qt的QThread实现后台任务处理，保持UI响应性
- **信号槽机制**: 实现组件间的松耦合通信

### 2.2 后端技术

- **Python 3.8+**: 核心编程语言
- **DuckDB**: 嵌入式分析型数据库，用于图像索引和配置管理
- **Pandas**: 用于Excel文件处理和数据分析
- **Photoshop Python API**: 与Adobe Photoshop进行交互
- **Supabase**: 云端数据同步和配置管理

### 2.3 算法技术

- **图像分类算法**: 基于图像尺寸与画布关系的智能分类
- **增强版二维装箱算法**: 用于A类和B类图像的高效排列
- **俄罗斯方块式算法**: 用于C类图像的最优排列，最大化水平空间利用率，包含超宽图片处理优化

## 3. 数据流架构

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│  图像库索引  │ => │ Excel数据处理│ => │ 图像分类    │
└─────────────┘    └─────────────┘    └─────────────┘
                                             │
                                             ▼
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│ TIFF文件生成 │ <= │ Photoshop处理│ <= │ 图像排列算法 │
└─────────────┘    └─────────────┘    └─────────────┘
```

## 4. 模块详解

### 4.1 图像索引模块 (ImageIndexerDuckDB)

- **功能**: 扫描图像库，建立索引，支持快速检索
- **技术特点**:
  - 使用DuckDB作为后端存储
  - 批处理机制，支持大规模图库
  - 多级索引策略，优化检索性能
  - 规范化文件名处理，支持中文路径

### 4.2 Excel处理模块 (ExcelProcessor)

- **功能**: 读取Excel材质表格，提取图案信息
- **技术特点**:
  - 支持多种Excel格式
  - 数据验证和错误处理
  - 批量处理能力
  - 精确查询模式支持

### 4.3 图像分类模块 (ImageClassifier)

- **功能**: 将图像分为A类、B类和C类
- **分类规则**:
  - A类(宽幅类): 图像边长与画布宽度接近
  - B类(宽幅约束类): 图像边长的倍数与画布宽度接近
  - C类(其他图像): 不符合A类和B类条件的图像

### 4.4 增强版装箱算法 (EnhancedBinPacker)

- **功能**: 处理A类和B类图像的排列
- **技术特点**:
  - 智能行策略
  - 前瞻性决策
  - 自适应布局
  - 高空间利用率

### 4.5 RectPack矩形装箱算法 (RectPackArranger)

- **功能**: 统一处理所有图像的排列
- **技术特点**:
  - 基于专业的矩形装箱算法
  - 最大化画布利用率
  - 智能旋转决策
  - 多种装箱策略
  - 自适应优化
  - 简化的统一流程

### 4.6 配置管理模块 (ConfigManagerDuckDB)

- **功能**: 管理应用配置
- **技术特点**:
  - 使用DuckDB存储配置
  - 支持类型安全的配置
  - 云端配置同步
  - 配置迁移功能

### 4.7 Photoshop辅助模块 (PhotoshopHelper)

- **功能**: 与Photoshop交互
- **技术特点**:
  - 自动检测和启动Photoshop
  - 高级JavaScript集成
  - 内存管理优化
  - 错误恢复机制

## 5. 数据库设计

### 5.1 图像索引数据库

```sql
CREATE TABLE image_files (
    id INTEGER PRIMARY KEY,
    filename VARCHAR NOT NULL,
    name_without_ext VARCHAR NOT NULL,
    extension VARCHAR NOT NULL,
    relative_path VARCHAR NOT NULL,
    full_path VARCHAR NOT NULL,
    has_chinese BOOLEAN NOT NULL,
    normalized_filename VARCHAR NOT NULL,
    normalized_name VARCHAR NOT NULL,
    file_size BIGINT NOT NULL,
    modified_time TIMESTAMP NOT NULL,
    created_time TIMESTAMP NOT NULL,
    indexed_time TIMESTAMP NOT NULL,
    UNIQUE(relative_path)
)
```

### 5.2 配置数据库

```sql
CREATE TABLE config (
    key VARCHAR PRIMARY KEY,
    value VARCHAR NOT NULL,
    value_type VARCHAR NOT NULL,
    updated_at TIMESTAMP NOT NULL
)
```

## 6. 多线程架构

应用采用多线程设计，确保UI响应性和后台任务高效执行：

1. **主线程**: 处理UI事件和用户交互
2. **索引线程**: 处理图像库索引任务
3. **检索线程**: 处理图像检索和Excel处理
4. **布局线程**: 处理图像排列和Photoshop交互

## 7. 云端集成

应用与Supabase云平台集成，提供以下功能：

1. **配置同步**: 从云端获取最新配置
2. **版本检查**: 确保应用版本最新
3. **高级设置授权**: 通过云端验证授权

## 8. 安全设计

1. **高级设置保护**: 使用密码保护高级设置
2. **配置数据安全**: 使用事务确保配置更新的原子性
3. **错误恢复机制**: 在处理失败时能够恢复

## 9. 扩展性设计

系统设计遵循以下原则，确保良好的扩展性：

1. **模块化设计**: 各组件高内聚低耦合
2. **接口抽象**: 定义清晰的模块接口
3. **配置驱动**: 通过配置调整系统行为
4. **插件架构**: 支持新算法和功能的插入

## 10. 性能优化

1. **批处理机制**: 减少数据库操作次数
2. **内存管理**: 定期清理Photoshop内存
3. **索引优化**: 多级索引提高检索速度
4. **并行处理**: 利用多线程提高性能
