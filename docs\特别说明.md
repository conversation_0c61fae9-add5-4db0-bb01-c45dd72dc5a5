# DeAI-智能排版 特别说明

## 0. 图库
### 1.1 图库图片声明

须提前声明，库里的全部图片都是默认横着放置的，跟图片名字没关系，即图片长边是水平方向，排列图片或旋转时，考虑到这个前提，不是要画布的图片横着放置，是指图库存放的图片，都是横着放置，与图片名字无关，根据边长和宽幅的关系，图片根据需要可旋转90度，让图片竖着放置，最大可能提高画布利用率

## 1. 图像分类与排版算法

### 1.1 图像分类规则

系统根据图像尺寸与画布宽度的关系，将图像分为三类：

1. **A类（宽幅类）**
   - 当图像任一边长与画布宽度相等或达到95%以上时，归为A类
   - A类图像占满一整行
   - 当图像长边与画布宽度相近时，直接排列
   - 当图像短边与画布宽度相近时，图像旋转90度竖着放置

2. **B类（宽幅约束类）**
   - 当图像任一边长的倍数与画布宽度相等或达到95%以上时，归为B类
   - B类图像按组排列，每组占满一整行
   - 当图像长边的倍数与画布宽度相近时，直接排列
   - 当图像短边的倍数与画布宽度相近时，图像旋转90度竖着放置
   - 对于超宽图片（宽度超过画布宽度1.5倍），会单独处理，避免与其他图片重叠

3. **C类（其他图像）**
   - 不符合A类和B类条件的图像归为C类
   - C类图像使用俄罗斯方块式算法排列，最大化利用空间
   - 对于超宽图片，会自动旋转或缩放以适应画布宽度

### 1.2 图像排列顺序

图像排列严格按照类别顺序进行：
1. 首先排列A类图像
2. 然后排列B类图像
3. 最后排列C类图像

多个类别的图像可以放在同一个画布上，但必须保持严格的排列顺序。

### 1.3 增强版二维装箱算法 (EnhancedBinPacker)

用于A类和B类图像的排列，具有以下特点：

- **智能行策略**：根据图像尺寸自动决定是单独占一行还是多个图像共享一行
- **前瞻性决策**：考虑未来将要放置的图像，做出更优的当前决策
- **自适应布局**：根据图像特性动态调整布局策略
- **高空间利用率**：减少画布高度，提高空间利用率
- **优先考虑利用率**：算法优化考虑利用率和高度的组合评分，利用率权重更高

### 1.4 RectPack矩形装箱算法 (RectPackArranger)

用于所有图像的统一排列，具有以下特点：

- **统一处理**：使用单一算法处理所有类型的图像
- **最优装箱**：基于专业的矩形装箱算法，实现最大化画布利用率
- **智能旋转**：自动优化图片旋转以提高空间利用率
- **多种策略**：支持多种装箱策略和排序方法
- **简化流程**：移除复杂的分类逻辑，统一的处理流程
- **高效率**：专业算法保证高画布利用率

## 2. RectPack算法优化说明

### 2.1 RectPack算法特点

RectPack算法具有以下优化特点：

#### 2.1.1 统一处理优化

- **自动旋转**：当图片宽度超过画布宽度时，自动尝试旋转90度
- **智能缩放**：当旋转后仍然超宽时，自动缩放至画布宽度的95%
- **碰撞检测优化**：对于超宽图片（宽度超过画布宽度1.5倍），强制返回碰撞，防止错误放置
- **旋转优先级提升**：对于宽度超过画布宽度80%的图片，自动提高旋转优先级至80%以上

- **优先考虑水平空间利用**：算法首先考虑水平空间利用率，然后再考虑垂直排列
- **保持水平方向**：默认保持图像在水平方向，不随意旋转90度
- **智能旋转决策**：只有当旋转能显著提高画布利用率时才进行旋转
- **填充右侧空间**：优化算法以填充右侧空间，避免中间出现空隙
- **改善水平利用率**：提高整体水平利用率，避免左侧出现大片空白
- **放置高天际线图像**：默认将高天际线图像水平放置，不旋转90度
- **创建更平坦的天际线**：优化布局以创建更平坦的天际线

### 2.2 B类图像处理优化

#### 2.2.1 B类图片超宽图片处理

- **单独处理**：对于B类图片组中的超宽图片（宽度超过画布宽度1.5倍），会单独处理每张图片
- **标准放置**：使用标准的find_position方法处理超宽图片，确保正确旋转或缩放
- **避免重叠**：特别处理B类和C类图片交界处的超宽图片，防止重叠和空白区域

#### 2.2.2 B类图像Tetris算法应用

B类图像也可以应用Tetris算法进行排列，同时保持其他逻辑不变：

- **保持B类图像分组**：B类图像仍然按组排列
- **应用Tetris算法**：使用Tetris算法优化B类图像在组内的排列
- **优化空间利用**：填充底部右侧和中间的空白空间
- **使用小图像填充**：必要时使用较小的图像填充空隙，最大化水平利用率

### 2.3 画布高度限制

系统会根据配置中的最大高度参数限制画布高度：

- **最大高度限制**：当画布高度达到最大限制时，剩余图像将放置在新画布上
- **智能分配**：系统会尝试平衡图像在画布间的分布，避免单个画布上只有少量图像
- **优化利用率**：在限制高度的同时，仍然尽可能优化画布利用率

## 3. 利用率计算

### 3.1 利用率计算公式

系统使用统一的利用率计算公式，适用于所有类型的图像：

```
利用率 = (图片面积 + 间距面积 + 水平扩展面积) / 画布面积
```

其中：
- **图片面积**：所有图片的面积总和
- **间距面积**：图片间距占用的面积
- **水平扩展面积**：水平扩展值占用的面积
- **画布面积**：画布宽度（包含水平扩展）× 画布高度

### 3.2 利用率显示精度

- 利用率显示保留2位小数，以提供清晰易读的数值
- 不对A类图像自动设置100%利用率，而是使用实际计算值
- 确保利用率不超过1.0（100%）
- 所有百分比格式统一为2位小数，例如：98.15%

### 3.3 水平利用率

系统还计算每一行的水平利用率：

```
水平利用率 = 行中已放置图片总宽度 / 画布宽度
```

这有助于优化C类图像的排列，最大化水平空间利用。

## 4. TIFF文件输出

### 4.1 文件命名规则

TIFF文件采用以下命名格式：

```
材质-宽幅-序号.TIFF
```

例如：`棉麻-200-1.TIFF`

其中：
- **材质**：材质名称
- **宽幅**：画布宽度（厘米），不包含水平扩展数据
- **序号**：自动递增的序号

### 4.2 TIFF文件特性

- **分辨率**：根据设置的PPI值（默认72PPI）
- **色彩模式**：RGB模式
- **压缩方式**：可选LZW、ZIP或JPEG压缩
- **嵌入颜色配置**：是
- **图层**：无（扁平化）

### 4.3 TIFF文档信息

每个TIFF文件都会生成对应的文档信息，包含：

- **画布信息**：尺寸、分辨率、间距等
- **图像列表**：包含每个图像的名称、位置、尺寸、利用率等
- **利用率统计**：
  - **画布利用率**：画布空间利用率（使用统一计算公式）
  - **行利用率**：每行的空间利用率
  - **平均行利用率**：所有行的平均利用率
  - **旋转率**：旋转图片数量占总图片数量的百分比
- **处理时间**：排版和处理的时间
- **算法参数**：使用的算法和参数
- **所有百分比**：统一显示为2位小数（例如：98.15%）

## 5. 高级设置与参数

### 5.1 算法参数存储

- 所有高级算法参数都保存在DuckDB数据库中
- 支持从云端同步最新配置
- RectPack算法参数已添加到高级设置中，并与DuckDB集成

### 5.2 关键算法参数

#### 5.2.1 RectPack算法参数

- **装箱算法**：选择使用的装箱算法（MaxRects、Skyline等）
- **排序策略**：图片排序策略（按面积、周长等）
- **旋转启用**：是否允许图片旋转
- **优化级别**：算法优化级别设置
- **最小利用率阈值**：最小画布利用率要求

#### 5.2.3 画布参数

- **画布宽度**：设置画布的宽度（默认为2米）
- **PPI**：设置图像的每英寸像素数（默认为72）
- **图片间距**：设置图片之间的间距（默认为0.1厘米）
- **水平扩展**：设置画布的水平扩展值（默认为0厘米）
- **最大高度**：设置画布的最大高度（默认为5000厘米）

## 6. 性能优化

### 6.1 内存管理优化

为了处理大型图片集，系统实现了以下内存优化策略：

- **定期清理内存**：在处理过程中定期清理Photoshop内存
- **关闭不需要的文档**：自动关闭不再需要的文档，减少内存占用
- **优化图片处理流程**：使用更高效的方式处理图片，减少内存占用
- **错误恢复机制**：当某个图片处理失败时能够继续处理其他图片

### 6.2 算法性能优化

- **布局计算优化**：在排列图像的同时计算布局，提高性能
- **批处理机制**：减少数据库操作次数，提高性能
- **并行处理**：利用多线程提高性能
- **索引优化**：多级索引提高检索速度

## 7. 特殊注意事项

### 7.1 图像旋转说明

- 图库中的图片都是横着放置的（长边水平），与图片名字无关
- RectPack算法会智能决定是否旋转图片以提高利用率
- 只有当旋转能显著提高画布利用率时才进行旋转

### 7.2 画布分配平衡

- 系统会尝试平衡图像在画布间的分布
- 避免单个画布上只有少量图像（个位数）
- 优化画布数量，减少不必要的画布生成

### 7.3 中文文件名处理

- 系统支持中文文件名和路径
- 对于中文文件名，会保留原始文件名作为备用索引
- 如果规范化后文件名为空，会使用原始文件名的哈希值代替

### 7.4 错误恢复机制

- 当某个图片处理失败时，系统会记录错误并继续处理其他图片
- 对于无法放置的图片，会尝试在新画布上继续排列
- 系统会生成详细的错误日志，便于排查问题

## 8. 开发者特别说明

### 8.1 代码规范

- 遵循PEP 8编码规范
- 使用4个空格缩进
- 行长度不超过100个字符
- 使用类型注解提高代码可读性
- 编写详细的文档字符串

### 8.2 命名规范

- **类名**: 使用驼峰命名法，如`ImageIndexer`
- **方法名和变量名**: 使用小写字母和下划线，如`index_library`
- **常量**: 使用大写字母和下划线，如`MAX_HEIGHT`
- **私有方法和变量**: 使用下划线前缀，如`_connect_db`

### 8.3 文档字符串规范

使用Google风格的文档字符串：

```python
def function_name(param1, param2):
    """函数简短描述

    详细描述，可以多行。

    Args:
        param1: 参数1的描述
        param2: 参数2的描述

    Returns:
        返回值的描述

    Raises:
        ValueError: 异常描述
    """
```

### 8.4 模块化设计

- 系统采用模块化设计，各组件高内聚低耦合
- 定义清晰的模块接口，便于扩展和维护
- 通过配置调整系统行为，减少硬编码
- 支持新算法和功能的插入
