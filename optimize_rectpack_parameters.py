#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RectPack算法参数优化脚本

全面优化RectPack算法参数，实现画布最大化利用率
包括算法选择、排序策略、旋转优化等多维度优化

作者: RectPack算法优化团队
日期: 2024-12-19
版本: 参数优化版
"""

import sys
import os
import time
import json
import itertools
from typing import List, Dict, Any, Tuple, Optional
from dataclasses import dataclass

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

@dataclass
class OptimizationResult:
    """优化结果数据类"""
    algorithm: str
    sort_strategy: str
    rotation_enabled: bool
    utilization_rate: float
    placement_count: int
    canvas_height: int
    processing_time: float
    success_rate: float
    
    def score(self) -> float:
        """计算综合评分"""
        # 权重分配：利用率50%，成功率30%，速度15%，紧凑度5%
        utilization_score = self.utilization_rate * 0.5
        success_score = self.success_rate * 0.3
        speed_score = min(1.0, 10.0 / max(0.1, self.processing_time)) * 0.15
        compactness_score = min(1.0, 1000.0 / max(100, self.canvas_height)) * 0.05
        
        return utilization_score + success_score + speed_score + compactness_score

def get_available_algorithms():
    """
    获取所有可用的RectPack算法
    """
    try:
        from rectpack import (
            MaxRectsBaf, MaxRectsBl, MaxRectsBlsf, MaxRectsBssf,
            SkylineBl, SkylineBlWm, SkylineMwf, SkylineMwfWm, SkylineMwfl, SkylineMwflWm,
            GuillotineBafLas, GuillotineBafLlas, GuillotineBafMaxas, GuillotineBafMinas,
            GuillotineBafSas, GuillotineBafSlas, GuillotineBlsfLas, GuillotineBlsfLlas,
            GuillotineBlsfMaxas, GuillotineBlsfMinas, GuillotineBlsfSas, GuillotineBlsfSlas,
            GuillotineBssfLas, GuillotineBssfLlas, GuillotineBssfMaxas, GuillotineBssfMinas,
            GuillotineBssfSas, GuillotineBssfSlas
        )
        
        algorithms = {
            # MaxRects算法族 - 通常效果最好
            'MaxRectsBaf': MaxRectsBaf,      # Best Area Fit - 推荐
            'MaxRectsBl': MaxRectsBl,        # Bottom Left
            'MaxRectsBlsf': MaxRectsBlsf,    # Bottom Left Short Side Fit
            'MaxRectsBssf': MaxRectsBssf,    # Best Short Side Fit - 推荐
            
            # Skyline算法族 - 适合长条形布局
            'SkylineBl': SkylineBl,          # Bottom Left
            'SkylineBlWm': SkylineBlWm,      # Bottom Left with Waste Map
            'SkylineMwf': SkylineMwf,        # Min Waste Fit
            'SkylineMwfWm': SkylineMwfWm,    # Min Waste Fit with Waste Map - 推荐
            'SkylineMwfl': SkylineMwfl,      # Min Waste Fit Long
            'SkylineMwflWm': SkylineMwflWm,  # Min Waste Fit Long with Waste Map
            
            # Guillotine算法族 - 快速但可能不是最优
            'GuillotineBafLas': GuillotineBafLas,    # Best Area Fit, Longer Axis Split
            'GuillotineBafLlas': GuillotineBafLlas,  # Best Area Fit, Longer Left Area Split
            'GuillotineBafMaxas': GuillotineBafMaxas, # Best Area Fit, Maximize Area Split
            'GuillotineBafMinas': GuillotineBafMinas, # Best Area Fit, Minimize Area Split
            'GuillotineBafSas': GuillotineBafSas,    # Best Area Fit, Shorter Axis Split
            'GuillotineBafSlas': GuillotineBafSlas,  # Best Area Fit, Shorter Left Area Split
        }
        
        return algorithms
        
    except ImportError as e:
        print(f"❌ 无法导入RectPack算法: {str(e)}")
        return {}

def get_sort_strategies():
    """
    获取所有可用的排序策略
    """
    try:
        from rectpack import SORT_AREA, SORT_PERI, SORT_DIFF, SORT_SSIDE, SORT_LSIDE, SORT_RATIO, SORT_NONE
        
        strategies = {
            'SORT_AREA': (SORT_AREA, '按面积排序 - 大图片优先'),
            'SORT_PERI': (SORT_PERI, '按周长排序 - 周长大的优先'),
            'SORT_DIFF': (SORT_DIFF, '按差值排序 - 宽高差大的优先'),
            'SORT_SSIDE': (SORT_SSIDE, '按短边排序 - 短边长的优先'),
            'SORT_LSIDE': (SORT_LSIDE, '按长边排序 - 长边长的优先'),
            'SORT_RATIO': (SORT_RATIO, '按比例排序 - 宽高比大的优先'),
            'SORT_NONE': (SORT_NONE, '不排序 - 保持原始顺序'),
        }
        
        return strategies
        
    except ImportError as e:
        print(f"❌ 无法导入排序策略: {str(e)}")
        return {}

def create_optimized_packer(algorithm_class, sort_strategy, rotation_enabled=True):
    """
    创建优化的packer实例
    """
    try:
        from rectpack import newPacker
        
        packer = newPacker(
            mode=1,  # Offline mode - 批量处理模式
            bin_algo=2,  # 使用默认的bin选择算法
            pack_algo=algorithm_class,  # 使用指定的装箱算法
            sort_algo=lambda x: sort_strategy,  # 排序算法
            rotation=rotation_enabled  # 旋转设置
        )
        
        return packer
        
    except Exception as e:
        print(f"❌ 创建packer失败: {str(e)}")
        return None

def test_algorithm_combination(algorithm_name, algorithm_class, sort_name, sort_strategy, 
                             rotation_enabled, test_images, canvas_width, canvas_height):
    """
    测试特定算法组合的效果
    """
    try:
        start_time = time.time()
        
        # 创建packer
        packer = create_optimized_packer(algorithm_class, sort_strategy, rotation_enabled)
        if packer is None:
            return None
        
        # 添加容器
        packer.add_bin(canvas_width, canvas_height)
        
        # 添加矩形
        placed_count = 0
        total_area = 0
        
        for i, img in enumerate(test_images):
            width = img['width']
            height = img['height']
            total_area += width * height
            
            packer.add_rect(width, height, rid=i)
            placed_count += 1
        
        # 执行装箱
        packer.pack()
        
        # 分析结果
        placed_rects = packer.rect_list()
        successful_placements = len(placed_rects)
        
        if successful_placements == 0:
            return OptimizationResult(
                algorithm=algorithm_name,
                sort_strategy=sort_name,
                rotation_enabled=rotation_enabled,
                utilization_rate=0.0,
                placement_count=0,
                canvas_height=canvas_height,
                processing_time=time.time() - start_time,
                success_rate=0.0
            )
        
        # 计算实际使用的画布高度
        max_y = 0
        used_area = 0
        
        for rect in placed_rects:
            bin_id, x, y, width, height, rid = rect
            max_y = max(max_y, y + height)
            used_area += width * height
        
        actual_canvas_area = canvas_width * max_y
        utilization_rate = used_area / actual_canvas_area if actual_canvas_area > 0 else 0.0
        success_rate = successful_placements / len(test_images)
        
        processing_time = time.time() - start_time
        
        return OptimizationResult(
            algorithm=algorithm_name,
            sort_strategy=sort_name,
            rotation_enabled=rotation_enabled,
            utilization_rate=utilization_rate,
            placement_count=successful_placements,
            canvas_height=max_y,
            processing_time=processing_time,
            success_rate=success_rate
        )
        
    except Exception as e:
        print(f"❌ 测试算法组合失败 {algorithm_name}+{sort_name}: {str(e)}")
        return None

def generate_test_images(count=20, min_size=50, max_size=200):
    """
    生成测试图片数据
    """
    import random
    
    images = []
    for i in range(count):
        # 生成多样化的图片尺寸
        if i % 4 == 0:
            # 正方形图片
            size = random.randint(min_size, max_size)
            width, height = size, size
        elif i % 4 == 1:
            # 横向矩形
            width = random.randint(max_size, max_size * 2)
            height = random.randint(min_size, max_size)
        elif i % 4 == 2:
            # 纵向矩形
            width = random.randint(min_size, max_size)
            height = random.randint(max_size, max_size * 2)
        else:
            # 随机尺寸
            width = random.randint(min_size, max_size)
            height = random.randint(min_size, max_size)
        
        images.append({
            'width': width,
            'height': height,
            'name': f'test_img_{i+1}',
            'area': width * height
        })
    
    return images

def run_comprehensive_optimization(test_images, canvas_width=205, canvas_height=5000):
    """
    运行全面的参数优化
    """
    print("🔧 开始RectPack算法全面参数优化")
    print("=" * 80)
    
    algorithms = get_available_algorithms()
    sort_strategies = get_sort_strategies()
    
    if not algorithms or not sort_strategies:
        print("❌ 无法获取算法或排序策略")
        return None
    
    print(f"📋 测试配置:")
    print(f"  可用算法: {len(algorithms)} 种")
    print(f"  排序策略: {len(sort_strategies)} 种")
    print(f"  旋转选项: 2 种 (启用/禁用)")
    print(f"  测试图片: {len(test_images)} 张")
    print(f"  画布尺寸: {canvas_width}x{canvas_height}px")
    
    total_combinations = len(algorithms) * len(sort_strategies) * 2
    print(f"  总组合数: {total_combinations}")
    
    results = []
    current_test = 0
    
    # 优先测试推荐的算法组合
    priority_algorithms = ['MaxRectsBssf', 'MaxRectsBaf', 'SkylineMwfWm', 'SkylineMwf']
    priority_sorts = ['SORT_AREA', 'SORT_RATIO', 'SORT_SSIDE']
    
    print(f"\n🚀 第一阶段：测试优先算法组合")
    
    # 第一阶段：测试优先组合
    for algo_name in priority_algorithms:
        if algo_name not in algorithms:
            continue
            
        for sort_name in priority_sorts:
            if sort_name not in sort_strategies:
                continue
                
            for rotation in [True, False]:
                current_test += 1
                print(f"  测试 {current_test}/{total_combinations}: {algo_name} + {sort_name} + 旋转={rotation}")
                
                result = test_algorithm_combination(
                    algo_name, algorithms[algo_name],
                    sort_name, sort_strategies[sort_name][0],
                    rotation, test_images, canvas_width, canvas_height
                )
                
                if result:
                    results.append(result)
                    print(f"    利用率: {result.utilization_rate:.2%}, 成功率: {result.success_rate:.2%}, 评分: {result.score():.4f}")
    
    print(f"\n🔧 第二阶段：测试其他算法组合")
    
    # 第二阶段：测试其他组合
    for algo_name, algo_class in algorithms.items():
        if algo_name in priority_algorithms:
            continue  # 跳过已测试的优先算法
            
        for sort_name, (sort_strategy, sort_desc) in sort_strategies.items():
            if sort_name in priority_sorts:
                continue  # 跳过已测试的优先排序
                
            for rotation in [True, False]:
                current_test += 1
                print(f"  测试 {current_test}/{total_combinations}: {algo_name} + {sort_name} + 旋转={rotation}")
                
                result = test_algorithm_combination(
                    algo_name, algo_class, sort_name, sort_strategy,
                    rotation, test_images, canvas_width, canvas_height
                )
                
                if result:
                    results.append(result)
                    print(f"    利用率: {result.utilization_rate:.2%}, 成功率: {result.success_rate:.2%}, 评分: {result.score():.4f}")
    
    return results

def analyze_optimization_results(results: List[OptimizationResult]):
    """
    分析优化结果
    """
    if not results:
        print("❌ 没有有效的测试结果")
        return None
    
    print(f"\n📊 优化结果分析")
    print("=" * 80)
    
    # 按评分排序
    results.sort(key=lambda x: x.score(), reverse=True)
    
    print(f"📈 总测试组合: {len(results)}")
    print(f"📈 有效结果: {len([r for r in results if r.success_rate > 0])}")
    
    # 显示前10名
    print(f"\n🏆 前10名最佳组合:")
    print("-" * 120)
    print(f"{'排名':<4} {'算法':<20} {'排序策略':<12} {'旋转':<6} {'利用率':<8} {'成功率':<8} {'高度':<8} {'时间':<8} {'评分':<8}")
    print("-" * 120)
    
    for i, result in enumerate(results[:10], 1):
        print(f"{i:<4} {result.algorithm:<20} {result.sort_strategy:<12} "
              f"{'是' if result.rotation_enabled else '否':<6} "
              f"{result.utilization_rate:.2%:<8} {result.success_rate:.2%:<8} "
              f"{result.canvas_height:<8} {result.processing_time:.3f}s<8 {result.score():.4f}")
    
    # 最佳结果
    best_result = results[0]
    print(f"\n🎯 最佳配置:")
    print(f"  算法: {best_result.algorithm}")
    print(f"  排序策略: {best_result.sort_strategy}")
    print(f"  旋转: {'启用' if best_result.rotation_enabled else '禁用'}")
    print(f"  画布利用率: {best_result.utilization_rate:.2%}")
    print(f"  成功率: {best_result.success_rate:.2%}")
    print(f"  画布高度: {best_result.canvas_height}px")
    print(f"  处理时间: {best_result.processing_time:.3f}s")
    print(f"  综合评分: {best_result.score():.4f}")
    
    # 分类分析
    print(f"\n📋 分类分析:")
    
    # 按算法分析
    algo_scores = {}
    for result in results:
        if result.algorithm not in algo_scores:
            algo_scores[result.algorithm] = []
        algo_scores[result.algorithm].append(result.score())
    
    print(f"\n🔧 算法性能排名:")
    algo_avg_scores = {algo: sum(scores)/len(scores) for algo, scores in algo_scores.items()}
    for algo, avg_score in sorted(algo_avg_scores.items(), key=lambda x: x[1], reverse=True)[:5]:
        print(f"  {algo}: 平均评分 {avg_score:.4f}")
    
    # 按排序策略分析
    sort_scores = {}
    for result in results:
        if result.sort_strategy not in sort_scores:
            sort_scores[result.sort_strategy] = []
        sort_scores[result.sort_strategy].append(result.score())
    
    print(f"\n📊 排序策略性能排名:")
    sort_avg_scores = {sort: sum(scores)/len(scores) for sort, scores in sort_scores.items()}
    for sort, avg_score in sorted(sort_avg_scores.items(), key=lambda x: x[1], reverse=True):
        print(f"  {sort}: 平均评分 {avg_score:.4f}")
    
    # 旋转效果分析
    rotation_true = [r for r in results if r.rotation_enabled]
    rotation_false = [r for r in results if not r.rotation_enabled]
    
    if rotation_true and rotation_false:
        avg_score_rotation = sum(r.score() for r in rotation_true) / len(rotation_true)
        avg_score_no_rotation = sum(r.score() for r in rotation_false) / len(rotation_false)
        
        print(f"\n🔄 旋转效果分析:")
        print(f"  启用旋转: 平均评分 {avg_score_rotation:.4f}")
        print(f"  禁用旋转: 平均评分 {avg_score_no_rotation:.4f}")
        print(f"  旋转优势: {((avg_score_rotation - avg_score_no_rotation) / avg_score_no_rotation * 100):+.1f}%")
    
    return best_result

def save_optimization_results(results: List[OptimizationResult], best_result: OptimizationResult, 
                            output_file="rectpack_optimization_results.json"):
    """
    保存优化结果到文件
    """
    try:
        data = {
            'optimization_timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'best_configuration': {
                'algorithm': best_result.algorithm,
                'sort_strategy': best_result.sort_strategy,
                'rotation_enabled': best_result.rotation_enabled,
                'utilization_rate': best_result.utilization_rate,
                'success_rate': best_result.success_rate,
                'canvas_height': best_result.canvas_height,
                'processing_time': best_result.processing_time,
                'score': best_result.score()
            },
            'all_results': [
                {
                    'algorithm': r.algorithm,
                    'sort_strategy': r.sort_strategy,
                    'rotation_enabled': r.rotation_enabled,
                    'utilization_rate': r.utilization_rate,
                    'success_rate': r.success_rate,
                    'canvas_height': r.canvas_height,
                    'processing_time': r.processing_time,
                    'score': r.score()
                }
                for r in results
            ]
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 优化结果已保存到: {output_file}")
        
    except Exception as e:
        print(f"❌ 保存结果失败: {str(e)}")

def apply_optimized_parameters(best_result: OptimizationResult):
    """
    应用优化后的参数到RectPackArranger
    """
    try:
        from core.rectpack_arranger import RectPackArranger
        
        print(f"\n🔧 应用优化参数到RectPackArranger")
        print(f"  算法: {best_result.algorithm}")
        print(f"  排序策略: {best_result.sort_strategy}")
        print(f"  旋转: {'启用' if best_result.rotation_enabled else '禁用'}")
        
        # 这里可以添加代码来更新RectPackArranger的默认参数
        # 或者生成配置文件供系统使用
        
        config = {
            'rectpack_algorithm': best_result.algorithm,
            'rectpack_sort_strategy': best_result.sort_strategy,
            'rectpack_rotation_enabled': best_result.rotation_enabled,
            'expected_utilization_rate': best_result.utilization_rate,
            'expected_success_rate': best_result.success_rate
        }
        
        with open('rectpack_optimized_config.json', 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 优化配置已保存到: rectpack_optimized_config.json")
        
        return config
        
    except Exception as e:
        print(f"❌ 应用优化参数失败: {str(e)}")
        return None

def main():
    """
    主优化函数
    """
    print("🚀 RectPack算法参数全面优化")
    print("=" * 100)
    print("目标:")
    print("1. 🎯 最大化画布利用率")
    print("2. 🔄 优化图片旋转策略")
    print("3. ⚡ 提升处理速度")
    print("4. 📊 提高成功率")
    print("=" * 100)
    
    # 生成测试数据
    print(f"\n📋 生成测试数据...")
    test_images = generate_test_images(count=30, min_size=50, max_size=150)
    
    total_area = sum(img['area'] for img in test_images)
    print(f"  测试图片: {len(test_images)} 张")
    print(f"  尺寸范围: 50-150px")
    print(f"  总面积: {total_area:,}px²")
    
    # 运行优化
    canvas_width = 205
    canvas_height = 5000
    
    print(f"\n🔧 开始参数优化...")
    results = run_comprehensive_optimization(test_images, canvas_width, canvas_height)
    
    if not results:
        print("❌ 优化失败，没有有效结果")
        return False
    
    # 分析结果
    best_result = analyze_optimization_results(results)
    
    if not best_result:
        print("❌ 无法确定最佳配置")
        return False
    
    # 保存结果
    save_optimization_results(results, best_result)
    
    # 应用优化参数
    config = apply_optimized_parameters(best_result)
    
    print(f"\n🎉 优化完成！")
    print(f"💡 建议使用以下配置:")
    print(f"  算法: {best_result.algorithm}")
    print(f"  排序策略: {best_result.sort_strategy}")
    print(f"  旋转: {'启用' if best_result.rotation_enabled else '禁用'}")
    print(f"  预期利用率: {best_result.utilization_rate:.2%}")
    print(f"  预期成功率: {best_result.success_rate:.2%}")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
