#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
应用最佳参数工具
将优化后的最佳参数应用到俄罗斯方块算法中
"""

import os
import sys
import json
import argparse
import logging

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
log = logging.getLogger("ApplyBestParams")

def load_best_params(input_path: str) -> dict:
    """
    从JSON文件加载最佳参数

    Args:
        input_path: 输入文件路径

    Returns:
        dict: 最佳参数
    """
    try:
        with open(input_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        if 'best_parameters' in data:
            params = data['best_parameters']
            log.info(f"从 {input_path} 加载了最佳参数:")
            for key, value in params.items():
                log.info(f"  - {key}: {value}")
            return params
        else:
            log.error(f"文件 {input_path} 中没有找到最佳参数")
            return {}
    except Exception as e:
        log.error(f"加载最佳参数时出错: {e}")
        return {}

def apply_params_to_config(params: dict, output_path: str) -> bool:
    """
    将参数应用到配置文件中

    Args:
        params: 参数字典
        output_path: 输出文件路径

    Returns:
        bool: 是否成功
    """
    try:
        # 创建配置字典
        config = {
            'tetris_algorithm': {
                'parameters': params,
                'description': '自动优化的俄罗斯方块算法参数'
            }
        }
        
        # 保存到JSON文件
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2)
        
        log.info(f"参数已应用到配置文件: {output_path}")
        return True
    except Exception as e:
        log.error(f"应用参数到配置文件时出错: {e}")
        return False

def apply_params_to_duckdb(params: dict, db_path: str) -> bool:
    """
    将参数应用到DuckDB数据库中

    Args:
        params: 参数字典
        db_path: 数据库文件路径

    Returns:
        bool: 是否成功
    """
    try:
        # 尝试导入DuckDB
        import duckdb
        
        # 连接数据库
        conn = duckdb.connect(db_path)
        
        # 检查表是否存在，如果不存在则创建
        conn.execute("""
            CREATE TABLE IF NOT EXISTS algorithm_parameters (
                id INTEGER PRIMARY KEY,
                name VARCHAR,
                value INTEGER,
                description VARCHAR,
                updated_at TIMESTAMP
            )
        """)
        
        # 插入或更新参数
        for name, value in params.items():
            # 检查参数是否已存在
            result = conn.execute(f"SELECT id FROM algorithm_parameters WHERE name = '{name}'").fetchone()
            
            if result:
                # 更新现有参数
                conn.execute(f"""
                    UPDATE algorithm_parameters 
                    SET value = {value}, updated_at = CURRENT_TIMESTAMP 
                    WHERE name = '{name}'
                """)
                log.info(f"更新参数: {name} = {value}")
            else:
                # 插入新参数
                conn.execute(f"""
                    INSERT INTO algorithm_parameters (name, value, description, updated_at)
                    VALUES ('{name}', {value}, '自动优化的俄罗斯方块算法参数', CURRENT_TIMESTAMP)
                """)
                log.info(f"插入参数: {name} = {value}")
        
        # 提交事务
        conn.commit()
        
        # 关闭连接
        conn.close()
        
        log.info(f"参数已应用到DuckDB数据库: {db_path}")
        return True
    except ImportError:
        log.error("未找到DuckDB库，请安装: pip install duckdb")
        return False
    except Exception as e:
        log.error(f"应用参数到DuckDB数据库时出错: {e}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='应用最佳参数工具')
    
    # 输入参数
    parser.add_argument('--input', type=str, required=True, help='输入最佳参数文件路径')
    
    # 输出参数
    parser.add_argument('--output', type=str, default='config/tetris_params.json', help='输出配置文件路径')
    parser.add_argument('--db', type=str, default=None, help='DuckDB数据库文件路径')
    
    args = parser.parse_args()
    
    # 加载最佳参数
    params = load_best_params(args.input)
    
    if not params:
        log.error("未能加载最佳参数，退出")
        return
    
    # 应用参数到配置文件
    apply_params_to_config(params, args.output)
    
    # 如果提供了数据库路径，应用参数到DuckDB
    if args.db:
        apply_params_to_duckdb(params, args.db)


if __name__ == "__main__":
    main()
