#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
贝叶斯优化器
使用贝叶斯优化算法寻找俄罗斯方块算法的最优参数
"""

import os
import sys
import time
import json
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
from typing import List, Dict, Tuple, Any, Callable, Optional
import logging

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'SimSun', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

# 尝试导入scikit-optimize库
try:
    from skopt import gp_minimize
    from skopt.space import Integer
    from skopt.utils import use_named_args
    from skopt.plots import plot_convergence, plot_objective
    SKOPT_AVAILABLE = True
except ImportError:
    SKOPT_AVAILABLE = False
    print("警告: 未找到scikit-optimize库，将使用网格搜索代替贝叶斯优化")
    print("可以通过运行 'pip install scikit-optimize' 安装该库")

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
log = logging.getLogger("BayesianOptimizer")

class BayesianOptimizer:
    """贝叶斯优化器"""

    def __init__(self):
        """初始化贝叶斯优化器"""
        self.available = SKOPT_AVAILABLE
        self.best_params = None
        self.best_score = -float('inf')
        self.search_history = []

        log.info(f"初始化贝叶斯优化器")
        if not self.available:
            log.warning("未找到scikit-optimize库，将使用网格搜索代替贝叶斯优化")

    def optimize(self,
                objective_function: Callable,
                param_ranges: Dict[str, Tuple[int, int]],
                n_calls: int = 30,
                random_state: Optional[int] = None) -> Dict[str, Any]:
        """
        使用贝叶斯优化寻找最优参数

        Args:
            objective_function: 目标函数，接受参数字典，返回负的得分（因为优化器是最小化函数）
            param_ranges: 参数范围字典，键为参数名，值为(最小值, 最大值)元组
            n_calls: 调用目标函数的次数
            random_state: 随机种子

        Returns:
            Dict[str, Any]: 优化结果
        """
        if not self.available:
            log.warning("贝叶斯优化不可用，将使用网格搜索代替")
            log.warning("请安装scikit-optimize库: pip install scikit-optimize")
            return self._grid_search(objective_function, param_ranges)

        # 清空历史记录
        self.search_history = []
        self.best_score = -float('inf')
        self.best_params = None

        # 创建参数空间
        space = []
        param_names = []

        for name, (low, high) in param_ranges.items():
            space.append(Integer(low, high, name=name))
            param_names.append(name)
            log.info(f"参数 {name} 搜索范围: [{low}, {high}]")

        # 定义带名称的目标函数
        @use_named_args(space)
        def objective_with_names(**params):
            # 记录当前迭代
            iteration = len(self.search_history) + 1
            log.info(f"===== 开始迭代 {iteration}/{n_calls} =====")
            log.info(f"当前参数组合: {params}")

            # 调用目标函数
            try:
                start_time = time.time()
                score = -objective_function(params)  # 取负值，因为我们要最小化
                end_time = time.time()

                log.info(f"评估完成，耗时: {end_time - start_time:.2f}秒")
                log.info(f"得分: {-score:.4f}")

                # 记录搜索历史
                self.search_history.append({
                    'iteration': iteration,
                    'parameters': params.copy(),
                    'score': -score,  # 恢复为正值
                    'time': end_time - start_time
                })

                # 更新最佳参数
                if -score > self.best_score:
                    self.best_score = -score
                    self.best_params = params.copy()
                    log.info(f"发现新的最佳参数组合! 迭代: {iteration}, 得分: {-score:.4f}")
                    log.info(f"  参数: {params}")

                log.info(f"===== 完成迭代 {iteration}/{n_calls} =====")
                return score
            except Exception as e:
                log.error(f"迭代 {iteration} 评估出错: {e}")
                # 返回一个非常差的分数
                return 1000000

        # 执行贝叶斯优化
        log.info(f"开始贝叶斯优化，调用次数: {n_calls}")
        log.info(f"参数范围: {param_ranges}")

        try:
            result = gp_minimize(
                objective_with_names,
                space,
                n_calls=n_calls,
                random_state=random_state,
                verbose=True
            )
        except Exception as e:
            log.error(f"贝叶斯优化过程出错: {e}")
            log.info("尝试使用网格搜索作为备选方案...")
            return self._grid_search(objective_function, param_ranges)

        # 提取最佳参数
        best_params = {}
        for i, name in enumerate(param_names):
            best_params[name] = result.x[i]

        log.info(f"贝叶斯优化完成!")
        log.info(f"最优参数组合: {best_params}")
        log.info(f"最优得分: {-result.fun:.4f}")

        # 返回结果
        return {
            'best_params': best_params,
            'best_score': -result.fun,
            'search_history': self.search_history,
            'raw_result': result
        }

    def _grid_search(self,
                    objective_function: Callable,
                    param_ranges: Dict[str, Tuple[int, int]],
                    grid_points: int = 3) -> Dict[str, Any]:
        """
        使用网格搜索寻找最优参数（当贝叶斯优化不可用时的备选方案）

        Args:
            objective_function: 目标函数，接受参数字典，返回得分
            param_ranges: 参数范围字典，键为参数名，值为(最小值, 最大值)元组
            grid_points: 每个维度的网格点数量

        Returns:
            Dict[str, Any]: 优化结果
        """
        # 清空历史记录
        self.search_history = []
        self.best_score = -float('inf')
        self.best_params = None

        # 创建网格点
        param_grids = {}
        for name, (low, high) in param_ranges.items():
            param_grids[name] = np.linspace(low, high, grid_points, dtype=int).tolist()
            log.info(f"参数 {name} 网格点: {param_grids[name]}")

        # 计算总组合数
        param_names = list(param_grids.keys())
        total_combinations = 1
        for values in param_grids.values():
            total_combinations *= len(values)

        log.info(f"开始网格搜索，总组合数: {total_combinations}")
        log.info(f"参数范围: {param_ranges}")
        log.info(f"每个维度的网格点数量: {grid_points}")

        # 使用迭代而不是递归来生成组合，避免栈溢出
        current_iteration = 0

        # 生成所有参数组合
        from itertools import product
        param_combinations = list(product(*[param_grids[name] for name in param_names]))

        for combination in param_combinations:
            current_iteration += 1
            current_params = {name: value for name, value in zip(param_names, combination)}

            log.info(f"===== 开始组合 {current_iteration}/{total_combinations} =====")
            log.info(f"当前参数组合: {current_params}")

            try:
                # 调用目标函数
                start_time = time.time()
                score = objective_function(current_params)
                end_time = time.time()

                log.info(f"评估完成，耗时: {end_time - start_time:.2f}秒")
                log.info(f"得分: {score:.4f}")

                # 记录搜索历史
                self.search_history.append({
                    'iteration': current_iteration,
                    'parameters': current_params.copy(),
                    'score': score,
                    'time': end_time - start_time
                })

                # 更新最佳参数
                if score > self.best_score:
                    self.best_score = score
                    self.best_params = current_params.copy()
                    log.info(f"发现新的最佳参数组合! 迭代: {current_iteration}, 得分: {score:.4f}")
                    log.info(f"  参数: {current_params}")
            except Exception as e:
                log.error(f"组合 {current_iteration} 评估出错: {e}")

            log.info(f"===== 完成组合 {current_iteration}/{total_combinations} =====")

            # 显示进度
            if current_iteration % 5 == 0 or current_iteration == total_combinations:
                progress = current_iteration / total_combinations * 100
                log.info(f"网格搜索进度: {progress:.1f}% ({current_iteration}/{total_combinations})")

        log.info(f"网格搜索完成!")
        log.info(f"最优参数组合: {self.best_params}")
        log.info(f"最优得分: {self.best_score:.4f}")

        # 返回结果
        return {
            'best_params': self.best_params,
            'best_score': self.best_score,
            'search_history': self.search_history,
            'raw_result': None
        }

    def visualize_search_history(self, search_history: List[Dict[str, Any]], output_path: str = None) -> None:
        """
        可视化参数搜索历史

        Args:
            search_history: 搜索历史
            output_path: 输出文件路径，如果为None则显示图像
        """
        if not search_history:
            log.warning("没有搜索历史可视化")
            return

        # 提取数据
        iterations = [h['iteration'] for h in search_history]
        scores = [h['score'] for h in search_history]

        # 创建图像
        plt.figure(figsize=(10, 6))
        plt.plot(iterations, scores, 'b-', marker='o', markersize=4)

        # 标记最佳点
        best_idx = np.argmax(scores)
        best_iteration = iterations[best_idx]
        best_score = scores[best_idx]
        best_params = search_history[best_idx]['parameters']

        plt.plot(best_iteration, best_score, 'ro', markersize=8)

        # 创建参数文本
        param_text = ", ".join([f"{k}:{v}" for k, v in best_params.items()])

        plt.annotate(f"最佳: {best_score:.2f}\n{param_text}",
                    xy=(best_iteration, best_score),
                    xytext=(best_iteration + 1, best_score + 1),
                    arrowprops=dict(facecolor='black', shrink=0.05),
                    fontproperties='SimHei')

        # 设置标题和标签（使用中文字体）
        plt.title('参数搜索历史', fontproperties='SimHei')
        plt.xlabel('迭代次数', fontproperties='SimHei')
        plt.ylabel('得分', fontproperties='SimHei')
        plt.grid(True)

        # 保存或显示图像
        if output_path:
            # 确保目录存在
            output_dir = os.path.dirname(output_path)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir, exist_ok=True)
            # 保存图像，使用高质量设置
            plt.savefig(output_path, dpi=150, bbox_inches='tight')
            log.info(f"搜索历史可视化已保存到: {output_path}")
        else:
            plt.show()

        plt.close()

    def visualize_objective(self, result: Dict[str, Any], output_path: str = None) -> None:
        """
        可视化目标函数（仅贝叶斯优化可用）

        Args:
            result: 优化结果
            output_path: 输出文件路径，如果为None则显示图像
        """
        if not self.available or 'raw_result' not in result or result['raw_result'] is None:
            log.warning("贝叶斯优化不可用或没有原始结果，无法可视化目标函数")
            return

        # 创建图像
        fig, ax = plt.subplots(figsize=(10, 8))

        # 绘制收敛图
        plot_convergence(result['raw_result'], ax=ax)

        # 设置标题（使用中文字体）
        plt.title('贝叶斯优化收敛过程', fontproperties='SimHei')

        # 保存或显示图像
        if output_path:
            # 确保目录存在
            output_dir = os.path.dirname(output_path)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir, exist_ok=True)
            # 保存图像，使用高质量设置
            plt.savefig(output_path, dpi=150, bbox_inches='tight')
            log.info(f"目标函数可视化已保存到: {output_path}")
        else:
            plt.show()

        plt.close()

        # 如果参数数量小于等于3，绘制参数重要性图
        if len(result['best_params']) <= 3:
            fig, ax = plt.subplots(figsize=(10, 8))
            plot_objective(result['raw_result'], n_points=10)

            # 设置标题（使用中文字体）
            plt.suptitle('参数重要性分析', fontproperties='SimHei')

            # 保存或显示图像
            if output_path:
                importance_path = output_path.replace('.png', '_importance.png')
                # 确保目录存在
                output_dir = os.path.dirname(importance_path)
                if output_dir and not os.path.exists(output_dir):
                    os.makedirs(output_dir, exist_ok=True)
                # 保存图像，使用高质量设置
                plt.savefig(importance_path, dpi=150, bbox_inches='tight')
                log.info(f"参数重要性可视化已保存到: {importance_path}")
            else:
                plt.show()

            plt.close()

    def save_best_params(self, best_params: Dict[str, Any], best_score: float, output_path: str) -> None:
        """
        保存最佳参数到JSON文件

        Args:
            best_params: 最佳参数
            best_score: 最佳得分
            output_path: 输出文件路径
        """
        # 创建包含最佳参数和得分的字典
        data = {
            'best_parameters': best_params,
            'best_score': best_score,
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
        }

        # 保存到JSON文件
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2)

        log.info(f"最佳参数已保存到: {output_path}")
