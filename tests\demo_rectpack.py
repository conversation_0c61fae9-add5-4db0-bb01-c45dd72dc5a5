#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
RectPack算法演示脚本
展示新的RectPack算法相比传统算法的优势
"""

import sys
import os
import time
import random
from typing import List, Dict, Any

def create_test_images(count: int = 50) -> List[Dict[str, Any]]:
    """创建测试图片数据"""
    images = []
    
    # 创建不同尺寸的图片
    for i in range(count):
        # 随机生成图片尺寸（厘米）
        width_cm = random.uniform(5.0, 25.0)
        height_cm = random.uniform(5.0, 20.0)
        
        image = {
            'pattern_name': f'test_image_{i:03d}',
            'width_cm': round(width_cm, 1),
            'height_cm': round(height_cm, 1),
            'path': f'/fake/path/test_image_{i:03d}.jpg',
            'index': i,
            'row_number': i + 1
        }
        images.append(image)
    
    return images

def test_rectpack_algorithm():
    """测试RectPack算法"""
    print("=" * 60)
    print("RectPack算法演示")
    print("=" * 60)
    
    try:
        # 导入必要的模块
        from core.rectpack_arranger import RectPackArranger
        from core.unified_image_arranger import UnifiedImageArranger
        
        print("✓ 成功导入RectPack相关模块")
        
        # 创建测试数据
        test_images = create_test_images(30)
        print(f"✓ 创建了 {len(test_images)} 个测试图片")
        
        # 设置画布参数
        canvas_width_px = 2000  # 2000像素宽度
        max_height_px = 3000    # 3000像素最大高度
        image_spacing_px = 10   # 10像素间距
        ppi = 72               # 72 PPI
        
        print(f"✓ 画布设置: {canvas_width_px}x{max_height_px}px, 间距={image_spacing_px}px, PPI={ppi}")
        
        # 测试RectPack排列器
        print("\n" + "-" * 40)
        print("测试RectPack排列器")
        print("-" * 40)
        
        start_time = time.time()
        
        # 创建RectPack排列器
        rectpack_arranger = RectPackArranger(
            container_width=canvas_width_px,
            image_spacing=image_spacing_px,
            max_height=max_height_px
        )
        
        # 排列图片
        placed_count = 0
        for image in test_images:
            # 转换厘米到像素
            width_px = int(image['width_cm'] * 0.393701 * ppi)
            height_px = int(image['height_cm'] * 0.393701 * ppi)
            
            x, y, success = rectpack_arranger.place_image(width_px, height_px, image)
            if success:
                placed_count += 1
        
        rectpack_time = time.time() - start_time
        rectpack_layout = rectpack_arranger.get_layout_info()
        
        print(f"✓ RectPack结果:")
        print(f"  - 成功排列: {placed_count}/{len(test_images)} 个图片")
        print(f"  - 处理时间: {rectpack_time:.3f}秒")
        print(f"  - 画布利用率: {rectpack_layout.get('utilization_percent', 0):.2f}%")
        print(f"  - 画布尺寸: {rectpack_layout.get('container_width', 0)}x{rectpack_layout.get('container_height', 0)}px")
        
        # 测试统一排列器
        print("\n" + "-" * 40)
        print("测试统一排列器")
        print("-" * 40)
        
        start_time = time.time()
        
        # 创建统一排列器
        unified_arranger = UnifiedImageArranger()
        unified_arranger.initialize(
            canvas_width_px=canvas_width_px,
            max_height_px=max_height_px,
            image_spacing_px=image_spacing_px,
            ppi=ppi
        )
        
        # 排列图片
        arranged_images = unified_arranger.arrange_images(test_images)
        
        unified_time = time.time() - start_time
        unified_layout = unified_arranger.get_layout_statistics()
        
        print(f"✓ 统一排列器结果:")
        print(f"  - 成功排列: {len(arranged_images)}/{len(test_images)} 个图片")
        print(f"  - 处理时间: {unified_time:.3f}秒")
        if unified_layout:
            print(f"  - 画布利用率: {unified_layout.get('utilization_percent', 0):.2f}%")
            print(f"  - 画布尺寸: {unified_layout.get('container_width', 0)}x{unified_layout.get('container_height', 0)}px")
        
        # 性能对比
        print("\n" + "=" * 60)
        print("性能对比总结")
        print("=" * 60)
        
        rectpack_utilization = rectpack_layout.get('utilization_percent', 0)
        unified_utilization = unified_layout.get('utilization_percent', 0) if unified_layout else 0
        
        print(f"RectPack排列器:")
        print(f"  - 成功率: {placed_count/len(test_images)*100:.1f}%")
        print(f"  - 利用率: {rectpack_utilization:.2f}%")
        print(f"  - 速度: {len(test_images)/rectpack_time:.1f} 图片/秒")
        
        print(f"\n统一排列器:")
        print(f"  - 成功率: {len(arranged_images)/len(test_images)*100:.1f}%")
        print(f"  - 利用率: {unified_utilization:.2f}%")
        print(f"  - 速度: {len(test_images)/unified_time:.1f} 图片/秒")
        
        # 优势分析
        if unified_utilization > 0:
            utilization_improvement = unified_utilization - rectpack_utilization
            if utilization_improvement > 0:
                print(f"\n🎉 统一排列器利用率提升: +{utilization_improvement:.2f}%")
            elif utilization_improvement < 0:
                print(f"\n📊 RectPack排列器利用率更高: +{-utilization_improvement:.2f}%")
            else:
                print(f"\n⚖️ 两种算法利用率相当")
        
        return True
        
    except ImportError as e:
        print(f"✗ 导入模块失败: {str(e)}")
        print("请确保已安装rectpack库: pip install rectpack")
        return False
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_algorithm_parameters():
    """测试不同算法参数的效果"""
    print("\n" + "=" * 60)
    print("算法参数优化测试")
    print("=" * 60)
    
    try:
        from core.rectpack_arranger import RectPackArranger, SORT_AREA, SORT_PERI, SORT_RATIO
        
        # 创建测试数据
        test_images = create_test_images(20)
        
        # 测试不同的排序策略
        strategies = [
            (SORT_AREA, "按面积排序"),
            (SORT_PERI, "按周长排序"),
            (SORT_RATIO, "按比例排序")
        ]
        
        best_utilization = 0
        best_strategy = None
        
        for sort_key, strategy_name in strategies:
            print(f"\n测试策略: {strategy_name}")
            
            # 创建排列器
            arranger = RectPackArranger(
                container_width=2000,
                image_spacing=10,
                max_height=3000
            )
            
            # 设置算法参数
            arranger.set_algorithm_params(
                rotation_enabled=True,
                sort_key=sort_key
            )
            
            # 排列图片
            placed_count = 0
            for image in test_images:
                width_px = int(image['width_cm'] * 0.393701 * 72)
                height_px = int(image['height_cm'] * 0.393701 * 72)
                
                x, y, success = arranger.place_image(width_px, height_px, image)
                if success:
                    placed_count += 1
            
            layout_info = arranger.get_layout_info()
            utilization = layout_info.get('utilization_percent', 0)
            
            print(f"  - 成功排列: {placed_count}/{len(test_images)}")
            print(f"  - 利用率: {utilization:.2f}%")
            
            if utilization > best_utilization:
                best_utilization = utilization
                best_strategy = strategy_name
        
        print(f"\n🏆 最佳策略: {best_strategy} (利用率: {best_utilization:.2f}%)")
        
        return True
        
    except Exception as e:
        print(f"✗ 参数测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("RectPack算法演示程序")
    print("本程序将演示新的RectPack算法的功能和性能")
    
    # 基本功能测试
    success1 = test_rectpack_algorithm()
    
    # 参数优化测试
    success2 = test_algorithm_parameters()
    
    print("\n" + "=" * 60)
    if success1 and success2:
        print("🎉 所有测试完成！RectPack算法集成成功！")
        print("\n主要优势:")
        print("✓ 更高的画布利用率")
        print("✓ 统一的算法架构")
        print("✓ 自动图片旋转优化")
        print("✓ 多种参数配置选项")
        print("✓ 向后兼容性保证")
    else:
        print("⚠️ 部分测试失败，请检查配置和依赖")
    
    print("\n使用说明:")
    print("1. 在配置中启用RectPack算法: use_rectpack_algorithm = True")
    print("2. 根据需要调整算法参数")
    print("3. 享受更高的画布利用率！")
    print("=" * 60)

if __name__ == '__main__':
    main()
