#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试数据生成脚本
生成各种情况的图片组合，用于测试俄罗斯方块算法
"""

import os
import sys
import random
import argparse
import json
import numpy as np
import locale
from typing import List, Dict, Any, Tuple

# 检查系统默认编码
system_encoding = locale.getpreferredencoding()
print(f"系统默认编码: {system_encoding}")

# 强制设置环境变量，确保使用UTF-8编码
os.environ['PYTHONIOENCODING'] = 'utf-8'

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
log = logging.getLogger("TestDataGenerator")

class TestDataGenerator:
    """测试数据生成器"""

    def __init__(self, seed: int = None):
        """
        初始化测试数据生成器

        Args:
            seed: 随机种子，用于生成可重复的测试数据
        """
        # 设置随机种子
        if seed is not None:
            random.seed(seed)
            np.random.seed(seed)

        log.info(f"初始化测试数据生成器 (种子: {seed})")

    def generate_random_images(self,
                              count: int,
                              min_size: int = 50,
                              max_size: int = 300,
                              distribution: str = 'random',
                              aspect_ratios: List[float] = None) -> List[Dict[str, Any]]:
        """
        生成随机图片

        Args:
            count: 图片数量
            min_size: 最小尺寸（像素）
            max_size: 最大尺寸（像素）
            distribution: 尺寸分布类型，可选值：'random'（随机）, 'uniform'（均匀）, 'normal'（正态）, 'bimodal'（双峰）
            aspect_ratios: 宽高比列表，如果提供，则从中随机选择

        Returns:
            List[Dict[str, Any]]: 图片列表，每个图片包含宽度、高度等信息
        """
        # 默认宽高比范围
        if aspect_ratios is None:
            aspect_ratios = [0.5, 0.75, 1.0, 1.33, 1.5, 2.0]

        images = []

        for i in range(count):
            # 根据分布类型生成尺寸
            if distribution == 'uniform':
                # 均匀分布
                size = random.randint(min_size, max_size)
            elif distribution == 'normal':
                # 正态分布
                mean = (min_size + max_size) / 2
                std = (max_size - min_size) / 6  # 99.7%的值在6个标准差内
                size = int(np.clip(np.random.normal(mean, std), min_size, max_size))
            elif distribution == 'bimodal':
                # 双峰分布（模拟小图和大图）
                if random.random() < 0.5:
                    size = random.randint(min_size, min_size + (max_size - min_size) // 3)
                else:
                    size = random.randint(max_size - (max_size - min_size) // 3, max_size)
            else:
                # 默认随机分布
                size = random.randint(min_size, max_size)

            # 随机选择宽高比
            aspect_ratio = random.choice(aspect_ratios)

            # 计算宽度和高度
            if random.random() < 0.5:
                # 宽图
                width = size
                height = int(width / aspect_ratio)
            else:
                # 高图
                height = size
                width = int(height * aspect_ratio)

            # 确保尺寸在合理范围内
            width = max(10, min(width, max_size * 2))
            height = max(10, min(height, max_size * 2))

            # 创建图片数据
            image = {
                'id': f"image_{i+1}",
                'width': width,
                'height': height,
                'aspect_ratio': width / height,
                'area': width * height
            }

            images.append(image)

        log.info(f"生成了 {len(images)} 个随机图片 (分布类型: {distribution})")
        log.info(f"  - 尺寸范围: {min_size}-{max_size}像素")
        log.info(f"  - 宽高比范围: {min(aspect_ratios)}-{max(aspect_ratios)}")

        return images

    def generate_fixed_size_images(self,
                                  sizes: List[Tuple[int, int]],
                                  counts: List[int]) -> List[Dict[str, Any]]:
        """
        生成固定尺寸的图片

        Args:
            sizes: 尺寸列表，每个元素为(宽度, 高度)
            counts: 每种尺寸的数量列表

        Returns:
            List[Dict[str, Any]]: 图片列表，每个图片包含宽度、高度等信息
        """
        if len(sizes) != len(counts):
            raise ValueError("尺寸列表和数量列表长度必须相同")

        images = []
        image_id = 1

        for (width, height), count in zip(sizes, counts):
            for i in range(count):
                image = {
                    'id': f"image_{image_id}",
                    'width': width,
                    'height': height,
                    'aspect_ratio': width / height,
                    'area': width * height
                }

                images.append(image)
                image_id += 1

        log.info(f"生成了 {len(images)} 个固定尺寸图片")
        for (width, height), count in zip(sizes, counts):
            log.info(f"  - {width}x{height}: {count}个")

        return images

    def generate_challenging_images(self, count: int = 50) -> List[Dict[str, Any]]:
        """
        生成具有挑战性的图片组合
        包括：极端宽高比、相似尺寸、大小差异极大的图片等

        Args:
            count: 图片总数

        Returns:
            List[Dict[str, Any]]: 图片列表，每个图片包含宽度、高度等信息
        """
        images = []
        image_id = 1

        # 1. 极端宽高比的图片（10%）
        extreme_count = max(1, int(count * 0.1))
        for i in range(extreme_count):
            if random.random() < 0.5:
                # 极宽图片
                width = random.randint(200, 400)
                height = random.randint(20, 50)
            else:
                # 极高图片
                width = random.randint(20, 50)
                height = random.randint(200, 400)

            image = {
                'id': f"image_{image_id}",
                'name': f"极端图片_{image_id}",  # 添加中文名称
                'width': width,
                'height': height,
                'aspect_ratio': width / height,
                'area': width * height,
                'type': 'extreme'
            }

            images.append(image)
            image_id += 1

        # 2. 相似尺寸的图片（30%）
        similar_count = max(1, int(count * 0.3))
        base_width = random.randint(100, 200)
        base_height = random.randint(100, 200)

        for i in range(similar_count):
            # 在基础尺寸上小幅波动
            width = max(10, int(base_width * random.uniform(0.9, 1.1)))
            height = max(10, int(base_height * random.uniform(0.9, 1.1)))

            image = {
                'id': f"image_{image_id}",
                'name': f"相似图片_{image_id}",  # 添加中文名称
                'width': width,
                'height': height,
                'aspect_ratio': width / height,
                'area': width * height,
                'type': 'similar'
            }

            images.append(image)
            image_id += 1

        # 3. 大小差异极大的图片（20%）
        large_small_count = max(1, int(count * 0.2))
        for i in range(large_small_count):
            if i % 2 == 0:
                # 大图片
                width = random.randint(250, 400)
                height = random.randint(250, 400)
            else:
                # 小图片
                width = random.randint(20, 50)
                height = random.randint(20, 50)

            image = {
                'id': f"image_{image_id}",
                'name': f"大小图片_{image_id}",  # 添加中文名称
                'width': width,
                'height': height,
                'aspect_ratio': width / height,
                'area': width * height,
                'type': 'large_small'
            }

            images.append(image)
            image_id += 1

        # 4. 正方形图片（10%）
        square_count = max(1, int(count * 0.1))
        for i in range(square_count):
            size = random.randint(50, 200)

            image = {
                'id': f"image_{image_id}",
                'name': f"正方形图片_{image_id}",  # 添加中文名称
                'width': size,
                'height': size,
                'aspect_ratio': 1.0,
                'area': size * size,
                'type': 'square'
            }

            images.append(image)
            image_id += 1

        # 5. 随机图片（剩余部分）
        random_count = count - len(images)
        if random_count > 0:
            for i in range(random_count):
                width = random.randint(50, 300)
                height = random.randint(50, 300)

                image = {
                    'id': f"image_{image_id}",
                    'name': f"随机图片_{image_id}",  # 添加中文名称
                    'width': width,
                    'height': height,
                    'aspect_ratio': width / height,
                    'area': width * height,
                    'type': 'random'
                }

                images.append(image)
                image_id += 1

        # 打乱图片顺序
        random.shuffle(images)

        log.info(f"生成了 {len(images)} 个具有挑战性的图片组合")
        log.info(f"  - 极端宽高比: {extreme_count}个")
        log.info(f"  - 相似尺寸: {similar_count}个")
        log.info(f"  - 大小差异极大: {large_small_count}个")
        log.info(f"  - 正方形: {square_count}个")
        log.info(f"  - 随机: {random_count}个")

        return images

    def generate_real_world_images(self, count: int = 50) -> List[Dict[str, Any]]:
        """
        生成模拟真实世界的图片组合
        基于常见的图片尺寸和比例

        Args:
            count: 图片总数

        Returns:
            List[Dict[str, Any]]: 图片列表，每个图片包含宽度、高度等信息
        """
        # 常见的图片尺寸和比例
        common_sizes = [
            # 横向照片
            (800, 600),   # 4:3
            (1024, 768),  # 4:3
            (1280, 720),  # 16:9
            (1920, 1080), # 16:9
            # 竖向照片
            (600, 800),   # 3:4
            (768, 1024),  # 3:4
            (720, 1280),  # 9:16
            (1080, 1920), # 9:16
            # 正方形
            (800, 800),
            (1000, 1000),
            # 宽幅
            (1200, 600),  # 2:1
            (1800, 600),  # 3:1
            # 高幅
            (600, 1200),  # 1:2
            (600, 1800),  # 1:3
        ]

        # 缩放因子，将大尺寸缩小到合适范围
        scale_factor = 0.25

        images = []
        image_id = 1

        for i in range(count):
            # 随机选择一个常见尺寸
            width, height = random.choice(common_sizes)

            # 应用缩放因子
            width = int(width * scale_factor)
            height = int(height * scale_factor)

            # 添加一些随机变化
            width = int(width * random.uniform(0.9, 1.1))
            height = int(height * random.uniform(0.9, 1.1))

            # 确保尺寸在合理范围内
            width = max(20, min(width, 500))
            height = max(20, min(height, 500))

            # 根据尺寸比例添加不同的中文名称
            if width > height:
                name_prefix = "横向照片"
            elif width < height:
                name_prefix = "竖向照片"
            else:
                name_prefix = "方形照片"

            image = {
                'id': f"image_{image_id}",
                'name': f"{name_prefix}_{image_id}",  # 添加中文名称
                'width': width,
                'height': height,
                'aspect_ratio': width / height,
                'area': width * height
            }

            images.append(image)
            image_id += 1

        log.info(f"生成了 {len(images)} 个模拟真实世界的图片")

        return images

    def save_to_json(self, images: List[Dict[str, Any]], output_path: str) -> None:
        """
        将图片数据保存为JSON文件

        Args:
            images: 图片列表
            output_path: 输出文件路径
        """
        # 确保目录存在
        output_dir = os.path.dirname(output_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir, exist_ok=True)

        try:
            # 使用UTF-8编码保存，确保中文正确显示
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(images, f, indent=2, ensure_ascii=False)  # 设置ensure_ascii=False以正确保存中文

            # 验证文件是否正确保存
            with open(output_path, 'r', encoding='utf-8') as f:
                test_read = json.load(f)
                print(f"成功验证文件 {output_path} 可以正确读取，包含 {len(test_read)} 个图片")

            log.info(f"图片数据已保存到: {output_path}")
            log.info(f"使用UTF-8编码，确保中文正确显示")
        except Exception as e:
            log.error(f"保存JSON文件时出错: {e}")
            # 尝试使用系统默认编码保存
            try:
                log.info(f"尝试使用系统默认编码 {system_encoding} 保存...")
                with open(output_path, 'w', encoding=system_encoding) as f:
                    # 不使用中文，避免编码问题
                    for img in images:
                        if 'name' in img:
                            img['name'] = img['id']  # 使用ID替代中文名称
                    json.dump(images, f, indent=2)
                log.info(f"使用系统默认编码保存成功")
            except Exception as e2:
                log.error(f"使用系统默认编码保存也失败: {e2}")

    def load_from_json(self, input_path: str) -> List[Dict[str, Any]]:
        """
        从JSON文件加载图片数据

        Args:
            input_path: 输入文件路径

        Returns:
            List[Dict[str, Any]]: 图片列表
        """
        try:
            # 尝试使用UTF-8编码读取
            with open(input_path, 'r', encoding='utf-8') as f:
                images = json.load(f)
            log.info(f"使用UTF-8编码从 {input_path} 加载了 {len(images)} 个图片")
        except UnicodeDecodeError:
            # 如果UTF-8解码失败，尝试使用系统默认编码
            log.warning(f"UTF-8解码失败，尝试使用系统默认编码 {system_encoding}")
            with open(input_path, 'r', encoding=system_encoding) as f:
                images = json.load(f)
            log.info(f"使用系统默认编码从 {input_path} 加载了 {len(images)} 个图片")
        except Exception as e:
            log.error(f"加载JSON文件时出错: {e}")
            # 返回空列表作为备选
            return []

        return images


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='测试数据生成器')

    # 基本参数
    parser.add_argument('--seed', type=int, default=None, help='随机种子')
    parser.add_argument('--output', type=str, default='test_data.json', help='输出文件路径')

    # 生成模式
    parser.add_argument('--mode', type=str, default='random',
                      choices=['random', 'fixed', 'challenging', 'real_world'],
                      help='生成模式')

    # 随机图片参数
    parser.add_argument('--count', type=int, default=50, help='图片数量')
    parser.add_argument('--min-size', type=int, default=50, help='最小图片尺寸（像素）')
    parser.add_argument('--max-size', type=int, default=300, help='最大图片尺寸（像素）')
    parser.add_argument('--distribution', type=str, default='random',
                      choices=['random', 'uniform', 'normal', 'bimodal'],
                      help='图片尺寸分布类型')

    args = parser.parse_args()

    # 创建测试数据生成器
    generator = TestDataGenerator(seed=args.seed)

    # 根据生成模式生成图片
    if args.mode == 'random':
        images = generator.generate_random_images(
            count=args.count,
            min_size=args.min_size,
            max_size=args.max_size,
            distribution=args.distribution
        )
    elif args.mode == 'fixed':
        # 固定尺寸示例
        sizes = [(100, 100), (200, 100), (100, 200), (300, 200)]
        counts = [10, 15, 15, 10]
        images = generator.generate_fixed_size_images(sizes, counts)
    elif args.mode == 'challenging':
        images = generator.generate_challenging_images(count=args.count)
    elif args.mode == 'real_world':
        images = generator.generate_real_world_images(count=args.count)

    # 保存图片数据
    generator.save_to_json(images, args.output)


if __name__ == "__main__":
    main()
