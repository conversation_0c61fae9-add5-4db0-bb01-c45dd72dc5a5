#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
快速测试脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    print("测试1: 导入ExcelProcessor...")
    from core.excel_processor import ExcelProcessor
    print("✓ ExcelProcessor导入成功")
    
    print("测试2: 创建ExcelProcessor实例...")
    processor = ExcelProcessor()
    print("✓ ExcelProcessor实例创建成功")
    
    print("测试3: 检查process_excel_file方法...")
    if hasattr(processor, 'process_excel_file'):
        print("✓ process_excel_file方法存在")
    else:
        print("✗ process_excel_file方法不存在")
    
    print("测试4: 检查列名标准化方法...")
    if hasattr(processor, '_standardize_column_names'):
        print("✓ _standardize_column_names方法存在")
    else:
        print("✗ _standardize_column_names方法不存在")
    
    print("测试5: 检查列验证方法...")
    if hasattr(processor, '_validate_required_columns'):
        print("✓ _validate_required_columns方法存在")
    else:
        print("✗ _validate_required_columns方法不存在")
    
    print("\n所有基础测试通过！")
    
except Exception as e:
    print(f"测试失败: {str(e)}")
    import traceback
    traceback.print_exc()
