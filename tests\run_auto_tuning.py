#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
运行自动调优测试脚本
自动生成测试数据，运行自动调优，并应用最佳参数
"""

import os
import sys
import time
import argparse
import subprocess
import logging
import locale

# 检查系统默认编码
system_encoding = locale.getpreferredencoding()
print(f"系统默认编码: {system_encoding}")

# 强制设置环境变量，确保使用UTF-8编码
os.environ['PYTHONIOENCODING'] = 'utf-8'

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
log = logging.getLogger("RunAutoTuning")

def run_command(command, timeout=None, show_output=True):
    """运行命令并返回输出"""
    log.info(f"运行命令: {command}")
    try:
        # 设置环境变量，确保使用UTF-8编码
        env = os.environ.copy()
        env['PYTHONIOENCODING'] = 'utf-8'  # 设置Python IO编码为UTF-8

        # 使用实时输出模式，这样用户可以看到命令的执行进度
        if show_output:
            process = subprocess.Popen(
                command,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                bufsize=1,
                universal_newlines=True,
                env=env,
                encoding='utf-8',  # 明确指定编码为UTF-8
                errors='replace'   # 替换无法解码的字符
            )

            # 实时显示输出
            output_lines = []
            for line in process.stdout:
                line = line.rstrip()
                output_lines.append(line)
                print(line)  # 实时打印输出

            # 等待进程结束
            process.wait()

            if process.returncode != 0:
                log.error(f"命令执行失败，返回码: {process.returncode}")
                return False
        else:
            # 不显示输出的模式
            process = subprocess.Popen(
                command,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                env=env,
                encoding='utf-8',  # 明确指定编码为UTF-8
                errors='replace'   # 替换无法解码的字符
            )
            stdout, stderr = process.communicate(timeout=timeout)

            if process.returncode != 0:
                log.error(f"命令执行失败: {stderr}")
                return False

        log.info(f"命令执行成功")
        return True
    except subprocess.TimeoutExpired:
        process.kill()
        log.error(f"命令执行超时")
        return False
    except Exception as e:
        log.error(f"命令执行出错: {e}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='运行自动调优测试')

    # 基本参数
    parser.add_argument('--width', type=int, default=1600, help='画布宽度（像素）')
    parser.add_argument('--spacing', type=int, default=5, help='图片间距（像素）')
    parser.add_argument('--count', type=int, default=50, help='测试图片数量')
    parser.add_argument('--seed', type=int, default=42, help='随机种子')

    # 调优方法
    parser.add_argument('--method', type=str, default='advanced', choices=['basic', 'advanced'],
                      help='调优方法: basic (网格搜索) 或 advanced (贝叶斯优化)')

    # 优化参数
    parser.add_argument('--n-calls', type=int, default=5, help='贝叶斯优化调用次数')

    # 测试模式
    parser.add_argument('--test-mode', action='store_true', help='启用测试模式，使用更少的图片和更简单的算法')

    # 输出参数
    parser.add_argument('--output-dir', type=str, default='results', help='输出目录')
    parser.add_argument('--db', type=str, default=None, help='DuckDB数据库文件路径')

    args = parser.parse_args()

    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)

    # 步骤1: 生成测试数据
    log.info("步骤1: 生成测试数据")
    test_data_path = os.path.join(args.output_dir, 'test_data.json')

    # 检查是否已存在测试数据
    if os.path.exists(test_data_path) and os.path.getsize(test_data_path) > 0:
        log.info(f"发现已存在的测试数据: {test_data_path}")
        log.info("跳过生成测试数据步骤")
    else:
        # 限制测试图片数量，避免处理时间过长
        actual_count = min(args.count, 100)  # 限制最大数量为100
        if actual_count < args.count:
            log.warning(f"图片数量过多，已限制为{actual_count}（原始请求: {args.count}）")

        # 使用-u参数强制Python使用unbuffered模式，避免编码问题
        generate_cmd = (
            f"python -u tests/generate_test_data.py "
            f"--mode challenging "
            f"--count {actual_count} "
            f"--seed {args.seed} "
            f"--output {test_data_path}"
        )

        if not run_command(generate_cmd, timeout=60):  # 设置60秒超时
            log.error("生成测试数据失败，退出")
            return

    # 步骤2: 运行自动调优
    log.info(f"步骤2: 运行{args.method}自动调优")

    # 检查是否已存在最佳参数
    best_params_path = os.path.join(args.output_dir, 'best_params.json')
    if os.path.exists(best_params_path) and os.path.getsize(best_params_path) > 0:
        log.info(f"发现已存在的最佳参数: {best_params_path}")
        log.info("是否要重新运行自动调优？(y/n)")
        response = input().strip().lower()
        if response != 'y':
            log.info("跳过自动调优步骤")
            return

    # 限制调用次数，避免处理时间过长
    actual_n_calls = min(args.n_calls, 5)  # 限制最大调用次数为5
    if actual_n_calls < args.n_calls:
        log.warning(f"调用次数过多，已限制为{actual_n_calls}（原始请求: {args.n_calls}）")

    # 添加测试模式标志，在测试模式下会使用更少的图片和更简单的算法
    test_mode_flag = "--test-mode" if args.test_mode else ""

    if args.method == 'basic':
        # 使用基本网格搜索，添加-u参数强制Python使用unbuffered模式
        tune_cmd = (
            f"python -u tests/auto_tune_tetris.py "
            f"--width {args.width} "
            f"--spacing {args.spacing} "
            f"--input {test_data_path} "
            f"--output-dir {args.output_dir} "
            f"{test_mode_flag} "
            f"--save-params"
        )
    else:
        # 使用高级贝叶斯优化，添加-u参数强制Python使用unbuffered模式
        tune_cmd = (
            f"python -u tests/auto_tune_tetris_advanced.py "
            f"--width {args.width} "
            f"--spacing {args.spacing} "
            f"--input {test_data_path} "
            f"--n-calls {actual_n_calls} "
            f"--output-dir {args.output_dir} "
            f"{test_mode_flag} "
            f"--save-params"
        )

    # 设置较长的超时时间，因为调优过程可能很耗时
    # 使用实时输出模式，这样用户可以看到进度
    if not run_command(tune_cmd, timeout=1800, show_output=True):  # 30分钟超时
        log.error("运行自动调优失败，退出")
        return

    # 步骤3: 应用最佳参数
    log.info("步骤3: 应用最佳参数")

    best_params_path = os.path.join(args.output_dir, 'best_params.json')
    config_output_path = os.path.join(args.output_dir, 'tetris_params.json')

    # 添加-u参数强制Python使用unbuffered模式
    apply_cmd = (
        f"python -u tests/apply_best_params.py "
        f"--input {best_params_path} "
        f"--output {config_output_path}"
    )

    if args.db:
        apply_cmd += f" --db {args.db}"

    if not run_command(apply_cmd):
        log.error("应用最佳参数失败，退出")
        return

    # 步骤4: 验证最佳参数
    log.info("步骤4: 验证最佳参数")

    # 从最佳参数文件中读取参数
    import json
    try:
        with open(best_params_path, 'r') as f:
            best_params = json.load(f)['best_parameters']

        horizontal_priority = best_params.get('horizontal_priority', 80)
        gap_filling_priority = best_params.get('gap_filling_priority', 70)
        rotation_priority = best_params.get('rotation_priority', 60)

        # 运行真实场景测试
        # 添加-u参数强制Python使用unbuffered模式
        verify_cmd = (
            f"python -u tests/test_real_world_scenario.py "
            f"--width {args.width} "
            f"--spacing {args.spacing} "
            f"--input {test_data_path} "
            f"--horizontal {horizontal_priority} "
            f"--gap-filling {gap_filling_priority} "
            f"--rotation {rotation_priority} "
            f"--output {os.path.join(args.output_dir, 'verification.png')} "
            f"--show-grid"
        )

        if not run_command(verify_cmd):
            log.error("验证最佳参数失败，退出")
            return

    except Exception as e:
        log.error(f"读取最佳参数失败: {e}")
        return

    log.info("自动调优测试完成!")
    log.info(f"最佳参数: 水平优先级={horizontal_priority}%, 空隙填充优先级={gap_filling_priority}%, 旋转优先级={rotation_priority}%")
    log.info(f"结果保存在: {args.output_dir}")


if __name__ == "__main__":
    main()
