#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
基本功能测试
"""

def test_imports():
    """测试导入"""
    try:
        print("测试导入...")
        
        # 测试rectpack
        try:
            import rectpack
            print("✓ rectpack 导入成功")
        except ImportError:
            print("✗ rectpack 导入失败")
        
        # 测试自定义模块
        from core.abstract_packer import AbstractPacker
        print("✓ AbstractPacker 导入成功")
        
        from core.rectpack_arranger import RectPackArranger
        print("✓ RectPackArranger 导入成功")
        
        from core.unified_image_arranger import UnifiedImageArranger
        print("✓ UnifiedImageArranger 导入成功")
        
        from core.simplified_image_classifier import SimplifiedImageClassifier
        print("✓ SimplifiedImageClassifier 导入成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 导入测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_basic_functionality():
    """测试基本功能"""
    try:
        print("\n测试基本功能...")
        
        from core.rectpack_arranger import RectPackArranger
        
        # 创建排列器
        arranger = RectPackArranger(
            container_width=1000,
            image_spacing=5,
            max_height=800
        )
        print("✓ RectPackArranger 创建成功")
        
        # 测试放置图片
        x, y, success = arranger.place_image(100, 200)
        print(f"✓ 图片放置: 位置=({x}, {y}), 成功={success}")
        
        # 获取布局信息
        layout_info = arranger.get_layout_info()
        print(f"✓ 布局信息: {layout_info}")
        
        return True
        
    except Exception as e:
        print(f"✗ 基本功能测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("基本功能测试")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_basic_functionality
    ]
    
    passed = 0
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n测试结果: {passed}/{len(tests)} 通过")
    
    if passed == len(tests):
        print("🎉 所有测试通过！")
    else:
        print("⚠️ 部分测试失败")

if __name__ == '__main__':
    main()
