#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试Excel处理修复的脚本
"""

import pandas as pd
import tempfile
import os
from core.excel_processor import ExcelProcessor

def create_test_excel_files():
    """创建测试用的Excel文件"""
    test_files = []
    
    # 测试文件1：标准格式
    data1 = {
        '图案全称': ['图案A', '图案B', '图案C'],
        '尺寸': ['10x20', '15x25', '30x30'],
        '数量': [1, 2, 3]
    }
    df1 = pd.DataFrame(data1)
    file1 = tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False)
    df1.to_excel(file1.name, index=False)
    test_files.append((file1.name, "标准格式"))
    
    # 测试文件2：不同列名格式
    data2 = {
        '图案': ['图案D', '图案E', '图案F'],
        '宽度': [10, 15, 30],
        '高度': [20, 25, 30],
        'qty': [1, 2, 3]
    }
    df2 = pd.DataFrame(data2)
    file2 = tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False)
    df2.to_excel(file2.name, index=False)
    test_files.append((file2.name, "不同列名格式"))
    
    # 测试文件3：英文列名
    data3 = {
        'pattern': ['PatternG', 'PatternH', 'PatternI'],
        'width': [10, 15, 30],
        'height': [20, 25, 30],
        'quantity': [1, 2, 3]
    }
    df3 = pd.DataFrame(data3)
    file3 = tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False)
    df3.to_excel(file3.name, index=False)
    test_files.append((file3.name, "英文列名"))
    
    # 测试文件4：混合格式
    data4 = {
        '图案名称': ['图案J', '图案K', '图案L'],
        '规格': ['10x20', '15x25', '30x30'],
        '件数': [1, 2, 3]
    }
    df4 = pd.DataFrame(data4)
    file4 = tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False)
    df4.to_excel(file4.name, index=False)
    test_files.append((file4.name, "混合格式"))
    
    return test_files

def test_excel_processor():
    """测试Excel处理器"""
    print("开始测试Excel处理器修复...")
    print("=" * 60)
    
    # 创建测试文件
    test_files = create_test_excel_files()
    
    # 创建Excel处理器
    processor = ExcelProcessor()
    
    # 测试每个文件
    for file_path, description in test_files:
        print(f"\n测试文件: {description}")
        print(f"文件路径: {file_path}")
        
        try:
            # 读取原始数据查看列名
            df_original = pd.read_excel(file_path)
            print(f"原始列名: {list(df_original.columns)}")
            
            # 使用process_excel_file方法
            patterns = processor.process_excel_file(file_path)
            
            if patterns:
                print(f"✓ 成功提取 {len(patterns)} 个图案")
                # 显示第一个图案的信息
                if patterns:
                    first_pattern = patterns[0]
                    print(f"  示例图案: {first_pattern.get('pattern_name', 'N/A')}")
                    print(f"  尺寸: {first_pattern.get('width_cm', 'N/A')}x{first_pattern.get('height_cm', 'N/A')} cm")
                    print(f"  数量: {first_pattern.get('quantity', 'N/A')}")
            else:
                print("✗ 未能提取到图案数据")
                
        except Exception as e:
            print(f"✗ 测试失败: {str(e)}")
            import traceback
            traceback.print_exc()
        
        # 清理临时文件
        try:
            os.unlink(file_path)
        except:
            pass
    
    print("\n" + "=" * 60)
    print("Excel处理器测试完成")

def test_column_standardization():
    """测试列名标准化功能"""
    print("\n开始测试列名标准化...")
    print("=" * 60)
    
    processor = ExcelProcessor()
    
    # 测试不同的列名组合
    test_cases = [
        {
            'name': '标准中文列名',
            'columns': ['图案全称', '尺寸', '数量'],
            'data': [['图案A', '10x20', 1], ['图案B', '15x25', 2]]
        },
        {
            'name': '英文列名',
            'columns': ['pattern', 'width', 'height', 'quantity'],
            'data': [['PatternA', 10, 20, 1], ['PatternB', 15, 25, 2]]
        },
        {
            'name': '混合列名',
            'columns': ['图案名称', '宽度', '高度', 'qty'],
            'data': [['图案A', 10, 20, 1], ['图案B', 15, 25, 2]]
        },
        {
            'name': '变体列名',
            'columns': ['图案', '规格', '件数'],
            'data': [['图案A', '10x20', 1], ['图案B', '15x25', 2]]
        }
    ]
    
    for test_case in test_cases:
        print(f"\n测试: {test_case['name']}")
        print(f"原始列名: {test_case['columns']}")
        
        # 创建DataFrame
        df = pd.DataFrame(test_case['data'], columns=test_case['columns'])
        
        # 应用列名标准化
        processor._standardize_column_names(df)
        
        print(f"标准化后: {list(df.columns)}")
        
        # 验证列验证
        validation_result = processor._validate_required_columns(df, is_standard_mode=True)
        if validation_result['success']:
            print("✓ 列验证通过")
        else:
            print(f"✗ 列验证失败: {validation_result['message']}")
    
    print("\n" + "=" * 60)
    print("列名标准化测试完成")

if __name__ == "__main__":
    test_column_standardization()
    test_excel_processor()
