#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试修复的脚本
验证ExcelProcessor的process_excel_file方法和RectPackLayoutWorker的success属性
"""

import sys
import os

def test_excel_processor():
    """测试ExcelProcessor的process_excel_file方法"""
    try:
        from core.excel_processor import ExcelProcessor
        processor = ExcelProcessor()
        print('✓ ExcelProcessor 创建成功')
        
        # 测试 process_excel_file 方法是否存在
        if hasattr(processor, 'process_excel_file'):
            print('✓ process_excel_file 方法存在')
            return True
        else:
            print('✗ process_excel_file 方法不存在')
            return False
            
    except Exception as e:
        print(f'✗ ExcelProcessor 测试失败: {str(e)}')
        return False

def test_rectpack_layout_worker():
    """测试RectPackLayoutWorker的success属性"""
    try:
        from ui.rectpack_layout_worker import RectPackLayoutWorker
        from core.image_indexer_duckdb import ImageIndexerDuckDB
        from utils.config_manager_duckdb import ConfigManagerDuckDB
        from core.excel_processor import ExcelProcessor
        
        config_manager = ConfigManagerDuckDB()
        image_indexer = ImageIndexerDuckDB()
        excel_processor = ExcelProcessor()
        
        worker = RectPackLayoutWorker(config_manager, image_indexer, excel_processor)
        print('✓ RectPackLayoutWorker 创建成功')
        
        # 测试 success 属性是否存在
        if hasattr(worker, 'success'):
            print('✓ success 属性存在')
            print(f'  初始值: {worker.success}')
            return True
        else:
            print('✗ success 属性不存在')
            return False
            
    except Exception as e:
        print(f'✗ RectPackLayoutWorker 测试失败: {str(e)}')
        import traceback
        traceback.print_exc()
        return False

def test_stop_method():
    """测试RectPackLayoutWorker的stop方法"""
    try:
        from ui.rectpack_layout_worker import RectPackLayoutWorker
        from core.image_indexer_duckdb import ImageIndexerDuckDB
        from utils.config_manager_duckdb import ConfigManagerDuckDB
        from core.excel_processor import ExcelProcessor
        
        config_manager = ConfigManagerDuckDB()
        image_indexer = ImageIndexerDuckDB()
        excel_processor = ExcelProcessor()
        
        worker = RectPackLayoutWorker(config_manager, image_indexer, excel_processor)
        
        # 测试 stop 方法是否存在
        if hasattr(worker, 'stop'):
            print('✓ stop 方法存在')
            # 测试调用stop方法
            worker.stop()
            print('✓ stop 方法调用成功')
            return True
        else:
            print('✗ stop 方法不存在')
            return False
            
    except Exception as e:
        print(f'✗ stop 方法测试失败: {str(e)}')
        return False

def main():
    """主测试函数"""
    print("开始测试修复...")
    print("=" * 50)
    
    # 测试ExcelProcessor
    print("1. 测试ExcelProcessor:")
    excel_success = test_excel_processor()
    print()
    
    # 测试RectPackLayoutWorker
    print("2. 测试RectPackLayoutWorker:")
    worker_success = test_rectpack_layout_worker()
    print()
    
    # 测试stop方法
    print("3. 测试stop方法:")
    stop_success = test_stop_method()
    print()
    
    # 总结
    print("=" * 50)
    print("测试结果总结:")
    print(f"ExcelProcessor.process_excel_file: {'✓ 通过' if excel_success else '✗ 失败'}")
    print(f"RectPackLayoutWorker.success: {'✓ 通过' if worker_success else '✗ 失败'}")
    print(f"RectPackLayoutWorker.stop: {'✓ 通过' if stop_success else '✗ 失败'}")
    
    all_success = excel_success and worker_success and stop_success
    print(f"\n总体结果: {'✓ 所有测试通过' if all_success else '✗ 部分测试失败'}")
    
    return all_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
