#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
RectPack排列器测试模块
测试新的RectPack算法的功能和性能
"""

import unittest
import sys
import os
import time
from typing import List, Dict, Any

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.rectpack_arranger import RectPackArranger
from core.unified_image_arranger import UnifiedImageArranger
from core.simplified_image_classifier import SimplifiedImageClassifier

class TestRectPackArranger(unittest.TestCase):
    """RectPack排列器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.container_width = 2000  # 2000像素宽度
        self.max_height = 3000      # 3000像素最大高度
        self.image_spacing = 10     # 10像素间距
        
        self.arranger = RectPackArranger(
            container_width=self.container_width,
            image_spacing=self.image_spacing,
            max_height=self.max_height
        )
    
    def test_basic_placement(self):
        """测试基本图片放置"""
        # 测试放置一个简单的矩形
        x, y, success = self.arranger.place_image(100, 200)
        
        self.assertTrue(success, "基本图片放置应该成功")
        self.assertEqual(x, 0, "第一个图片应该放在x=0位置")
        self.assertEqual(y, 0, "第一个图片应该放在y=0位置")
        self.assertEqual(len(self.arranger.placed_images), 1, "应该有一个已放置的图片")
    
    def test_multiple_placements(self):
        """测试多个图片放置"""
        test_images = [
            (100, 200),  # 图片1
            (150, 100),  # 图片2
            (200, 150),  # 图片3
            (80, 120),   # 图片4
        ]
        
        placed_count = 0
        for width, height in test_images:
            x, y, success = self.arranger.place_image(width, height)
            if success:
                placed_count += 1
        
        self.assertGreater(placed_count, 0, "至少应该成功放置一个图片")
        self.assertEqual(len(self.arranger.placed_images), placed_count, "已放置图片数量应该匹配")
    
    def test_rotation_functionality(self):
        """测试旋转功能"""
        # 创建一个启用旋转的排列器
        arranger_with_rotation = RectPackArranger(
            container_width=self.container_width,
            image_spacing=self.image_spacing,
            max_height=self.max_height
        )
        arranger_with_rotation.set_algorithm_params(rotation_enabled=True)
        
        # 放置一个可能需要旋转的图片
        image_data = {'name': 'test_image'}
        x, y, success = arranger_with_rotation.place_image(300, 100, image_data)
        
        self.assertTrue(success, "带旋转的图片放置应该成功")
    
    def test_layout_info(self):
        """测试布局信息获取"""
        # 放置几个图片
        self.arranger.place_image(100, 200)
        self.arranger.place_image(150, 100)
        
        layout_info = self.arranger.get_layout_info()
        
        self.assertIn('container_width', layout_info, "布局信息应该包含容器宽度")
        self.assertIn('container_height', layout_info, "布局信息应该包含容器高度")
        self.assertIn('utilization_percent', layout_info, "布局信息应该包含利用率")
        self.assertIn('placed_count', layout_info, "布局信息应该包含已放置数量")
        
        self.assertEqual(layout_info['placed_count'], 2, "应该显示2个已放置的图片")
        self.assertGreater(layout_info['utilization_percent'], 0, "利用率应该大于0")
    
    def test_reset_functionality(self):
        """测试重置功能"""
        # 放置一些图片
        self.arranger.place_image(100, 200)
        self.arranger.place_image(150, 100)
        
        # 重置
        self.arranger.reset()
        
        self.assertEqual(len(self.arranger.placed_images), 0, "重置后应该没有已放置的图片")
        self.assertEqual(self.arranger.placement_count, 0, "重置后放置计数应该为0")
        self.assertEqual(self.arranger.used_area, 0, "重置后使用面积应该为0")
    
    def test_container_overflow(self):
        """测试容器溢出处理"""
        # 尝试放置一个超过容器宽度的图片
        x, y, success = self.arranger.place_image(self.container_width + 100, 100)
        
        # 应该失败或者被处理（取决于算法实现）
        # 这里我们主要测试不会崩溃
        self.assertIsInstance(success, bool, "应该返回布尔值")

class TestUnifiedImageArranger(unittest.TestCase):
    """统一图片排列器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.arranger = UnifiedImageArranger()
        self.arranger.initialize(
            canvas_width_px=2000,
            max_height_px=3000,
            image_spacing_px=10,
            ppi=72
        )
    
    def test_image_arrangement(self):
        """测试图片排列"""
        # 创建测试图片数据
        test_patterns = [
            {
                'pattern_name': 'test1',
                'width_cm': 10.0,
                'height_cm': 15.0,
                'path': '/fake/path/test1.jpg',
                'index': 0,
                'row_number': 1
            },
            {
                'pattern_name': 'test2',
                'width_cm': 12.0,
                'height_cm': 8.0,
                'path': '/fake/path/test2.jpg',
                'index': 1,
                'row_number': 2
            },
            {
                'pattern_name': 'test3',
                'width_cm': 8.0,
                'height_cm': 10.0,
                'path': '/fake/path/test3.jpg',
                'index': 2,
                'row_number': 3
            }
        ]
        
        # 排列图片
        arranged_images = self.arranger.arrange_images(test_patterns)
        
        self.assertIsInstance(arranged_images, list, "应该返回列表")
        self.assertGreater(len(arranged_images), 0, "应该至少排列一个图片")
        
        # 检查排列结果的结构
        for image_info in arranged_images:
            self.assertIn('x', image_info, "图片信息应该包含x坐标")
            self.assertIn('y', image_info, "图片信息应该包含y坐标")
            self.assertIn('width', image_info, "图片信息应该包含宽度")
            self.assertIn('height', image_info, "图片信息应该包含高度")
            self.assertIn('unique_id', image_info, "图片信息应该包含唯一ID")
    
    def test_layout_statistics(self):
        """测试布局统计"""
        # 先排列一些图片
        test_patterns = [
            {
                'pattern_name': 'test1',
                'width_cm': 10.0,
                'height_cm': 15.0,
                'path': '/fake/path/test1.jpg',
                'index': 0,
                'row_number': 1
            }
        ]
        
        self.arranger.arrange_images(test_patterns)
        stats = self.arranger.get_layout_statistics()
        
        self.assertIsInstance(stats, dict, "统计信息应该是字典")
        if stats:  # 如果有统计信息
            self.assertIn('utilization_percent', stats, "应该包含利用率")

class TestSimplifiedImageClassifier(unittest.TestCase):
    """简化图片分类器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.classifier = SimplifiedImageClassifier()
    
    def test_preprocess_patterns(self):
        """测试图片预处理"""
        test_patterns = [
            {
                'pattern_name': 'test1',
                'width_cm': 10.0,
                'height_cm': 15.0,
                'path': '/fake/path/test1.jpg'
            },
            {
                'pattern_name': 'test2',
                'width_cm': 12.0,
                'height_cm': 8.0,
                'path': '/fake/path/test2.jpg'
            }
        ]
        
        processed = self.classifier.preprocess_patterns(test_patterns)
        
        self.assertEqual(len(processed), len(test_patterns), "处理后数量应该相同")
        
        for pattern in processed:
            self.assertIn('unique_id', pattern, "应该添加唯一ID")
            self.assertIn('area', pattern, "应该添加面积")
            self.assertIn('aspect_ratio', pattern, "应该添加长宽比")
            self.assertIn('image_class', pattern, "应该添加图片分类")
    
    def test_classify_images_compatibility(self):
        """测试分类兼容性"""
        test_patterns = [
            {
                'pattern_name': 'test1',
                'width_cm': 10.0,
                'height_cm': 15.0,
                'path': '/fake/path/test1.jpg'
            }
        ]
        
        class_a, class_b, class_c = self.classifier.classify_images(
            test_patterns, canvas_width_cm=200.0
        )
        
        # 检查返回的三个列表
        self.assertIsInstance(class_a, list, "A类应该是列表")
        self.assertIsInstance(class_b, list, "B类应该是列表")
        self.assertIsInstance(class_c, list, "C类应该是列表")
        
        # 在简化版本中，所有图片都应该在C类中
        self.assertEqual(len(class_a), 0, "A类应该为空")
        self.assertEqual(len(class_b), 0, "B类应该为空")
        self.assertEqual(len(class_c), len(test_patterns), "所有图片应该在C类中")

class TestPerformanceComparison(unittest.TestCase):
    """性能对比测试类"""
    
    def test_rectpack_vs_traditional_performance(self):
        """测试RectPack与传统算法的性能对比"""
        # 创建大量测试图片数据
        test_patterns = []
        for i in range(100):
            test_patterns.append({
                'pattern_name': f'test_{i}',
                'width_cm': 5.0 + (i % 20),
                'height_cm': 5.0 + ((i * 2) % 15),
                'path': f'/fake/path/test_{i}.jpg',
                'index': i,
                'row_number': i + 1
            })
        
        # 测试RectPack算法
        start_time = time.time()
        
        arranger = UnifiedImageArranger()
        arranger.initialize(
            canvas_width_px=2000,
            max_height_px=5000,
            image_spacing_px=10,
            ppi=72
        )
        
        arranged_images = arranger.arrange_images(test_patterns)
        rectpack_time = time.time() - start_time
        
        # 获取布局统计
        stats = arranger.get_layout_statistics()
        utilization = stats.get('utilization_percent', 0) if stats else 0
        
        print(f"\nRectPack算法性能测试结果:")
        print(f"处理时间: {rectpack_time:.3f}秒")
        print(f"成功排列: {len(arranged_images)}/{len(test_patterns)} 个图片")
        print(f"画布利用率: {utilization:.2f}%")
        
        # 基本断言
        self.assertGreater(len(arranged_images), 0, "应该至少排列一些图片")
        self.assertLess(rectpack_time, 10.0, "处理时间应该在合理范围内")

def run_tests():
    """运行所有测试"""
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试类
    test_classes = [
        TestRectPackArranger,
        TestUnifiedImageArranger,
        TestSimplifiedImageClassifier,
        TestPerformanceComparison
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    return result.wasSuccessful()

if __name__ == '__main__':
    print("开始RectPack算法测试...")
    success = run_tests()
    
    if success:
        print("\n所有测试通过！")
    else:
        print("\n部分测试失败，请检查代码。")
    
    sys.exit(0 if success else 1)
