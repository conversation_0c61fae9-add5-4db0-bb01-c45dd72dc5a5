#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
第一步测试：验证RectPack算法的基本分解是否成功
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_rectpack_basic_functions():
    """测试RectPack基本功能"""
    print("开始测试RectPack基本功能分解...")
    
    try:
        from core.rectpack_arranger import RectPackArranger
        print("✓ RectPackArranger导入成功")
        
        # 创建排列器
        arranger = RectPackArranger(
            container_width=200,
            image_spacing=2,
            max_height=500
        )
        print("✓ RectPackArranger创建成功")
        
        # 测试预处理函数
        result = arranger._preprocess_image_dimensions(100, 80)
        if result:
            width_with_spacing, height_with_spacing, was_rotated = result
            print(f"✓ 预处理函数测试成功: {width_with_spacing}x{height_with_spacing}, 旋转={was_rotated}")
        else:
            print("✗ 预处理函数返回None")
            
        # 测试放置图片
        x, y, success = arranger.place_image(100, 80)
        print(f"✓ 图片放置测试: 位置=({x}, {y}), 成功={success}")
        
        # 获取布局信息
        layout_info = arranger.get_layout_info()
        print(f"✓ 布局信息: 利用率={layout_info['utilization_percent']:.2f}%")
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("RectPack算法分解测试 - 第一步")
    print("=" * 50)
    
    success = test_rectpack_basic_functions()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 第一步测试完成！基本功能分解成功！")
        print("✓ place_image函数已成功分解为更小的模块")
        print("✓ 预处理、尝试放置、更新状态等功能已模块化")
    else:
        print("⚠️ 第一步测试失败，需要修复问题")
    print("=" * 50)
