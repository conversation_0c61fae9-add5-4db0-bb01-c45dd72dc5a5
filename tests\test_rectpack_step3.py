#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
第三步测试：验证RectPack算法的优化函数分解是否成功
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_optimization_functions():
    """测试优化算法的分解功能"""
    print("开始测试优化算法功能分解...")
    
    try:
        from core.rectpack_arranger import RectPackArranger
        print("✓ RectPackArranger导入成功")
        
        # 创建排列器
        arranger = RectPackArranger(
            container_width=200,
            image_spacing=2,
            max_height=500
        )
        print("✓ RectPackArranger创建成功")
        
        # 先放置一些图片
        test_images = [
            {'width': 80, 'height': 60, 'name': 'img1'},
            {'width': 70, 'height': 50, 'name': 'img2'},
            {'width': 60, 'height': 40, 'name': 'img3'}
        ]
        
        for img in test_images:
            x, y, success = arranger.place_image(img['width'], img['height'], img)
            if success:
                print(f"✓ 放置图片 {img['name']}: ({x}, {y})")
        
        # 测试保存状态函数
        original_state = arranger._save_current_state()
        print(f"✓ 保存状态函数测试: 图片数={len(original_state['images'])}, 利用率={original_state['stats']['utilization_percent']:.2f}%")
        
        # 测试获取配置组合函数
        configurations = arranger._get_all_configurations()
        print(f"✓ 获取配置组合函数测试: 共{len(configurations)}种配置")
        
        # 测试单个配置
        if configurations:
            test_config = configurations[0]
            utilization = arranger._test_configuration(test_config, original_state['images'])
            print(f"✓ 测试配置函数: 配置{test_config}, 利用率={utilization:.2f}%")
        
        # 测试放置所有图片函数
        arranger.reset()
        success = arranger._place_all_images(original_state['images'])
        print(f"✓ 放置所有图片函数测试: 成功={success}")
        
        # 测试寻找最佳配置函数（简化版，只测试前3个配置）
        arranger.placed_images = original_state['images'].copy()
        limited_configs = configurations[:3]  # 只测试前3个配置以节省时间
        
        # 临时替换_get_all_configurations方法
        original_method = arranger._get_all_configurations
        arranger._get_all_configurations = lambda: limited_configs
        
        best_config, best_utilization = arranger._find_best_configuration(original_state)
        print(f"✓ 寻找最佳配置函数测试: 最佳配置={best_config}, 最佳利用率={best_utilization:.2f}%")
        
        # 恢复原方法
        arranger._get_all_configurations = original_method
        
        # 测试应用最佳配置函数
        if best_config:
            result = arranger._apply_best_configuration(best_config, best_utilization, original_state)
            print(f"✓ 应用最佳配置函数测试: 结果={result}")
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("RectPack算法分解测试 - 第三步")
    print("=" * 50)
    
    success = test_optimization_functions()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 第三步测试完成！优化算法分解成功！")
        print("✓ optimize_for_utilization函数已成功分解为更小的模块")
        print("✓ 保存状态、寻找配置、测试配置、应用配置等功能已模块化")
    else:
        print("⚠️ 第三步测试失败，需要修复问题")
    print("=" * 50)
