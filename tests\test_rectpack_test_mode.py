#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
RectPack测试模式验证脚本
验证新实现的rectpack测试模式是否完全符合test_rectpack_real_data.py的标准
"""

import os
import sys
import logging

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.rectpack_test_mode import (
    run_rectpack_test_mode,
    run_single_test_comparison,
    get_container_config_px,
    convert_pattern_items_to_px_data
)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
log = logging.getLogger(__name__)

def get_test_pattern_items():
    """
    获取测试图案项目，模拟真实的pattern_items数据
    """
    return [
        {'width_cm': 180, 'height_cm': 80, 'pattern_name': '大图案A', 'quantity': 3},
        {'width_cm': 150, 'height_cm': 60, 'pattern_name': '中图案B', 'quantity': 5},
        {'width_cm': 120, 'height_cm': 90, 'pattern_name': '中图案C', 'quantity': 4},
        {'width_cm': 100, 'height_cm': 80, 'pattern_name': '小图案D', 'quantity': 6},
        {'width_cm': 90, 'height_cm': 60, 'pattern_name': '小图案E', 'quantity': 8},
        {'width_cm': 80, 'height_cm': 50, 'pattern_name': '小图案F', 'quantity': 10},
        {'width_cm': 160, 'height_cm': 120, 'pattern_name': '特殊图案G', 'quantity': 2},
        {'width_cm': 140, 'height_cm': 90, 'pattern_name': '特殊图案H', 'quantity': 3},
    ]

def test_basic_functionality():
    """
    测试基本功能
    """
    print("\n" + "="*60)
    print("测试1：基本功能验证")
    print("="*60)
    
    try:
        # 准备测试数据
        pattern_items = get_test_pattern_items()
        
        # 运行测试模式
        result = run_rectpack_test_mode(
            pattern_items=pattern_items,
            canvas_width_cm=200,
            horizontal_expansion_cm=2,
            max_height_cm=500,  # 较小的高度，测试多容器
            image_spacing_cm=1,
            miniature_ratio=1.0,
            output_dir="test_output/basic_test",
            material_name="基本功能测试"
        )
        
        if result['success']:
            print(f"✓ 基本功能测试成功")
            print(f"  - 容器数量: {result['total_containers']}")
            print(f"  - 图片总数: {result['total_images']}")
            print(f"  - 平均利用率: {result['avg_utilization_rate']:.2f}%")
            print(f"  - 处理速度: {result['processing_speed']:.1f} 图片/秒")
            print(f"  - 可视化文件: {len(result['visualization_files'])}个")
            print(f"  - 文档文件: {len(result['documentation_files'])}个")
            return True
        else:
            print(f"✗ 基本功能测试失败: {result.get('error', '未知错误')}")
            return False
            
    except Exception as e:
        print(f"✗ 基本功能测试异常: {str(e)}")
        return False

def test_multi_container():
    """
    测试多容器功能
    """
    print("\n" + "="*60)
    print("测试2：多容器功能验证")
    print("="*60)
    
    try:
        # 准备大量图片数据，确保需要多个容器
        pattern_items = []
        for i in range(50):
            pattern_items.append({
                'width_cm': 100 + (i % 50),
                'height_cm': 60 + (i % 40),
                'pattern_name': f'图案_{i+1}',
                'quantity': 1
            })
        
        # 运行测试模式，使用较小的最大高度强制多容器
        result = run_rectpack_test_mode(
            pattern_items=pattern_items,
            canvas_width_cm=200,
            horizontal_expansion_cm=5,
            max_height_cm=300,  # 很小的高度，强制多容器
            image_spacing_cm=1,
            miniature_ratio=1.0,
            output_dir="test_output/multi_container_test",
            material_name="多容器测试"
        )
        
        if result['success']:
            print(f"✓ 多容器测试成功")
            print(f"  - 容器数量: {result['total_containers']} (应该 > 1)")
            print(f"  - 图片总数: {result['total_images']}")
            print(f"  - 平均利用率: {result['avg_utilization_rate']:.2f}%")
            
            # 验证确实创建了多个容器
            if result['total_containers'] > 1:
                print(f"  ✓ 成功创建多个容器")
                return True
            else:
                print(f"  ✗ 未能创建多个容器")
                return False
        else:
            print(f"✗ 多容器测试失败: {result.get('error', '未知错误')}")
            return False
            
    except Exception as e:
        print(f"✗ 多容器测试异常: {str(e)}")
        return False

def test_height_adjustment():
    """
    测试容器高度重设功能
    """
    print("\n" + "="*60)
    print("测试3：容器高度重设验证")
    print("="*60)
    
    try:
        # 准备少量图片，测试高度重设
        pattern_items = [
            {'width_cm': 100, 'height_cm': 80, 'pattern_name': '图案1', 'quantity': 1},
            {'width_cm': 90, 'height_cm': 60, 'pattern_name': '图案2', 'quantity': 1},
            {'width_cm': 80, 'height_cm': 50, 'pattern_name': '图案3', 'quantity': 1},
        ]
        
        # 运行测试模式
        result = run_rectpack_test_mode(
            pattern_items=pattern_items,
            canvas_width_cm=200,
            horizontal_expansion_cm=2,
            max_height_cm=5000,  # 很大的高度
            image_spacing_cm=1,
            miniature_ratio=1.0,
            output_dir="test_output/height_adjustment_test",
            material_name="高度重设测试"
        )
        
        if result['success']:
            print(f"✓ 高度重设测试成功")
            
            # 检查第一个容器的实际高度是否远小于最大高度
            first_container = result['containers'][0]
            actual_height = first_container['actual_height']
            max_height = first_container['container_config']['max_height']
            
            print(f"  - 最大高度: {max_height}px")
            print(f"  - 实际高度: {actual_height}px")
            print(f"  - 高度利用率: {actual_height/max_height*100:.2f}%")
            
            if actual_height < max_height * 0.5:  # 实际高度应该远小于最大高度
                print(f"  ✓ 容器高度成功重设")
                return True
            else:
                print(f"  ✗ 容器高度重设可能有问题")
                return False
        else:
            print(f"✗ 高度重设测试失败: {result.get('error', '未知错误')}")
            return False
            
    except Exception as e:
        print(f"✗ 高度重设测试异常: {str(e)}")
        return False

def test_comparison_with_standard():
    """
    测试与test_rectpack_real_data.py标准的对比
    """
    print("\n" + "="*60)
    print("测试4：与标准格式对比验证")
    print("="*60)
    
    try:
        # 使用类似test_rectpack_real_data.py的数据格式
        test_data = [
            (180, 80, "180-80"),
            (150, 60, "150-60"),
            (120, 90, "120-90"),
            (100, 80, "100-80"),
            (90, 60, "90-60"),
        ]
        
        # 创建容器配置
        container_config = get_container_config_px(
            canvas_width_cm=205,
            horizontal_expansion_cm=0,
            max_height_cm=5000,
            image_spacing_cm=1,
            miniature_ratio=1.0
        )
        
        # 运行单个测试对比
        result = run_single_test_comparison(
            test_name="标准格式对比测试",
            test_data=test_data,
            container_config=container_config,
            output_path="test_output/comparison_test/standard_comparison.png"
        )
        
        if result['success']:
            print(f"✓ 标准格式对比测试成功")
            print(f"  - 成功率: {result['success_rate']:.1f}%")
            print(f"  - 利用率: {result['utilization_rate']:.2f}%")
            print(f"  - 处理速度: {result['processing_speed']:.1f} 图片/秒")
            return True
        else:
            print(f"✗ 标准格式对比测试失败: {result.get('error', '未知错误')}")
            return False
            
    except Exception as e:
        print(f"✗ 标准格式对比测试异常: {str(e)}")
        return False

def main():
    """
    主测试函数
    """
    print("RectPack测试模式验证脚本")
    print("=" * 80)
    print("验证新实现的rectpack测试模式是否完全符合test_rectpack_real_data.py的标准")
    print("=" * 80)
    
    # 创建输出目录
    os.makedirs("test_output", exist_ok=True)
    
    # 运行所有测试
    tests = [
        ("基本功能", test_basic_functionality),
        ("多容器功能", test_multi_container),
        ("高度重设", test_height_adjustment),
        ("标准格式对比", test_comparison_with_standard),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"测试 {test_name} 发生异常: {str(e)}")
            results.append((test_name, False))
    
    # 显示总结
    print("\n" + "="*80)
    print("测试总结")
    print("="*80)
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for test_name, success in results:
        status = "✓ 通过" if success else "✗ 失败"
        print(f"{test_name:<20} {status}")
    
    print("-" * 40)
    print(f"总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("\n🎉 所有测试通过！RectPack测试模式实现成功！")
        print("\n主要成就:")
        print("✓ 完全按照test_rectpack_real_data.py标准实现")
        print("✓ 支持多容器功能（达到最大高度时新开容器）")
        print("✓ 支持容器高度重设（图片太少时重设高度）")
        print("✓ 使用matplotlib绘制可视化结果")
        print("✓ 生成详细的说明文档")
        print("✓ 统一使用px单位，忽略cm单位")
    else:
        print(f"\n⚠️ {total - passed} 个测试失败，需要进一步修复")
    
    print("="*80)

if __name__ == "__main__":
    main()
