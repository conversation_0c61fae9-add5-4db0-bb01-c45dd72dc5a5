#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
工作线程基类模块

为所有工作线程提供统一的接口和基本功能：
1. 支持多线程处理
2. 支持进度显示
3. 支持状态更新
4. 支持错误处理
5. 支持日志记录
"""

import time
from PyQt6.QtCore import QThread, pyqtSignal

# 配置日志
from utils.log_config import get_logger
log = get_logger("BaseWorker")

class BaseWorker(QThread):
    """工作线程基类

    为所有工作线程提供统一的接口和基本功能
    """

    # 通用信号定义
    progress_signal = pyqtSignal(int)  # 进度值 (0-100)
    log_signal = pyqtSignal(str)  # 日志信号
    status_signal = pyqtSignal(str)  # 状态信号
    error_signal = pyqtSignal(str)  # 错误信号

    def __init__(self):
        """初始化工作线程基类"""
        super().__init__()
        self.is_canceled = False
        self.start_time = None

    def stop(self):
        """停止任务"""
        self.is_canceled = True
        self.log_signal.emit("任务停止请求已发送")

    def run(self):
        """线程执行函数，子类必须重写此方法"""
        raise NotImplementedError("子类必须实现run方法")

    def _start_task(self, task_name):
        """开始任务，初始化计时器和状态

        Args:
            task_name: 任务名称
        """
        self.start_time = time.time()
        self.is_canceled = False
        self.log_signal.emit(f"开始{task_name}任务")
        self.progress_signal.emit(0)

    def _end_task(self, success, message, elapsed_time=None):
        """结束任务，记录日志和状态

        Args:
            success: 是否成功
            message: 消息
            elapsed_time: 耗时（秒），如果为None则自动计算
        """
        if elapsed_time is None and self.start_time is not None:
            elapsed_time = time.time() - self.start_time

        if success:
            self.log_signal.emit(f"任务完成: {message}" + (f"，耗时 {elapsed_time:.2f} 秒" if elapsed_time else ""))
            self.status_signal.emit("任务完成")
        else:
            self.log_signal.emit(f"任务失败: {message}")
            self.status_signal.emit("任务失败")

    def _check_canceled(self):
        """检查是否已取消任务

        Returns:
            bool: 如果已取消返回True
        """
        if self.is_canceled:
            self.log_signal.emit("任务已取消")
            return True
        return False
