#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
索引图库工作线程模块

提供索引图库的异步处理功能：
1. 支持多线程处理
2. 支持进度显示
3. 支持状态更新
4. 支持错误处理
"""

import os
import logging
import time
from PyQt6.QtCore import pyqtSignal

# 导入基类
from ui.base_worker import BaseWorker

# 配置日志
log = logging.getLogger("IndexLibraryWorker")

class IndexLibraryWorker(BaseWorker):
    """索引图库工作线程

    提供异步处理索引图库的功能，包括：
    1. 多线程索引处理
    2. 进度显示
    3. 状态更新
    4. 错误处理
    """

    # 特定信号定义
    finished_signal = pyqtSignal(bool, str, int)  # 完成信号，参数为成功标志、消息和图片数量

    def __init__(self):
        """初始化索引图库工作线程"""
        super().__init__()
        self.library_path = ""
        self.image_indexer = None

    def set_library_path(self, library_path):
        """设置图库路径

        Args:
            library_path: 图库路径
        """
        self.library_path = library_path

    def set_image_indexer(self, image_indexer):
        """设置图片索引器

        Args:
            image_indexer: 图片索引器实例
        """
        self.image_indexer = image_indexer

        # 连接图片索引器的信号
        self.image_indexer.progress_signal.connect(self.on_indexer_progress)
        self.image_indexer.status_signal.connect(self.on_indexer_status)
        self.image_indexer.error_signal.connect(self.on_indexer_error)

    def stop(self):
        """停止索引处理"""
        super().stop()
        if self.image_indexer:
            self.image_indexer.stop_indexing()

    def run(self):
        """线程执行函数"""
        try:
            # 检查参数
            if not self.library_path or not os.path.exists(self.library_path):
                self.error_signal.emit("图库路径无效")
                self.finished_signal.emit(False, "图库路径无效", 0)
                return

            if not self.image_indexer:
                self.error_signal.emit("图片索引器未设置")
                self.finished_signal.emit(False, "图片索引器未设置", 0)
                return

            # 开始任务
            self._start_task("索引图库")
            self.log_signal.emit(f"开始索引图库: {self.library_path}")
            self.status_signal.emit("正在索引图库...")

            # 执行索引操作
            start_time = time.time()
            success, message, count = self.image_indexer.scan_library(self.library_path)
            elapsed_time = time.time() - start_time

            # 结束任务
            self._end_task(success, f"{count} 个图片", elapsed_time)

            # 发送完成信号
            self.finished_signal.emit(success, message, count)

        except Exception as e:
            log.error(f"索引图库时出错: {str(e)}")
            self.error_signal.emit(f"索引图库时出错: {str(e)}")
            self.finished_signal.emit(False, f"索引图库时出错: {str(e)}", 0)

    def on_indexer_progress(self, progress):
        """处理索引器进度信号

        Args:
            progress: 进度值 (0-100)
        """
        self.progress_signal.emit(progress)

    def on_indexer_status(self, status):
        """处理索引器状态信号

        Args:
            status: 状态信息
        """
        self.status_signal.emit(status)
        self.log_signal.emit(status)

    def on_indexer_error(self, error):
        """处理索引器错误信号

        Args:
            error: 错误信息
        """
        self.error_signal.emit(error)
        self.log_signal.emit(f"错误: {error}")
