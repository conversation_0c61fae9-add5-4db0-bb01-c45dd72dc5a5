#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
布局工作线程模块

提供布局任务的异步处理功能：
1. 支持多线程处理
2. 支持进度显示
3. 支持状态更新
4. 支持错误处理
"""

import os
import sys
import math
import logging
import time
import traceback
import random
import gc
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple
import io

from PyQt6.QtCore import pyqtSignal

# 导入基类
from ui.base_worker import BaseWorker

# 导入图片排列器
from core.image_arranger import ImageArranger
from core.image_classifier import ImageClassifier
from utils.photoshop_helper import PhotoshopHelper
from utils.log_file_creator import create_log_file_async
from utils.memory_manager import MemoryManager
from utils.stream_processor import StreamProcessor
import photoshop.api as ps

# 导入图片处理器
from utils.image_processor import get_image_processor

# 配置日志
from utils.log_config import get_logger
log = get_logger("LayoutWorker")

class LayoutWorker(BaseWorker):
    """图片布局工作线程

    提供异步处理图片布局的功能，包括：
    1. 智能排列图片
    2. 自动旋转图片以提高利用率
    3. 高效内存管理
    4. 错误恢复机制
    """

    # 特定信号定义
    finished = pyqtSignal()  # 完成信号
    new_canvas_needed = pyqtSignal(str, list)  # 新画布信号，参数为材质名称和剩余图案
    error_signal = pyqtSignal(str, str)  # 错误信号，参数为错误类型和错误消息
    status_signal = pyqtSignal(str, dict)  # 状态信号，参数为状态类型和状态数据

    def __init__(self):
        """初始化布局工作线程"""
        super().__init__()

        # 初始化变量
        self.pattern_items = []  # 图案项目列表
        self.canvas_name = ""
        self.material_name = ""
        self.output_path = ""
        self.success = False
        self.canvas_sequence = 1  # 画布序号
        self.remaining_patterns = []  # 未处理完的图案

        # 添加图片ID跟踪机制，避免重复处理同一图片
        self.processed_image_ids = set()  # 用于跟踪已处理的图片ID

        # 导入常量
        from utils.constants import constants, get_constant

        # 画布设置 - 使用常量
        self.canvas_width_m = get_constant('CANVAS_DEFAULT_CANVAS_WIDTH_M', 2.0)
        self.ppi = get_constant('PS_DEFAULT_PPI', 72)
        self.image_spacing_cm = get_constant('CANVAS_DEFAULT_IMAGE_SPACING_CM', 0.1)
        self.horizontal_expansion_cm = get_constant('CANVAS_DEFAULT_HORIZONTAL_EXPANSION_CM', 0)
        self.max_height_cm = get_constant('PS_MAX_CANVAS_HEIGHT_CM', 5000)  # 默认最大高度50米
        self.canvas_width_cm_with_expansion = self.canvas_width_m * 100  # 初始化时不考虑水平拓展

        # 执行控制
        self.use_photoshop = True
        self.force_fit_remaining = False  # 是否强制将剩余图片放入当前画布

        # 测试模式设置
        self.is_test_mode = False  # 是否开启测试模式
        self.miniature_ratio = 0.02  # 缩小模型比率
        self.is_test_all_data = False  # 是否测试全部数据

        # 图片处理器
        self.image_processor = None

        # 性能监控
        self.last_progress_time = None
        self.processed_images = 0
        self.total_images = 0
        self.error_count = 0
        self.retry_count = 0
        self.max_retries = 3  # 最大重试次数

        # 排列速度监控
        self.arrangement_start_time = None  # 排列开始时间
        self.images_arranged = 0  # 已排列图片数
        self.last_speed_update_time = None  # 上次速度更新时间
        self.speed_update_interval = 2.0  # 速度更新间隔（秒）

        # 内存管理
        self.memory_check_interval = 10  # 每处理多少块图片检查一次内存
        self.last_memory_check = 0

        # 内存管理
        self.memory_manager = MemoryManager()
        self.stream_processor = StreamProcessor(
            batch_size=100,  # 默认批处理大小
            max_queue_size=1000
        )

        # 保存异步任务线程的引用，防止线程被垃圾回收
        self.async_threads = []

    def set_pattern_items(self, items):
        """设置图案项目列表"""
        self.pattern_items = items

    def set_canvas_name(self, name):
        """设置画布名称"""
        self.canvas_name = name

    def set_material_name(self, name):
        """设置材料名称"""
        self.material_name = name

    def set_output_path(self, path):
        """设置输出路径"""
        self.output_path = path

    def set_canvas_sequence(self, sequence):
        """设置画布序号"""
        self.canvas_sequence = sequence

    def set_canvas_settings(self, settings):
        """设置画布参数"""
        self.canvas_width_m = settings.get('canvas_width_m', 2.0)
        self.ppi = settings.get('ppi', 72)
        self.image_spacing_cm = settings.get('image_spacing_cm', 0.1)
        self.horizontal_expansion_cm = settings.get('horizontal_expansion_cm', 0)
        self.max_height_cm = settings.get('max_height_cm', 5000)
        # 更新包含水平拓展的画布宽度
        self.canvas_width_cm_with_expansion = self.canvas_width_m * 100 + self.horizontal_expansion_cm

    def set_use_photoshop(self, use_ps):
        """设置是否使用Photoshop"""
        self.use_photoshop = use_ps

    def set_test_mode_settings(self, settings):
        """设置测试模式参数

        Args:
            settings: 测试模式设置字典，包含is_test_mode、miniature_ratio和is_test_all_data
        """
        if settings and isinstance(settings, dict):
            self.is_test_mode = settings.get('is_test_mode', False)
            self.miniature_ratio = settings.get('miniature_ratio', 0.02)
            self.is_test_all_data = settings.get('is_test_all_data', False)
            self.log_signal.emit(f"测试模式设置: 启用={self.is_test_mode}, 缩小比率={self.miniature_ratio}, 测试全部数据={self.is_test_all_data}")

            # 如果是测试模式，不使用Photoshop
            if self.is_test_mode:
                self.use_photoshop = False

    def set_config_manager(self, config_manager):
        """设置配置管理器

        Args:
            config_manager: 配置管理器实例
        """
        self.config_manager = config_manager

    def stop(self):
        """停止任务"""
        super().stop()
        self.log_signal.emit("布局任务停止请求已发送")

        # 停止所有异步线程
        if hasattr(self, 'async_threads') and self.async_threads:
            self.log_signal.emit(f"正在停止 {len(self.async_threads)} 个异步任务...")
            for thread in self.async_threads[:]:  # 使用副本进行迭代，避免在迭代过程中修改列表
                try:
                    if thread.isRunning():
                        thread.wait(1000)  # 最多等待1秒
                    if thread in self.async_threads:
                        self.async_threads.remove(thread)
                except Exception as e:
                    self.log_signal.emit(f"停止异步任务失败: {str(e)}")

        # 测试模式下不调用Photoshop
        if hasattr(self, 'is_test_mode') and self.is_test_mode:
            self.log_signal.emit("测试模式下跳过Photoshop资源清理")
            return

        # 尝试清理Photoshop资源，但不要阻塞线程
        try:
            from utils.photoshop_helper import PhotoshopHelper
            # 使用安全清理函数
            PhotoshopHelper.safe_cleanup_resources()
        except Exception as e:
            self.log_signal.emit(f"停止时清理资源失败: {str(e)}")

    def _cm_to_px(self, cm_value):
        """将厘米转换为像素

        Args:
            cm_value: 厘米值

        Returns:
            int: 像素值
        """
        # 1厘米 = 0.393701英寸
        inches = cm_value * 0.393701
        # 像素 = 英寸 * PPI
        pixels = int(inches * self.ppi)
        return pixels

    def run(self):
        """运行布局任务"""
        try:
            # 初始化性能监控
            self._start_task("布局")
            self.last_progress_time = self.start_time
            self.processed_images = 0
            self.error_count = 0
            self.retry_count = 0

            # 启动内存监控
            self.memory_manager.start_monitoring(interval=10.0)  # 每10秒监控一次内存

            # 初始化图片处理器
            test_mode_config = {
                'miniature_ratio': self.miniature_ratio,
                'is_test_all_data': self.is_test_all_data
            }
            self.image_processor = get_image_processor(self.is_test_mode, test_mode_config)

            # 检查测试模式设置
            if self.is_test_mode:
                self.log_signal.emit(f"测试模式已启用，缩小模型比率: {self.miniature_ratio}")
                self.log_signal.emit("测试模式下不启动Photoshop，使用色块替代图片")

            self.log_signal.emit(f"开始处理 {self.material_name} 的布局任务")
            self.progress_signal.emit(0)

            # 发送状态信息
            self.status_signal.emit("start", {
                "material_name": self.material_name,
                "start_time": self.start_time,
                "estimated_duration": 0
            })

            # 转换单位: m -> cm -> px
            canvas_width_cm = self.canvas_width_m * 100

            # 考虑横向扩展 (厘米单位)
            self.canvas_width_cm_with_expansion = canvas_width_cm + self.horizontal_expansion_cm
            self.log_signal.emit(f"画布宽度: {canvas_width_cm}厘米 + 水平拓展: {self.horizontal_expansion_cm}厘米 = {self.canvas_width_cm_with_expansion}厘米")

            # 转换为像素
            canvas_width_px = self._cm_to_px(self.canvas_width_cm_with_expansion)

            # 设置最大高度限制
            max_height_px = self._cm_to_px(self.max_height_cm)

            # 初始化图片信息列表
            images_info = []
            processed_patterns = []  # 已处理的图案列表
            remaining_patterns = []  # 剩余未处理图案列表

            # 添加间距日志信息
            # 创建装箱器 - 根据实际PPI计算间距
            spacing_px = max(1, int(self.image_spacing_cm * self.ppi / 2.54))
            self.log_signal.emit(f"图片间距: {spacing_px}像素 (基于{self.image_spacing_cm}厘米和{self.ppi}PPI)")

            # 获取RectPack算法设置
            use_rectpack = False
            try:
                if hasattr(self, 'config_manager') and self.config_manager:
                    rectpack_settings = self.config_manager.get_rectpack_settings()
                    use_rectpack = rectpack_settings.get('use_rectpack_algorithm', False)
                    self.log_signal.emit(f"RectPack算法设置: {'启用' if use_rectpack else '禁用'}")
            except Exception as e:
                self.log_signal.emit(f"获取RectPack设置失败: {str(e)}，使用传统算法")
                use_rectpack = False

            # 创建图片排列器
            self.log_signal.emit(f"创建图片排列器使用画布宽度: {canvas_width_px}像素（包含水平拓展）")
            self.image_arranger = ImageArranger(log_signal=self.log_signal, use_rectpack=use_rectpack)
            self.image_arranger.progress_signal.connect(self.progress_signal)
            self.image_arranger.error_signal.connect(lambda msg: self.error_signal.emit("排列错误", msg))

            # 初始化图片排列器
            self.image_arranger.initialize(
                canvas_width_px=canvas_width_px,
                max_height_px=max_height_px,
                image_spacing_px=spacing_px,
                ppi=self.ppi
            )

            # 获取算法参数
            algorithm_params = {}
            try:
                if hasattr(self, 'config_manager') and self.config_manager:
                    params = self.config_manager.get_c_class_algorithm_params()
                    algorithm_params = {
                        'c_horizontal_priority': params.get('c_horizontal_priority', 80),
                        'c_gap_filling_priority': params.get('c_gap_filling_priority', 70),
                        'c_rotation_priority': params.get('c_rotation_priority', 60)
                    }
            except Exception as e:
                self.log_signal.emit(f"获取算法参数失败: {str(e)}，将使用默认参数")
                algorithm_params = {
                    'c_horizontal_priority': 80,
                    'c_gap_filling_priority': 70,
                    'c_rotation_priority': 60
                }

            # 设置图片排列器的算法参数
            self.image_arranger.set_algorithm_params(
                horizontal_priority=algorithm_params['c_horizontal_priority'],
                gap_filling_priority=algorithm_params['c_gap_filling_priority'],
                rotation_priority=algorithm_params['c_rotation_priority']
            )

            # 获取算法设置
            try:
                # 如果有配置管理器，使用配置管理器获取算法设置
                if hasattr(self, 'config_manager') and self.config_manager:
                    algorithm_settings = self.config_manager.get_algorithm_settings()
                    self.log_signal.emit("使用配置管理器获取算法设置")
                else:
                    # 如果没有配置管理器，使用默认设置
                    self.log_signal.emit("未设置配置管理器，使用默认算法设置")
                    algorithm_settings = {
                        # 图片分类参数
                        'class_a_threshold': 95,
                        'class_b_error_range': 5,

                        # 旋转决策参数
                        'class_a_rotation_threshold': 20,
                        'class_b_rotation_threshold': 20,
                        'class_c_rotation_threshold': 15,
                        'extreme_ratio_threshold': 3.0,
                        'extreme_ratio_utilization': 60,

                        # 行空隙填充参数
                        'row_utilization_threshold': 95,
                        'class_c_gap_error_range': 5,
                        'enable_row_gap_filling': True
                    }
            except Exception as e:
                # 如果获取算法设置失败，使用默认设置
                self.log_signal.emit(f"获取算法设置失败: {str(e)}，使用默认设置")
                algorithm_settings = {
                    # 图片分类参数
                    'class_a_threshold': 95,
                    'class_b_error_range': 5,

                    # 旋转决策参数
                    'class_a_rotation_threshold': 20,
                    'class_b_rotation_threshold': 20,
                    'class_c_rotation_threshold': 15,
                    'extreme_ratio_threshold': 3.0,
                    'extreme_ratio_utilization': 60,

                    # 行空隙填充参数
                    'row_utilization_threshold': 95,
                    'class_c_gap_error_range': 5,
                    'enable_row_gap_filling': True
                }

            # 计算宽度阈值（画布宽度的百分比）
            class_a_threshold = algorithm_settings['class_a_threshold'] / 100.0
            class_b_error_range = algorithm_settings['class_b_error_range'] / 100.0

            # 使用包含水平拓展的画布宽度
            canvas_width_cm = self.canvas_width_cm_with_expansion
            # 获取原始画布宽度（不包含水平拓展）
            original_canvas_width_cm = self.canvas_width_m * 100  # 转换米为厘米

            # 为每个图片添加唯一标识符，格式为"图片名_宽_高_索引"
            # 使用索引号代替行号，确保每个图片都有唯一的标识符
            self.log_signal.emit(f"开始为 {len(self.pattern_items)} 个图片添加唯一标识符...")

            for index, pattern in enumerate(self.pattern_items):
                width_cm = pattern['width_cm']
                height_cm = pattern['height_cm']
                pattern_name = pattern.get('pattern_name', '')

                # 创建基础唯一标识符，格式为"图片名_宽_高"
                base_unique_id = f"{pattern_name}_{width_cm:.1f}_{height_cm:.1f}"
                pattern['base_unique_id'] = base_unique_id

                # 使用索引号作为初始唯一标识符的一部分
                # 这样即使在初始状态下，每个图片也有唯一的标识符
                pattern['index'] = index

                # 添加行号信息，用于跟踪图片在不同画布上的位置
                # 初始行号为-1，表示尚未放置，后续会在放置图片时更新为实际行号
                pattern['row_number'] = -1

                # 完整唯一标识符，包含索引号
                pattern['unique_id'] = f"{base_unique_id}_{index}"

            # 一次循环完成A/B/C类图片分类
            self.log_signal.emit("开始一次循环完成A/B/C类图片分类...")
            self.log_signal.emit(f"图片总数: {len(self.pattern_items)}")
            self.log_signal.emit("图片分类流程: 先A类，再B类，最后C类")
            image_classifier = ImageClassifier(log_signal=self.log_signal)

            # 1. 分类图片
            self.log_signal.emit("第一步: 分类图片为A/B/C类...")
            class_a_patterns, class_b_patterns, class_c_patterns = image_classifier.classify_images(
                pattern_items=self.pattern_items,
                canvas_width_cm=canvas_width_cm,
                class_a_threshold=class_a_threshold,
                class_b_error_range=class_b_error_range,  # 严格执行5%误差范围
                original_canvas_width_cm=original_canvas_width_cm
            )

            # 2. 在每个类别内部按面积降序排序
            self.log_signal.emit("第二步: 对A类图片按面积降序排序...")
            class_a_patterns.sort(key=lambda p: -(p['width_cm'] * p['height_cm']))

            # 3. 处理B类图片分组 - 返回二维数组结构
            self.log_signal.emit("第三步: 处理B类图片分组，组织为二维数组结构...")
            b_class_groups, downgraded_c_patterns = image_classifier.process_b_class_groups(
                class_b_patterns=class_b_patterns,
                canvas_width_cm=canvas_width_cm,
                original_canvas_width_cm=original_canvas_width_cm
            )

            # 计算B类图片总数
            total_b_images = sum(len(group) for group in b_class_groups)
            self.log_signal.emit(f"B类图片分组完成: {len(b_class_groups)} 个组，共 {total_b_images} 个图片")

            # 4. 将降级的B类图片添加到C类图片列表，但保持标记为原始B类
            self.log_signal.emit(f"第四步: 将{len(downgraded_c_patterns)}个降级的B类图片添加到C类图片列表...")
            for pattern in downgraded_c_patterns:
                pattern['original_class'] = 'B'  # 记录原始类别
                pattern['image_class'] = 'C'     # 设置为C类

            class_c_patterns.extend(downgraded_c_patterns)

            # 5. 对C类图片进行排序
            self.log_signal.emit("第五步: 对C类图片进行排序...")
            class_c_patterns = image_classifier.sort_c_class_patterns(
                class_c_patterns=class_c_patterns,
                canvas_width_cm=canvas_width_cm
            )

            # 6. 将分类结果保存到Excel文件
            self.log_signal.emit("第六步: 将分类结果保存到Excel文件...")

            # 创建Excel管理器
            from core.excel_manager import ExcelManager
            excel_manager = ExcelManager(log_signal=self.log_signal)

            # 构建输出文件路径，使用宽幅值作为文件名后缀
            # 获取原始画布宽度（不包含水平拓展）
            canvas_width_int = int(original_canvas_width_cm)
            excel_output_path = os.path.join(
                os.path.dirname(self.output_path),
                f"{self.material_name}_分类结果_{canvas_width_int}.xlsx"
            )

            # 保存分类结果到Excel文件
            success, message = excel_manager.save_classification_to_excel(
                class_a_patterns=class_a_patterns,
                class_b_patterns=class_b_patterns,
                class_c_patterns=class_c_patterns,
                output_path=excel_output_path
            )

            if success:
                self.log_signal.emit(f"分类结果已保存到Excel文件: {excel_output_path}")
            else:
                self.log_signal.emit(f"保存分类结果失败: {message}")

            # 7. 从Excel文件读取分类结果
            self.log_signal.emit("第七步: 从Excel文件读取分类结果...")

            # 读取分类结果
            success, message, loaded_a_patterns, loaded_b_patterns, loaded_c_patterns = excel_manager.load_classification_from_excel(
                excel_path=excel_output_path
            )

            if success:
                self.log_signal.emit(f"从Excel文件读取分类结果成功: {message}")

                # 保存原始分类结果的副本，用于排列后的比较
                original_a_patterns = class_a_patterns.copy()
                original_b_patterns = class_b_patterns.copy()
                original_c_patterns = class_c_patterns.copy()

                # 使用读取的分类结果
                class_a_patterns = loaded_a_patterns
                class_b_patterns = loaded_b_patterns
                class_c_patterns = loaded_c_patterns

                # 记录读取前后的数据量
                self.log_signal.emit(f"原始分类结果: A类={len(original_a_patterns)}个, B类={len(original_b_patterns)}个, C类={len(original_c_patterns)}个")
                self.log_signal.emit(f"读取的分类结果: A类={len(class_a_patterns)}个, B类={len(class_b_patterns)}个, C类={len(class_c_patterns)}个")

                # 检查是否有数据丢失
                if len(class_a_patterns) < len(original_a_patterns) or len(class_b_patterns) < len(original_b_patterns) or len(class_c_patterns) < len(original_c_patterns):
                    self.log_signal.emit("警告: 从Excel读取的分类结果数据量少于原始分类结果，将使用原始分类结果")
                    class_a_patterns = original_a_patterns
                    class_b_patterns = original_b_patterns
                    class_c_patterns = original_c_patterns

                # 不再重新处理B类图片分组，完全保持原始分类
                # 不再对C类图片进行排序，完全保持原始分类

                # 记录分类结果
                self.log_signal.emit(f"保持原始分类结果: A类={len(class_a_patterns)}个, B类={len(class_b_patterns)}个, C类={len(class_c_patterns)}个")

                # 为了确保分类数据的完整性，创建所有分类数据的深拷贝
                class_a_patterns = [pattern.copy() for pattern in class_a_patterns]
                class_b_patterns = [pattern.copy() for pattern in class_b_patterns]
                class_c_patterns = [pattern.copy() for pattern in class_c_patterns]
            else:
                self.log_signal.emit(f"从Excel文件读取分类结果失败: {message}，将使用内存中的分类结果")

            # 使用图片排列器进行排列
            self.log_signal.emit("开始使用图片排列器进行排列...")

            # 为每个图片添加路径信息
            for pattern in class_a_patterns + class_b_patterns + class_c_patterns:
                if 'path' not in pattern:
                    pattern['path'] = pattern.get('image_path', '')

            # 清空未排列图片列表，重置排列数据
            self.log_signal.emit("清空未排列图片列表，重置排列数据...")

            # 记录总图片数量
            total_patterns = len(class_a_patterns) + len(class_b_patterns) + len(class_c_patterns)
            self.log_signal.emit(f"总图片数量: {total_patterns}个 (A类: {len(class_a_patterns)}个, B类: {len(class_b_patterns)}个, C类: {len(class_c_patterns)}个)")

            # 从分类表格中依次读取A、B、C类的图片数据
            self.log_signal.emit("从分类表格中依次读取A、B、C类的图片数据...")

            # 创建排列用的图片列表，按照A->B->C的顺序
            # 同时保存原始分类数据的副本，用于后续画布
            # 这是解决B类和C类数据丢失问题的关键
            self.original_class_a_patterns = [pattern.copy() for pattern in class_a_patterns]
            self.original_class_b_patterns = [pattern.copy() for pattern in class_b_patterns]
            self.original_class_c_patterns = [pattern.copy() for pattern in class_c_patterns]
            self.log_signal.emit(f"已保存原始分类数据副本: A类={len(self.original_class_a_patterns)}个, B类={len(self.original_class_b_patterns)}个, C类={len(self.original_class_c_patterns)}个")

            a_patterns_to_arrange = [pattern.copy() for pattern in class_a_patterns]
            b_patterns_to_arrange = [pattern.copy() for pattern in class_b_patterns]
            c_patterns_to_arrange = [pattern.copy() for pattern in class_c_patterns]

            # 检查是否使用RectPack算法
            if use_rectpack:
                # 使用RectPack统一排列算法
                self.log_signal.emit("使用RectPack统一排列算法，不再区分ABC类...")

                # 合并所有图片，不再区分类别
                all_patterns = []
                all_patterns.extend(a_patterns_to_arrange)
                all_patterns.extend(b_patterns_to_arrange)
                all_patterns.extend(c_patterns_to_arrange)

                # 使用统一排列器
                from core.unified_image_arranger import UnifiedImageArranger
                unified_arranger = UnifiedImageArranger(log_signal=self.log_signal)
                unified_arranger.initialize(
                    canvas_width_px=canvas_width_px,
                    max_height_px=max_height_px,
                    image_spacing_px=spacing_px,
                    ppi=self.ppi
                )

                arranged_images = unified_arranger.arrange_images(all_patterns)

                # 获取布局统计信息
                stats = unified_arranger.get_layout_statistics()
                if stats:
                    self.log_signal.emit(f"RectPack排列完成: 利用率={stats.get('utilization_percent', 0):.2f}%")
            else:
                # 使用传统的ABC分类排列算法
                self.log_signal.emit("使用传统图片排列器进行排列，遵循ABC各自的排列规则和算法逻辑...")
                arranged_images = self.image_arranger.arrange_images(
                    class_a_patterns=a_patterns_to_arrange,
                    class_b_patterns=b_patterns_to_arrange,
                    class_c_patterns=c_patterns_to_arrange
                )

            # 获取排列结果
            images_info = arranged_images

            # 检查是否有图片未能排列
            arranged_count = len(arranged_images)
            total_count = len(class_a_patterns) + len(class_b_patterns) + len(class_c_patterns)

            self.log_signal.emit(f"已排列图片数量: {arranged_count}/{total_count}")

            # 如果有图片未能排列，需要创建新的画布继续排列
            if arranged_count < total_count:
                self.log_signal.emit(f"有 {total_count - arranged_count} 个图片未能排列，需要创建新的画布继续排列")

                # 这些图片将在下一个画布中排列
                # 在 run 方法中，我们会检测到 remaining_patterns 不为空，然后创建新的画布

                # 记录未排列图片的数量
                self.log_signal.emit(f"未排列图片数量: A类={len([p for p in class_a_patterns if p.get('unique_id', '') not in [img.get('unique_id', '') for img in arranged_images]])}个, " +
                                   f"B类={len([p for p in class_b_patterns if p.get('unique_id', '') not in [img.get('unique_id', '') for img in arranged_images]])}个, " +
                                   f"C类={len([p for p in class_c_patterns if p.get('unique_id', '') not in [img.get('unique_id', '') for img in arranged_images]])}个")

            # 清空之前的剩余图片列表，避免重复添加
            remaining_patterns = []

            if arranged_count < total_count:
                self.log_signal.emit(f"警告: 有 {total_count - arranged_count} 个图片未能排列，可能是因为画布已满")
                canvas_is_full = True

                # 收集未排列的图片 - 使用更精确的方法
                # 从arranged_images中提取唯一标识符
                arranged_ids = set()
                for img in arranged_images:
                    unique_id = img.get('unique_id', '')
                    if unique_id:
                        arranged_ids.add(unique_id)

                # 创建所有图片的集合
                all_patterns = []
                all_patterns.extend(class_a_patterns)
                all_patterns.extend(class_b_patterns)
                all_patterns.extend(class_c_patterns)

                # 确保所有图片都有唯一标识符
                for i, pattern in enumerate(all_patterns):
                    if 'unique_id' not in pattern or not pattern['unique_id']:
                        # 生成唯一标识符
                        width_cm = pattern.get('width_cm', 0)
                        height_cm = pattern.get('height_cm', 0)
                        pattern_name = pattern.get('pattern_name', f'图片{i+1}')
                        index = pattern.get('index', i)
                        row_number = pattern.get('row_number', -1)
                        pattern['unique_id'] = f"{pattern_name}_{width_cm:.1f}_{height_cm:.1f}_{index}_{row_number}"
                        self.log_signal.emit(f"为图片添加唯一标识符: {pattern['unique_id']}")

                # 按照A->B->C的顺序收集未排列的图片
                # 创建已收集图片的唯一标识符集合
                collected_ids = set()

                # 获取已排列图片的唯一标识符
                arranged_ids = set(img.get('unique_id', '') for img in arranged_images if img.get('unique_id', ''))

                # 这里不需要再次保存原始分类数据的副本，因为我们已经在排列前保存了
                # 检查是否已经保存了原始分类数据
                if not hasattr(self, 'original_class_a_patterns') or not self.original_class_a_patterns:
                    self.original_class_a_patterns = [p.copy() for p in class_a_patterns]
                    self.original_class_b_patterns = [p.copy() for p in class_b_patterns]
                    self.original_class_c_patterns = [p.copy() for p in class_c_patterns]
                    self.log_signal.emit(f"已保存原始分类数据副本: A类={len(self.original_class_a_patterns)}个, B类={len(self.original_class_b_patterns)}个, C类={len(self.original_class_c_patterns)}个")

                # 首先收集A类图片
                for pattern in class_a_patterns:
                    unique_id = pattern.get('unique_id', '')
                    if unique_id and unique_id not in arranged_ids and unique_id not in collected_ids:
                        pattern_copy = pattern.copy()
                        pattern_copy['image_class'] = 'A'  # 确保保留分类信息
                        remaining_patterns.append(pattern_copy)
                        collected_ids.add(unique_id)
                        self.log_signal.emit(f"收集未排列的A类图片: {pattern.get('pattern_name', '')} (ID: {unique_id})")

                # 然后收集B类图片
                for pattern in class_b_patterns:
                    unique_id = pattern.get('unique_id', '')
                    if unique_id and unique_id not in arranged_ids and unique_id not in collected_ids:
                        pattern_copy = pattern.copy()
                        pattern_copy['image_class'] = 'B'  # 确保保留分类信息
                        remaining_patterns.append(pattern_copy)
                        collected_ids.add(unique_id)
                        self.log_signal.emit(f"收集未排列的B类图片: {pattern.get('pattern_name', '')} (ID: {unique_id})")

                # 最后收集C类图片
                for pattern in class_c_patterns:
                    unique_id = pattern.get('unique_id', '')
                    if unique_id and unique_id not in arranged_ids and unique_id not in collected_ids:
                        pattern_copy = pattern.copy()
                        pattern_copy['image_class'] = 'C'  # 确保保留分类信息
                        remaining_patterns.append(pattern_copy)
                        collected_ids.add(unique_id)
                        self.log_signal.emit(f"收集未排列的C类图片: {pattern.get('pattern_name', '')} (ID: {unique_id})")

                # 记录详细信息
                self.log_signal.emit(f"收集到 {len(remaining_patterns)} 个未排列的图片，将在新画布继续排列")
                self.log_signal.emit(f"详细信息: 总图片数={total_count}, 已排列={arranged_count}, 未排列={total_count - arranged_count}, 收集到={len(remaining_patterns)}")

                # 如果数量不一致，记录警告并尝试修复
                if len(remaining_patterns) != (total_count - arranged_count):
                    self.log_signal.emit(f"警告: 未排列图片数量 ({total_count - arranged_count}) 与收集到的图片数量 ({len(remaining_patterns)}) 不一致")

                    # 尝试查找原因
                    arranged_patterns_count = {}
                    for img in arranged_images:
                        pattern_name = img.get('name', '')
                        unique_id = img.get('unique_id', '')
                        key = unique_id if unique_id else pattern_name
                        if key in arranged_patterns_count:
                            arranged_patterns_count[key] += 1
                        else:
                            arranged_patterns_count[key] = 1

                    # 检查是否有重复排列的图片
                    duplicate_arranged = []
                    for key, count in arranged_patterns_count.items():
                        if count > 1:
                            duplicate_arranged.append(f"{key}({count}次)")

                    if duplicate_arranged:
                        self.log_signal.emit(f"发现重复排列的图片: {', '.join(duplicate_arranged[:10])}" +
                                           (f" 等{len(duplicate_arranged)}个" if len(duplicate_arranged) > 10 else ""))

                    # 尝试修复 - 确保剩余图片数量正确
                    if len(remaining_patterns) < (total_count - arranged_count):
                        self.log_signal.emit("尝试修复: 收集到的未排列图片数量少于预期，尝试找出缺失的图片...")

                        # 创建已排列和已收集的图片ID集合
                        # 使用更宽松的匹配方式，只比较基础部分（不包含索引和行号）
                        arranged_ids_full = set(img.get('unique_id', '') for img in arranged_images if img.get('unique_id', ''))
                        collected_ids_full = set(pattern.get('unique_id', '') for pattern in remaining_patterns if pattern.get('unique_id', ''))

                        # 创建基础ID集合（不包含索引和行号）
                        arranged_ids_base = set()
                        for img in arranged_images:
                            unique_id = img.get('unique_id', '')
                            if unique_id:
                                parts = unique_id.split('_')
                                if len(parts) >= 3:
                                    base_id = '_'.join(parts[:-2])  # 去掉最后两部分（索引和行号）
                                    arranged_ids_base.add(base_id)

                        collected_ids_base = set()
                        for pattern in remaining_patterns:
                            unique_id = pattern.get('unique_id', '')
                            if unique_id:
                                parts = unique_id.split('_')
                                if len(parts) >= 3:
                                    base_id = '_'.join(parts[:-2])  # 去掉最后两部分（索引和行号）
                                    collected_ids_base.add(base_id)

                        # 使用完整ID和基础ID的组合
                        arranged_ids = arranged_ids_full | arranged_ids_base
                        collected_ids = collected_ids_full | collected_ids_base

                        # 创建所有图片ID集合
                        all_ids_full = set()
                        all_ids_base = set()
                        for pattern in all_patterns:
                            unique_id = pattern.get('unique_id', '')
                            if unique_id:
                                all_ids_full.add(unique_id)
                                # 添加基础ID
                                parts = unique_id.split('_')
                                if len(parts) >= 3:
                                    base_id = '_'.join(parts[:-2])  # 去掉最后两部分（索引和行号）
                                    all_ids_base.add(base_id)

                        # 合并完整ID和基础ID
                        all_ids = all_ids_full | all_ids_base

                        # 找出缺失的图片ID
                        missing_ids = all_ids - arranged_ids - collected_ids

                        if missing_ids:
                            self.log_signal.emit(f"找到 {len(missing_ids)} 个缺失的图片ID")

                            # 按照A->B->C的顺序添加缺失的图片
                            # 首先添加A类图片
                            for pattern in class_a_patterns:
                                unique_id = pattern.get('unique_id', '')
                                if unique_id in missing_ids:
                                    pattern_copy = pattern.copy()
                                    remaining_patterns.append(pattern_copy)
                                    self.log_signal.emit(f"添加缺失的A类图片: {pattern.get('pattern_name', '')} (ID: {unique_id})")

                            # 然后添加B类图片
                            for pattern in class_b_patterns:
                                unique_id = pattern.get('unique_id', '')
                                if unique_id in missing_ids:
                                    pattern_copy = pattern.copy()
                                    remaining_patterns.append(pattern_copy)
                                    self.log_signal.emit(f"添加缺失的B类图片: {pattern.get('pattern_name', '')} (ID: {unique_id})")

                            # 最后添加C类图片
                            for pattern in class_c_patterns:
                                unique_id = pattern.get('unique_id', '')
                                if unique_id in missing_ids:
                                    pattern_copy = pattern.copy()
                                    remaining_patterns.append(pattern_copy)
                                    self.log_signal.emit(f"添加缺失的C类图片: {pattern.get('pattern_name', '')} (ID: {unique_id})")

                            self.log_signal.emit(f"修复后的剩余图片数量: {len(remaining_patterns)}")

                    elif len(remaining_patterns) > (total_count - arranged_count):
                        self.log_signal.emit("尝试修复: 收集到的未排列图片数量多于预期，尝试移除重复的图片...")

                        # 创建唯一ID集合，用于检测重复
                        unique_ids = set()
                        deduplicated_patterns = []

                        for pattern in remaining_patterns:
                            unique_id = pattern.get('unique_id', '')
                            if unique_id:
                                if unique_id not in unique_ids:
                                    unique_ids.add(unique_id)
                                    deduplicated_patterns.append(pattern)
                                else:
                                    self.log_signal.emit(f"移除重复的图片: {pattern.get('pattern_name', '')} (ID: {unique_id})")
                            else:
                                # 如果没有唯一ID，仍然保留该图片
                                deduplicated_patterns.append(pattern)

                        # 更新剩余图片列表
                        remaining_patterns = deduplicated_patterns
                        self.log_signal.emit(f"去重后的剩余图片数量: {len(remaining_patterns)}")

                    # 最终检查
                    if len(remaining_patterns) == (total_count - arranged_count):
                        self.log_signal.emit("修复成功: 剩余图片数量现在与预期一致")
                    else:
                        self.log_signal.emit(f"修复后仍有不一致: 预期 {total_count - arranged_count} 个剩余图片，实际收集到 {len(remaining_patterns)} 个")
            else:
                self.log_signal.emit(f"所有图片都已成功排列: {arranged_count}/{total_count}")

            # 使用图片排列器已经完成了图片排列，不需要再手动排列
            self.log_signal.emit("图片排列已完成，准备创建画布...")

            # 检查是否取消任务
            if self.is_canceled:
                self.log_signal.emit("布局任务已取消")
                self.success = False
                self.finished.emit()
                return

            # 获取画布高度
            canvas_height = self.image_arranger.get_canvas_height()
            self.log_signal.emit(f"画布高度: {canvas_height}像素 ({canvas_height/self.ppi*2.54:.2f}厘米)")

            # 检查是否是最后一个画布
            if not remaining_patterns:
                # 如果没有剩余图片，则当前画布是最后一个画布
                self.log_signal.emit("当前画布是最后一个画布，将在处理完所有图片后截断画布")
                # 注意：已移除tetris_packer相关代码，现在使用RectPack算法

            # 准备创建画布
            self.log_signal.emit("准备创建画布...")

            # 创建画布
            self.log_signal.emit(f"创建画布: {self.canvas_name}_{self.canvas_sequence}...")

            # 使用图片处理器创建画布
            canvas_created = self.image_processor.create_canvas(
                width=canvas_width_px,
                height=canvas_height,
                name=f"{self.canvas_name}_{self.canvas_sequence}",
                ppi=self.ppi
            )

            if not canvas_created:
                self.log_signal.emit("创建画布失败")
                self.success = False
                self.finished.emit()
                return

            # 放置图片
            self.log_signal.emit(f"开始放置 {len(images_info)} 个图片...")

            # 添加调试信息，帮助排查问题
            self.log_signal.emit(f"画布尺寸: {canvas_width_px}x{canvas_height}像素")

            # 检查图片位置范围
            min_x = min([img.get('x', 0) for img in images_info]) if images_info else 0
            max_x = max([img.get('x', 0) + img.get('width', 0) for img in images_info]) if images_info else 0
            min_y = min([img.get('y', 0) for img in images_info]) if images_info else 0
            max_y = max([img.get('y', 0) + img.get('height', 0) for img in images_info]) if images_info else 0

            self.log_signal.emit(f"图片位置范围: X({min_x}-{max_x}), Y({min_y}-{max_y})")

            # 检查是否有图片超出画布范围
            if max_x > canvas_width_px or max_y > canvas_height:
                self.log_signal.emit(f"警告: 有图片超出画布范围! 画布尺寸: {canvas_width_px}x{canvas_height}像素, 图片范围: {max_x}x{max_y}像素")

            # 按照A->B->C的顺序组织图片
            a_class_images = [img for img in images_info if img.get('image_class') == 'A']
            b_class_images = [img for img in images_info if img.get('image_class') == 'B']
            c_class_images = [img for img in images_info if img.get('image_class') == 'C']

            self.log_signal.emit(f"图片分类: A类={len(a_class_images)}个, B类={len(b_class_images)}个, C类={len(c_class_images)}个")

            # 放置所有图片
            total_images = len(images_info)
            images_placed = 0

            # 遍历所有图片并放置
            for i, image_info in enumerate(images_info):
                # 检查是否取消任务
                if self.is_canceled:
                    self.log_signal.emit("布局任务已取消")
                    self.success = False
                    self.finished.emit()
                    return

                # 准备图片信息
                image_path = image_info.get('path', '')
                x = image_info.get('x', 0)
                y = image_info.get('y', 0)
                width = image_info.get('width', 0)
                height = image_info.get('height', 0)
                need_rotation = image_info.get('need_rotation', False)
                image_name = image_info.get('name', os.path.basename(image_path))
                image_class = image_info.get('image_class', 'C')

                # 创建完整的图片信息字典
                place_info = {
                    'image_path': image_path,
                    'x': x,
                    'y': y,
                    'width': width,
                    'height': height,
                    'rotated': need_rotation,
                    'name': image_name,
                    'image_class': image_class
                }

                # 使用图片处理器放置图片
                success = self.image_processor.place_image(place_info)

                if not success:
                    self.log_signal.emit(f"警告: 放置图片 {image_name} 失败")

                # 更新进度
                images_placed += 1
                if i % 10 == 0 or i == len(images_info) - 1:  # 每10个图片更新一次进度，减少UI更新频率
                    progress = int((images_placed / total_images) * 100)
                    self.progress_signal.emit(progress)

            # 保存画布
            self.log_signal.emit("保存画布...")

            # 使用图片处理器保存画布
            save_success = self.image_processor.save_canvas(self.output_path)

            if not save_success:
                self.log_signal.emit("保存画布失败")
                self.success = False
                self.finished.emit()
                return

            # 生成画布说明文档
            self.log_signal.emit("生成画布说明文档...")

            # 准备画布信息
            canvas_info = {
                'canvas_name': f"{self.canvas_name}_{self.canvas_sequence}",
                'material_name': self.material_name,
                'canvas_sequence': self.canvas_sequence,
                'canvas_width_m': self.canvas_width_m,
                'canvas_width_px': canvas_width_px,
                'canvas_height': canvas_height,
                'horizontal_expansion_cm': self.horizontal_expansion_cm,
                'max_height_cm': self.max_height_cm,
                'ppi': self.ppi,
                'generation_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

            # 使用图片处理器生成说明文档
            desc_success = self.image_processor.generate_description(
                output_path=self.output_path,
                images_info=images_info,
                canvas_info=canvas_info
            )

            if not desc_success:
                self.log_signal.emit("警告: 生成画布说明文档失败")

            # 关闭画布
            self.log_signal.emit("关闭画布...")
            self.image_processor.close_canvas()

            # 清理资源
            self.image_processor.cleanup()

            # 检查是否有剩余图片
            if remaining_patterns:
                self.log_signal.emit(f"有 {len(remaining_patterns)} 个图片未能放置，需要创建新画布")

                # 发送新画布信号
                self.new_canvas_needed.emit(self.material_name, remaining_patterns)
            else:
                self.log_signal.emit("所有图片已成功放置")



            # 完成任务
            self.success = True
            self.log_signal.emit("布局任务完成")
            self.finished.emit()
        except Exception as e:
            # 记录错误
            self.error_count += 1
            error_message = f"布局任务出错: {str(e)}"
            self.log_signal.emit(error_message)
            self.log_signal.emit(traceback.format_exc())

            # 发送错误信号
            self.error_signal.emit("布局错误", error_message)

            # 设置任务失败
            self.success = False
            self.finished.emit()

    def process_b_class_images(self, images_info, processed_patterns, remaining_patterns):
        """处理B类图片，使用严格按组排列的算法

        Args:
            images_info: 图片信息列表
            processed_patterns: 已处理的图案列表
            remaining_patterns: 剩余未处理图案列表

        Returns:
            tuple: (images_info, processed_patterns, remaining_patterns)
        """
        # 使用图片排列器处理B类图片，不需要手动处理
        return images_info, processed_patterns, remaining_patterns

    def preprocess_c_class_patterns(self, class_c_patterns):
        """预处理C类图片，对相似尺寸的图片进行分组并做出统一的旋转决策

        Args:
            class_c_patterns: C类图片列表

        Returns:
            list: 预处理后的C类图片列表
        """
        # 使用图片排列器处理C类图片，不需要手动处理
        return class_c_patterns

    def retry_remaining_patterns(self, remaining_patterns):
        """重新尝试放置剩余图片

        Args:
            remaining_patterns: 剩余图片列表
        """
        self.log_signal.emit(f"重新尝试放置 {len(remaining_patterns)} 个剩余图片...")

        # 确保每个图片都有分类信息和唯一标识符
        for i, pattern in enumerate(remaining_patterns):
            if 'unique_id' not in pattern:
                width_cm = pattern.get('width_cm', 0)
                height_cm = pattern.get('height_cm', 0)
                pattern_name = pattern.get('pattern_name', f'图片{i+1}')
                pattern['unique_id'] = f"{pattern_name}_{width_cm:.1f}_{height_cm:.1f}_{i}"
                self.log_signal.emit(f"为剩余图片添加唯一标识符: {pattern['unique_id']}")

            # 确保有分类信息
            if 'image_class' not in pattern:
                # 如果有保存的原始分类数据，则使用它来推断分类
                if hasattr(self, 'original_class_a_patterns') and hasattr(self, 'original_class_b_patterns') and hasattr(self, 'original_class_c_patterns'):
                    # 获取基础ID（不包含索引和行号）
                    unique_id = pattern.get('unique_id', '')
                    unique_id_base = ''

                    if unique_id:
                        parts = unique_id.split('_')
                        if len(parts) >= 3:
                            unique_id_base = '_'.join(parts[:-2])  # 去掉最后两部分（索引和行号）

                    # 如果无法提取基础ID，使用完整ID
                    if not unique_id_base:
                        unique_id_base = unique_id

                    # 检查是否在A类中（使用更宽松的匹配）
                    is_a_class = False
                    for p in self.original_class_a_patterns:
                        p_unique_id = p.get('unique_id', '')
                        if p_unique_id:
                            p_parts = p_unique_id.split('_')
                            if len(p_parts) >= 3:
                                p_base_id = '_'.join(p_parts[:-2])
                                if p_base_id == unique_id_base or p_unique_id == unique_id:
                                    is_a_class = True
                                    break

                    # 检查是否在B类中（使用更宽松的匹配）
                    is_b_class = False
                    if not is_a_class:
                        for p in self.original_class_b_patterns:
                            p_unique_id = p.get('unique_id', '')
                            if p_unique_id:
                                p_parts = p_unique_id.split('_')
                                if len(p_parts) >= 3:
                                    p_base_id = '_'.join(p_parts[:-2])
                                    if p_base_id == unique_id_base or p_unique_id == unique_id:
                                        is_b_class = True
                                        break

                    # 根据匹配结果设置分类
                    if is_a_class:
                        pattern['image_class'] = 'A'
                        self.log_signal.emit(f"根据原始分类数据将图片 {pattern.get('pattern_name', '')} 分类为A类")
                    elif is_b_class:
                        pattern['image_class'] = 'B'
                        self.log_signal.emit(f"根据原始分类数据将图片 {pattern.get('pattern_name', '')} 分类为B类")
                    else:
                        pattern['image_class'] = 'C'
                        self.log_signal.emit(f"根据原始分类数据将图片 {pattern.get('pattern_name', '')} 分类为C类")
                else:
                    # 如果没有原始分类数据，则根据尺寸进行简单分类
                    width_cm = pattern.get('width_cm', 0)
                    canvas_width_cm = self.canvas_width_cm_with_expansion

                    # 简单分类逻辑
                    if abs(width_cm - canvas_width_cm) / canvas_width_cm <= 0.05:  # 5%误差范围内
                        pattern['image_class'] = 'A'
                    else:
                        pattern['image_class'] = 'C'  # 默认为C类

                    self.log_signal.emit(f"根据尺寸将图片 {pattern.get('pattern_name', '')} 分类为{pattern['image_class']}类")

        # 按照A->B->C的顺序对剩余图片进行分类
        a_class_patterns = [p for p in remaining_patterns if p.get('image_class') == 'A']
        b_class_patterns = [p for p in remaining_patterns if p.get('image_class') == 'B']
        c_class_patterns = [p for p in remaining_patterns if p.get('image_class') == 'C']

        self.log_signal.emit(f"剩余图片分类: A类={len(a_class_patterns)}个, B类={len(b_class_patterns)}个, C类={len(c_class_patterns)}个")

        # 如果设置了强制放入标志，尝试将剩余图片放入当前画布
        if hasattr(self, 'force_fit_remaining') and self.force_fit_remaining:
            self.log_signal.emit("尝试强制将剩余图片放入当前画布...")

            # 使用图片排列器进行排列
            if hasattr(self, 'image_arranger'):
                # 创建排列用的图片列表，按照A->B->C的顺序
                a_patterns_to_arrange = [pattern.copy() for pattern in a_class_patterns]
                b_patterns_to_arrange = [pattern.copy() for pattern in b_class_patterns]
                c_patterns_to_arrange = [pattern.copy() for pattern in c_class_patterns]

                # 使用图片排列器进行排列
                self.log_signal.emit("使用图片排列器强制放置剩余图片...")
                arranged_images = self.image_arranger.arrange_images(
                    class_a_patterns=a_patterns_to_arrange,
                    class_b_patterns=b_patterns_to_arrange,
                    class_c_patterns=c_patterns_to_arrange
                )

                # 检查是否成功放置了所有图片
                if len(arranged_images) == len(remaining_patterns):
                    self.log_signal.emit(f"成功强制放置所有 {len(remaining_patterns)} 个剩余图片")
                    return True
                else:
                    self.log_signal.emit(f"强制放置部分成功: 放置了 {len(arranged_images)}/{len(remaining_patterns)} 个剩余图片")
                    return False
            else:
                self.log_signal.emit("图片排列器未初始化，无法强制放置剩余图片")
                return False
        else:
            self.log_signal.emit("未设置强制放入标志，不尝试强制放置剩余图片")
            return False

    def _generate_canvas_description(self, output_path, images_info, canvas_width_px, canvas_height,
                                   class_a_count, class_b_count, class_c_count):
        """生成画布说明文档

        Args:
            output_path: 画布输出路径
            images_info: 图片信息列表
            canvas_width_px: 画布宽度（像素）
            canvas_height: 画布高度（像素）
            class_a_count: A类图片数量
            class_b_count: B类图片数量
            class_c_count: C类图片数量
        """
        try:
            # 生成说明文档路径
            doc_path = os.path.splitext(output_path)[0] + "_说明.txt"

            # 提取材质名称和画布序号
            material_name = self.material_name
            canvas_sequence = self.canvas_sequence

            # 计算画布尺寸（厘米和米）
            canvas_width_cm = canvas_width_px / self.ppi * 2.54
            canvas_height_cm = canvas_height / self.ppi * 2.54
            canvas_width_m = canvas_width_cm / 100

            # 获取水平拓展值（如果有）
            horizontal_expand_cm = getattr(self, 'horizontal_expansion_cm', 0)

            # 获取最大高度限制
            max_height_cm = getattr(self, 'max_height_cm', 5000.0)

            # 获取测试模式缩小比率
            scale_factor = getattr(self, 'miniature_ratio', 0.02)

            # 计算画布面积（像素）
            canvas_area_px = canvas_width_px * canvas_height

            # 计算画布面积（厘米）
            canvas_area_cm = canvas_width_cm * canvas_height_cm  # 使用前面计算的厘米尺寸

            # 计算图片总面积和占有率
            total_image_area_px = 0
            for img in images_info:
                width = img.get('width', 0)
                height = img.get('height', 0)
                total_image_area_px += width * height

            # 计算占有率
            utilization_ratio = (total_image_area_px / canvas_area_px) * 100 if canvas_area_px > 0 else 0

            # 计算旋转图片比例
            rotated_images = sum(1 for img in images_info if img.get('need_rotation', False))
            rotation_ratio = (rotated_images / len(images_info)) * 100 if images_info else 0

            # 计算各类图片的数量和占比
            total_images = len(images_info)
            class_a_percent = (class_a_count / total_images) * 100 if total_images > 0 else 0
            class_b_percent = (class_b_count / total_images) * 100 if total_images > 0 else 0
            class_c_percent = (class_c_count / total_images) * 100 if total_images > 0 else 0

            # 生成文档标题（使用材质名称和画布序号）
            # 根据实际运行环境设置标题
            if self.use_photoshop:
                title = f"{material_name}-{canvas_sequence} 正式环境说明文档"
            else:
                title = f"{material_name}-{canvas_sequence} 测试模式说明文档"

            # 生成说明文档内容
            content = [
                f"# {title}",
                f"",
                f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                f"材质名称: {material_name}",
                f"画布序号: {canvas_sequence}",
                f"画布宽度: {canvas_width_m:.2f}米 ({canvas_width_cm:.0f}厘米)",
                f"画布高度: {canvas_height_cm:.2f} 厘米 ({canvas_height} 像素)",
                f"水平拓展: {horizontal_expand_cm} 厘米",
                f"最大高度限制: {max_height_cm} 厘米",
                f"缩小模型比率: {scale_factor}",
                f"测试全部数据: {'是' if getattr(self, 'is_test_all_data', False) else '否'}",
                f"",
                f"## 利用率统计",
                f"",
                f"画布利用率: {utilization_ratio:.2f}%",
                f"旋转图片比例: {rotation_ratio:.2f}% ({rotated_images}/{total_images})",
                f"",
                f"## 图片统计",
                f"",
                f"总图片数: {total_images}",
                f"A类图片(宽幅类): {class_a_count} ({class_a_percent:.2f}%)",
                f"B类图片(宽幅约束类): {class_b_count} ({class_b_percent:.2f}%)",
                f"C类图片(俄罗斯方块类): {class_c_count} ({class_c_percent:.2f}%)",
                f"",
                f"## 图片排列信息",
                f"",
                f"序号   名称                            分类   位置(x,y)        尺寸(宽x高)        表格宽-高       旋转   ",
                f"------------------------------------------------------------------------------------------"
            ]

            # 添加图片详情（表格形式）
            for i, img in enumerate(images_info):
                image_class = img.get('image_class', 'C')
                name = img.get('name', f'图片{i+1}')
                x = img.get('x', 0)
                y = img.get('y', 0)
                width = img.get('width', 0)
                height = img.get('height', 0)
                need_rotation = img.get('need_rotation', False)

                # 获取表格中的宽高数据（如果有）
                table_width_cm = img.get('table_width_cm', 0)
                table_height_cm = img.get('table_height_cm', 0)

                # 格式化输出行
                name_padded = name.ljust(30)[:30]  # 限制名称长度并左对齐
                position = f"({x},{y})"
                size = f"({width}x{height})"
                table_size = f"{table_width_cm:.0f}-{table_height_cm:.0f}"
                rotation = "是" if need_rotation else "否"

                # 添加到内容中
                content.append(f"{i+1:<5} {name_padded} {image_class}    {position:<15} {size:<15} {table_size:<10} {rotation:<5}")

            # 添加色块说明
            content.extend([
                f"",
                f"## 色块说明",
                f"",
                f"- 红色: A类图片（宽幅类）",
                f"- 绿色: B类图片（宽幅约束类）",
                f"- 蓝色: C类图片（其他图片）"
            ])

            # 根据实际运行环境添加说明
            if self.use_photoshop:
                content.extend([
                    f"",
                    f"## 正式环境说明",
                    f"",
                    f"正式环境下，使用Photoshop处理实际图片，生成高质量输出。",
                    f"图片按照A->B->C的顺序排列，确保最佳的空间利用率。"
                ])
            else:
                content.extend([
                    f"",
                    f"## 测试模式说明",
                    f"",
                    f"测试模式下，使用色块替代实际图片，不启动Photoshop，提高测试效率。",
                    f"色块图片保存为PNG格式，缩小模型比率用于减小生成图片的尺寸。"
                ])

            # 写入文件
            with open(doc_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(content))

            self.log_signal.emit(f"画布说明文档已生成: {doc_path}")

        except Exception as e:
            self.log_signal.emit(f"生成画布说明文档失败: {str(e)}")
            self.log_signal.emit(traceback.format_exc())
