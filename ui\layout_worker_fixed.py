#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
布局工作线程模块

提供布局任务的异步处理功能：
1. 支持多线程处理
2. 支持进度显示
3. 支持状态更新
4. 支持错误处理
"""

import os
import sys
import math
import logging
import time
import traceback
import random
import gc
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple
import io

from PyQt6.QtCore import pyqtSignal

# 导入基类
from ui.base_worker import BaseWorker

# 导入图片排列器
from core.image_arranger import ImageArranger
from core.tetris_packer import TetrisPacker
from core.image_classifier import ImageClassifier
from utils.photoshop_helper import PhotoshopHelper
from utils.log_file_creator import create_log_file_async
from utils.memory_manager import MemoryManager
from utils.stream_processor import StreamProcessor
import photoshop.api as ps

# 导入图片处理器
from utils.image_processor import get_image_processor

# 配置日志
from utils.log_config import get_logger
log = get_logger("LayoutWorker")

class LayoutWorker(BaseWorker):
    """图片布局工作线程

    提供异步处理图片布局的功能，包括：
    1. 智能排列图片
    2. 自动旋转图片以提高利用率
    3. 高效内存管理
    4. 错误恢复机制
    """

    # 特定信号定义
    finished = pyqtSignal()  # 完成信号
    new_canvas_needed = pyqtSignal(str, list)  # 新画布信号，参数为材质名称和剩余图案
    error_signal = pyqtSignal(str, str)  # 错误信号，参数为错误类型和错误消息
    status_signal = pyqtSignal(str, dict)  # 状态信号，参数为状态类型和状态数据

    def __init__(self):
        """初始化布局工作线程"""
        super().__init__()

        # 初始化变量
        self.pattern_items = []  # 图案项目列表
        self.canvas_name = ""
        self.material_name = ""
        self.output_path = ""
        self.success = False
        self.canvas_sequence = 1  # 画布序号
        self.remaining_patterns = []  # 未处理完的图案

        # 添加图片ID跟踪机制，避免重复处理同一图片
        self.processed_image_ids = set()  # 用于跟踪已处理的图片ID

        # 画布设置
        self.canvas_width_m = 2.0
        self.ppi = 72
        self.image_spacing_cm = 0.1
        self.horizontal_expansion_cm = 0
        self.max_height_cm = 5000  # 默认最大高度50米
        self.canvas_width_cm_with_expansion = self.canvas_width_m * 100  # 初始化时不考虑水平拓展

        # 执行控制
        self.use_photoshop = True
        self.force_fit_remaining = False  # 是否强制将剩余图片放入当前画布

        # 测试模式设置
        self.is_test_mode = False  # 是否开启测试模式
        self.miniature_ratio = 0.02  # 缩小模型比率
        self.is_test_all_data = False  # 是否测试全部数据

        # 图片处理器
        self.image_processor = None

        # 性能监控
        self.last_progress_time = None
        self.processed_images = 0
        self.total_images = 0
        self.error_count = 0
        self.retry_count = 0
        self.max_retries = 3  # 最大重试次数

        # 排列速度监控
        self.arrangement_start_time = None  # 排列开始时间
        self.images_arranged = 0  # 已排列图片数
        self.last_speed_update_time = None  # 上次速度更新时间
        self.speed_update_interval = 2.0  # 速度更新间隔（秒）

        # 内存管理
        self.memory_check_interval = 10  # 每处理多少块图片检查一次内存
        self.last_memory_check = 0

        # 内存管理
        self.memory_manager = MemoryManager()
        self.stream_processor = StreamProcessor(
            batch_size=100,  # 默认批处理大小
            max_queue_size=1000
        )

        # 保存异步任务线程的引用，防止线程被垃圾回收
        self.async_threads = []

    def set_pattern_items(self, items):
        """设置图案项目列表"""
        self.pattern_items = items

    def set_canvas_name(self, name):
        """设置画布名称"""
        self.canvas_name = name

    def set_material_name(self, name):
        """设置材料名称"""
        self.material_name = name

    def set_output_path(self, path):
        """设置输出路径"""
        self.output_path = path

    def set_canvas_sequence(self, sequence):
        """设置画布序号"""
        self.canvas_sequence = sequence

    def set_canvas_settings(self, settings):
        """设置画布参数"""
        self.canvas_width_m = settings.get('canvas_width_m', 2.0)
        self.ppi = settings.get('ppi', 72)
        self.image_spacing_cm = settings.get('image_spacing_cm', 0.1)
        self.horizontal_expansion_cm = settings.get('horizontal_expansion_cm', 0)
        self.max_height_cm = settings.get('max_height_cm', 5000)
        # 更新包含水平拓展的画布宽度
        self.canvas_width_cm_with_expansion = self.canvas_width_m * 100 + self.horizontal_expansion_cm

    def set_use_photoshop(self, use_ps):
        """设置是否使用Photoshop"""
        self.use_photoshop = use_ps

    def set_test_mode_settings(self, settings):
        """设置测试模式参数

        Args:
            settings: 测试模式设置字典，包含is_test_mode、miniature_ratio和is_test_all_data
        """
        if settings and isinstance(settings, dict):
            self.is_test_mode = settings.get('is_test_mode', False)
            self.miniature_ratio = settings.get('miniature_ratio', 0.02)
            self.is_test_all_data = settings.get('is_test_all_data', False)
            self.log_signal.emit(f"测试模式设置: 启用={self.is_test_mode}, 缩小比率={self.miniature_ratio}, 测试全部数据={self.is_test_all_data}")

            # 如果是测试模式，不使用Photoshop
            if self.is_test_mode:
                self.use_photoshop = False

    def set_config_manager(self, config_manager):
        """设置配置管理器

        Args:
            config_manager: 配置管理器实例
        """
        self.config_manager = config_manager

    def stop(self):
        """停止任务"""
        super().stop()
        self.log_signal.emit("布局任务停止请求已发送")

        # 停止所有异步线程
        if hasattr(self, 'async_threads') and self.async_threads:
            self.log_signal.emit(f"正在停止 {len(self.async_threads)} 个异步任务...")
            for thread in self.async_threads[:]:  # 使用副本进行迭代，避免在迭代过程中修改列表
                try:
                    if thread.isRunning():
                        thread.wait(1000)  # 最多等待1秒
                    if thread in self.async_threads:
                        self.async_threads.remove(thread)
                except Exception as e:
                    self.log_signal.emit(f"停止异步任务失败: {str(e)}")

        # 测试模式下不调用Photoshop
        if hasattr(self, 'is_test_mode') and self.is_test_mode:
            self.log_signal.emit("测试模式下跳过Photoshop资源清理")
            return

        # 尝试清理Photoshop资源，但不要阻塞线程
        try:
            from utils.photoshop_helper import PhotoshopHelper
            # 使用安全清理函数
            PhotoshopHelper.safe_cleanup_resources()
        except Exception as e:
            self.log_signal.emit(f"停止时清理资源失败: {str(e)}")

    def _cm_to_px(self, cm_value):
        """将厘米转换为像素

        Args:
            cm_value: 厘米值

        Returns:
            int: 像素值
        """
        # 1厘米 = 0.393701英寸
        inches = cm_value * 0.393701
        # 像素 = 英寸 * PPI
        pixels = int(inches * self.ppi)
        return pixels

    def run(self):
        """运行布局任务"""
        try:
            # 初始化性能监控
            self._start_task("布局")
            self.last_progress_time = self.start_time
            self.processed_images = 0
            self.error_count = 0
            self.retry_count = 0

            # 启动内存监控
            self.memory_manager.start_monitoring(interval=10.0)  # 每10秒监控一次内存

            # 初始化图片处理器
            test_mode_config = {
                'miniature_ratio': self.miniature_ratio,
                'is_test_all_data': self.is_test_all_data
            }
            self.image_processor = get_image_processor(self.is_test_mode, test_mode_config)

            # 检查测试模式设置
            if self.is_test_mode:
                self.log_signal.emit(f"测试模式已启用，缩小模型比率: {self.miniature_ratio}")
                self.log_signal.emit("测试模式下不启动Photoshop，使用色块替代图片")

            self.log_signal.emit(f"开始处理 {self.material_name} 的布局任务")
            self.progress_signal.emit(0)

            # 发送状态信息
            self.status_signal.emit("start", {
                "material_name": self.material_name,
                "start_time": self.start_time,
                "estimated_duration": 0
            })

            # 转换单位: m -> cm -> px
            canvas_width_cm = self.canvas_width_m * 100

            # 考虑横向扩展 (厘米单位)
            self.canvas_width_cm_with_expansion = canvas_width_cm + self.horizontal_expansion_cm
            self.log_signal.emit(f"画布宽度: {canvas_width_cm}厘米 + 水平拓展: {self.horizontal_expansion_cm}厘米 = {self.canvas_width_cm_with_expansion}厘米")

            # 转换为像素
            canvas_width_px = self._cm_to_px(self.canvas_width_cm_with_expansion)

            # 设置最大高度限制
            max_height_px = self._cm_to_px(self.max_height_cm)

            # 初始化图片信息列表
            images_info = []
            processed_patterns = []  # 已处理的图案列表
            remaining_patterns = []  # 剩余未处理图案列表

            # 添加间距日志信息
            # 创建装箱器 - 根据实际PPI计算间距
            spacing_px = max(1, int(self.image_spacing_cm * self.ppi / 2.54))
            self.log_signal.emit(f"图片间距: {spacing_px}像素 (基于{self.image_spacing_cm}厘米和{self.ppi}PPI)")

            # 创建图片排列器
            self.log_signal.emit(f"创建图片排列器使用画布宽度: {canvas_width_px}像素（包含水平拓展）")
            self.image_arranger = ImageArranger(log_signal=self.log_signal)
            self.image_arranger.progress_signal.connect(self.progress_signal)
            self.image_arranger.error_signal.connect(lambda msg: self.error_signal.emit("排列错误", msg))

            # 初始化图片排列器
            self.image_arranger.initialize(
                canvas_width_px=canvas_width_px,
                max_height_px=max_height_px,
                image_spacing_px=spacing_px,
                ppi=self.ppi
            )

            # 获取算法参数
            algorithm_params = {}
            try:
                if hasattr(self, 'config_manager') and self.config_manager:
                    params = self.config_manager.get_c_class_algorithm_params()
                    algorithm_params = {
                        'c_horizontal_priority': params.get('c_horizontal_priority', 80),
                        'c_gap_filling_priority': params.get('c_gap_filling_priority', 70),
                        'c_rotation_priority': params.get('c_rotation_priority', 60)
                    }
            except Exception as e:
                self.log_signal.emit(f"获取算法参数失败: {str(e)}，将使用默认参数")
                algorithm_params = {
                    'c_horizontal_priority': 80,
                    'c_gap_filling_priority': 70,
                    'c_rotation_priority': 60
                }

            # 设置图片排列器的算法参数
            self.image_arranger.set_algorithm_params(
                horizontal_priority=algorithm_params['c_horizontal_priority'],
                gap_filling_priority=algorithm_params['c_gap_filling_priority'],
                rotation_priority=algorithm_params['c_rotation_priority']
            )

            # 获取算法设置
            try:
                # 如果有配置管理器，使用配置管理器获取算法设置
                if hasattr(self, 'config_manager') and self.config_manager:
                    algorithm_settings = self.config_manager.get_algorithm_settings()
                    self.log_signal.emit("使用配置管理器获取算法设置")
                else:
                    # 如果没有配置管理器，使用默认设置
                    self.log_signal.emit("未设置配置管理器，使用默认算法设置")
                    algorithm_settings = {
                        # 图片分类参数
                        'class_a_threshold': 95,
                        'class_b_error_range': 5,

                        # 旋转决策参数
                        'class_a_rotation_threshold': 20,
                        'class_b_rotation_threshold': 20,
                        'class_c_rotation_threshold': 15,
                        'extreme_ratio_threshold': 3.0,
                        'extreme_ratio_utilization': 60,

                        # 行空隙填充参数
                        'row_utilization_threshold': 95,
                        'class_c_gap_error_range': 5,
                        'enable_row_gap_filling': True
                    }
            except Exception as e:
                # 如果获取算法设置失败，使用默认设置
                self.log_signal.emit(f"获取算法设置失败: {str(e)}，使用默认设置")
                algorithm_settings = {
                    # 图片分类参数
                    'class_a_threshold': 95,
                    'class_b_error_range': 5,

                    # 旋转决策参数
                    'class_a_rotation_threshold': 20,
                    'class_b_rotation_threshold': 20,
                    'class_c_rotation_threshold': 15,
                    'extreme_ratio_threshold': 3.0,
                    'extreme_ratio_utilization': 60,

                    # 行空隙填充参数
                    'row_utilization_threshold': 95,
                    'class_c_gap_error_range': 5,
                    'enable_row_gap_filling': True
                }

            # 计算宽度阈值（画布宽度的百分比）
            class_a_threshold = algorithm_settings['class_a_threshold'] / 100.0
            class_b_error_range = algorithm_settings['class_b_error_range'] / 100.0

            # 使用包含水平拓展的画布宽度
            canvas_width_cm = self.canvas_width_cm_with_expansion
            # 获取原始画布宽度（不包含水平拓展）
            original_canvas_width_cm = self.canvas_width_m * 100  # 转换米为厘米

            # 为每个图片添加唯一标识符，格式为"图片名_宽_高_索引"
            # 使用索引号代替行号，确保每个图片都有唯一的标识符
            self.log_signal.emit(f"开始为 {len(self.pattern_items)} 个图片添加唯一标识符...")

            for index, pattern in enumerate(self.pattern_items):
                width_cm = pattern['width_cm']
                height_cm = pattern['height_cm']
                pattern_name = pattern.get('pattern_name', '')

                # 创建基础唯一标识符，格式为"图片名_宽_高"
                base_unique_id = f"{pattern_name}_{width_cm:.1f}_{height_cm:.1f}"
                pattern['base_unique_id'] = base_unique_id

                # 使用索引号作为初始唯一标识符的一部分
                # 这样即使在初始状态下，每个图片也有唯一的标识符
                pattern['index'] = index

                # 添加行号信息，用于跟踪图片在不同画布上的位置
                # 初始行号为-1，表示尚未放置，后续会在放置图片时更新为实际行号
                pattern['row_number'] = -1

                # 完整唯一标识符，包含索引号
                pattern['unique_id'] = f"{base_unique_id}_{index}"

            # 一次循环完成A/B/C类图片分类
            self.log_signal.emit("开始一次循环完成A/B/C类图片分类...")
            self.log_signal.emit(f"图片总数: {len(self.pattern_items)}")
            self.log_signal.emit("图片分类流程: 先A类，再B类，最后C类")
            image_classifier = ImageClassifier(log_signal=self.log_signal)

            # 1. 分类图片
            self.log_signal.emit("第一步: 分类图片为A/B/C类...")
            class_a_patterns, class_b_patterns, class_c_patterns = image_classifier.classify_images(
                pattern_items=self.pattern_items,
                canvas_width_cm=canvas_width_cm,
                class_a_threshold=class_a_threshold,
                class_b_error_range=class_b_error_range,  # 严格执行5%误差范围
                original_canvas_width_cm=original_canvas_width_cm
            )

            # 2. 在每个类别内部按面积降序排序
            self.log_signal.emit("第二步: 对A类图片按面积降序排序...")
            class_a_patterns.sort(key=lambda p: -(p['width_cm'] * p['height_cm']))

            # 3. 处理B类图片分组 - 返回二维数组结构
            self.log_signal.emit("第三步: 处理B类图片分组，组织为二维数组结构...")
            b_class_groups, downgraded_c_patterns = image_classifier.process_b_class_groups(
                class_b_patterns=class_b_patterns,
                canvas_width_cm=canvas_width_cm,
                original_canvas_width_cm=original_canvas_width_cm
            )

            # 计算B类图片总数
            total_b_images = sum(len(group) for group in b_class_groups)
            self.log_signal.emit(f"B类图片分组完成: {len(b_class_groups)} 个组，共 {total_b_images} 个图片")

            # 4. 将降级的B类图片添加到C类图片列表，但保持标记为原始B类
            self.log_signal.emit(f"第四步: 将{len(downgraded_c_patterns)}个降级的B类图片添加到C类图片列表...")
            for pattern in downgraded_c_patterns:
                pattern['original_class'] = 'B'  # 记录原始类别
                pattern['image_class'] = 'C'     # 设置为C类

            class_c_patterns.extend(downgraded_c_patterns)

            # 5. 对C类图片进行排序
            self.log_signal.emit("第五步: 对C类图片进行排序...")
            class_c_patterns = image_classifier.sort_c_class_patterns(
                class_c_patterns=class_c_patterns,
                canvas_width_cm=canvas_width_cm
            )

            # 6. 将分类结果保存到Excel文件
            self.log_signal.emit("第六步: 将分类结果保存到Excel文件...")

            # 创建Excel管理器
            from core.excel_manager import ExcelManager
            excel_manager = ExcelManager(log_signal=self.log_signal)

            # 构建输出文件路径
            excel_output_path = os.path.join(
                os.path.dirname(self.output_path),
                f"{self.material_name}_分类结果.xlsx"
            )

            # 保存分类结果到Excel文件
            success, message = excel_manager.save_classification_to_excel(
                class_a_patterns=class_a_patterns,
                class_b_patterns=class_b_patterns,
                class_c_patterns=class_c_patterns,
                output_path=excel_output_path
            )

            if success:
                self.log_signal.emit(f"分类结果已保存到Excel文件: {excel_output_path}")
            else:
                self.log_signal.emit(f"保存分类结果失败: {message}")

            # 7. 从Excel文件读取分类结果
            self.log_signal.emit("第七步: 从Excel文件读取分类结果...")

            # 读取分类结果
            success, message, loaded_a_patterns, loaded_b_patterns, loaded_c_patterns = excel_manager.load_classification_from_excel(
                excel_path=excel_output_path
            )

            if success:
                self.log_signal.emit(f"从Excel文件读取分类结果成功: {message}")
                # 使用读取的分类结果
                class_a_patterns = loaded_a_patterns
                class_b_patterns = loaded_b_patterns
                class_c_patterns = loaded_c_patterns

                # 重新处理B类图片分组
                b_class_groups, downgraded_c_patterns = image_classifier.process_b_class_groups(
                    class_b_patterns=class_b_patterns,
                    canvas_width_cm=canvas_width_cm,
                    original_canvas_width_cm=original_canvas_width_cm
                )

                # 将降级的B类图片添加到C类图片列表
                for pattern in downgraded_c_patterns:
                    pattern['original_class'] = 'B'
                    pattern['image_class'] = 'C'

                class_c_patterns.extend(downgraded_c_patterns)

                # 对C类图片进行排序
                class_c_patterns = image_classifier.sort_c_class_patterns(
                    class_c_patterns=class_c_patterns,
                    canvas_width_cm=canvas_width_cm
                )
            else:
                self.log_signal.emit(f"从Excel文件读取分类结果失败: {message}，将使用内存中的分类结果")

            # 使用图片排列器进行排列
            self.log_signal.emit("开始使用图片排列器进行排列...")

            # 为每个图片添加路径信息
            for pattern in class_a_patterns + class_b_patterns + class_c_patterns:
                if 'path' not in pattern:
                    pattern['path'] = pattern.get('image_path', '')

            # 使用图片排列器进行排列
            arranged_images = self.image_arranger.arrange_images(
                class_a_patterns=class_a_patterns,
                class_b_patterns=class_b_patterns,
                class_c_patterns=class_c_patterns
            )

            # 获取排列结果
            images_info = arranged_images

            # 检查是否有图片未能排列
            arranged_count = len(arranged_images)
            total_count = len(class_a_patterns) + len(class_b_patterns) + len(class_c_patterns)

            if arranged_count < total_count:
                self.log_signal.emit(f"警告: 有 {total_count - arranged_count} 个图片未能排列，可能是因为画布已满")
                canvas_is_full = True

                # 收集未排列的图片
                arranged_ids = set(img.get('unique_id', '') for img in arranged_images)

                # 收集A类未排列图片
                for pattern in class_a_patterns:
                    unique_id = pattern.get('unique_id', '')
                    if unique_id and unique_id not in arranged_ids:
                        remaining_patterns.append(pattern)

                # 收集B类未排列图片
                for pattern in class_b_patterns:
                    unique_id = pattern.get('unique_id', '')
                    if unique_id and unique_id not in arranged_ids:
                        remaining_patterns.append(pattern)

                # 收集C类未排列图片
                for pattern in class_c_patterns:
                    unique_id = pattern.get('unique_id', '')
                    if unique_id and unique_id not in arranged_ids:
                        remaining_patterns.append(pattern)

                self.log_signal.emit(f"收集到 {len(remaining_patterns)} 个未排列的图片，将在新画布继续排列")

            # 使用图片排列器已经完成了图片排列，不需要再手动排列
            self.log_signal.emit("图片排列已完成，准备创建画布...")

            # 检查是否取消任务
            if self.is_canceled:
                self.log_signal.emit("布局任务已取消")
                self.success = False
                self.finished.emit()
                return

            # 获取画布高度
            canvas_height = self.image_arranger.get_canvas_height()
            self.log_signal.emit(f"画布高度: {canvas_height}像素 ({canvas_height/self.ppi*2.54:.2f}厘米)")

            # 准备创建画布
            self.log_signal.emit("准备创建画布...")

            # 创建画布
            self.log_signal.emit(f"创建画布: {self.canvas_name}_{self.canvas_sequence}...")

            # 使用图片处理器创建画布
            canvas_created = self.image_processor.create_canvas(
                width=canvas_width_px,
                height=canvas_height,
                name=f"{self.canvas_name}_{self.canvas_sequence}",
                ppi=self.ppi
            )

            if not canvas_created:
                self.log_signal.emit("创建画布失败")
                self.success = False
                self.finished.emit()
                return

            # 放置图片
            self.log_signal.emit(f"开始放置 {len(images_info)} 个图片...")

            # 放置所有图片
            total_images = len(images_info)
            images_placed = 0

            # 遍历所有图片并放置
            for i, image_info in enumerate(images_info):
                # 检查是否取消任务
                if self.is_canceled:
                    self.log_signal.emit("布局任务已取消")
                    self.success = False
                    self.finished.emit()
                    return

                # 准备图片信息
                image_path = image_info.get('path', '')
                x = image_info.get('x', 0)
                y = image_info.get('y', 0)
                width = image_info.get('width', 0)
                height = image_info.get('height', 0)
                need_rotation = image_info.get('need_rotation', False)
                image_name = image_info.get('name', os.path.basename(image_path))
                image_class = image_info.get('image_class', 'C')

                # 创建完整的图片信息字典
                place_info = {
                    'image_path': image_path,
                    'x': x,
                    'y': y,
                    'width': width,
                    'height': height,
                    'rotated': need_rotation,
                    'name': image_name,
                    'image_class': image_class
                }

                # 使用图片处理器放置图片
                success = self.image_processor.place_image(place_info)

                if not success:
                    self.log_signal.emit(f"警告: 放置图片 {image_name} 失败")

                # 更新进度
                images_placed += 1
                if i % 10 == 0 or i == len(images_info) - 1:  # 每10个图片更新一次进度，减少UI更新频率
                    progress = int((images_placed / total_images) * 100)
                    self.progress_signal.emit(progress)

            # 保存画布
            self.log_signal.emit("保存画布...")

            # 使用图片处理器保存画布
            save_success = self.image_processor.save_canvas(self.output_path)

            if not save_success:
                self.log_signal.emit("保存画布失败")
                self.success = False
                self.finished.emit()
                return

            # 生成画布说明文档
            self.log_signal.emit("生成画布说明文档...")

            # 准备画布信息
            canvas_info = {
                'canvas_name': f"{self.canvas_name}_{self.canvas_sequence}",
                'material_name': self.material_name,
                'canvas_sequence': self.canvas_sequence,
                'canvas_width_m': self.canvas_width_m,
                'canvas_width_px': canvas_width_px,
                'canvas_height': canvas_height,
                'horizontal_expansion_cm': self.horizontal_expansion_cm,
                'max_height_cm': self.max_height_cm,
                'ppi': self.ppi,
                'generation_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

            # 使用图片处理器生成说明文档
            desc_success = self.image_processor.generate_description(
                output_path=self.output_path,
                images_info=images_info,
                canvas_info=canvas_info
            )

            if not desc_success:
                self.log_signal.emit("警告: 生成画布说明文档失败")

            # 关闭画布
            self.log_signal.emit("关闭画布...")
            self.image_processor.close_canvas()

            # 清理资源
            self.image_processor.cleanup()

            # 检查是否有剩余图片
            if remaining_patterns:
                self.log_signal.emit(f"有 {len(remaining_patterns)} 个图片未能放置，需要创建新画布")

                # 发送新画布信号
                self.new_canvas_needed.emit(self.material_name, remaining_patterns)

            # 完成任务
            self.success = True
            self.log_signal.emit("布局任务完成")
            self.finished.emit()
        except Exception as e:
            # 记录错误
            self.error_count += 1
            error_message = f"布局任务出错: {str(e)}"
            self.log_signal.emit(error_message)
            self.log_signal.emit(traceback.format_exc())

            # 发送错误信号
            self.error_signal.emit("布局错误", error_message)

            # 设置任务失败
            self.success = False
            self.finished.emit()

    def process_b_class_images(self, images_info, processed_patterns, remaining_patterns):
        """处理B类图片，使用严格按组排列的算法

        Args:
            images_info: 图片信息列表
            processed_patterns: 已处理的图案列表
            remaining_patterns: 剩余未处理图案列表

        Returns:
            tuple: (images_info, processed_patterns, remaining_patterns)
        """
        # 使用图片排列器处理B类图片，不需要手动处理
        return images_info, processed_patterns, remaining_patterns

    def preprocess_c_class_patterns(self, class_c_patterns):
        """预处理C类图片，对相似尺寸的图片进行分组并做出统一的旋转决策

        Args:
            class_c_patterns: C类图片列表

        Returns:
            list: 预处理后的C类图片列表
        """
        # 使用图片排列器处理C类图片，不需要手动处理
        return class_c_patterns
