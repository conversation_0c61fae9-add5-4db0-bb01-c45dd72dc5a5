#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
登录对话框模块

提供用户登录功能：
1. 显示登录界面
2. 验证用户凭据
3. 保存用户会话信息
"""

import logging
import os
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel,
    QLineEdit, QPushButton, QDialogButtonBox,
    QMessageBox, QCheckBox, QFrame
)
from PyQt6.QtCore import Qt, QSettings
from PyQt6.QtGui import QFont, QPainter, QColor, QBrush, QPen, QLinearGradient

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
log = logging.getLogger("LoginDialog")

# 自定义Logo组件
class LogoWidget(QFrame):
    """自定义Logo组件，显示一个圆形背景的AI标志"""
    
    def __init__(self, parent=None):
        super(LogoWidget, self).__init__(parent)
        self.setFixedSize(70, 70)
        self.setStyleSheet("background-color: transparent;")

    def paintEvent(self, event):
        """绘制Logo"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        # 绘制圆形背景
        gradient = QLinearGradient(0, 0, self.width(), self.height())
        gradient.setColorAt(0, QColor(0, 120, 215))  # Win11蓝色
        gradient.setColorAt(1, QColor(0, 103, 192))

        painter.setBrush(QBrush(gradient))
        painter.setPen(Qt.PenStyle.NoPen)
        painter.drawEllipse(0, 0, self.width(), self.height())

        # 绘制简单的"AI"文字
        painter.setPen(QPen(QColor(255, 255, 255), 2))
        painter.setFont(QFont("Arial", 28, QFont.Weight.Bold))
        painter.drawText(self.rect(), Qt.AlignmentFlag.AlignCenter, "AI")

class LoginDialog(QDialog):
    """登录对话框类"""

    def __init__(self, supabase_helper, app_name, app_version, parent=None):
        """初始化登录对话框

        Args:
            supabase_helper: Supabase辅助类实例
            app_name: 应用名称
            app_version: 应用版本
            parent: 父窗口
        """
        super().__init__(parent)
        self.supabase_helper = supabase_helper
        self.app_name = app_name
        self.app_version = app_version
        self.init_ui()
        self.load_saved_credentials()

    def init_ui(self):
        """初始化UI界面"""
        self.setWindowTitle(f"{self.app_name} 登录")
        self.setFixedSize(380, 450)
        self.setWindowFlags(self.windowFlags() & ~Qt.WindowType.WindowContextHelpButtonHint)

        # Win11风格设置
        self.setStyleSheet("""
            QDialog {
                background-color: #f5f5f5;
            }
            QLabel {
                color: #202020;
            }
            QLineEdit {
                border: 1px solid #d0d0d0;
                border-radius: 4px;
                padding: 8px;
                background-color: white;
                selection-background-color: #0078d7;
                min-height: 20px;
            }
            QLineEdit:focus {
                border: 1px solid #0078d7;
            }
            QPushButton#loginButton {
                background-color: #0078d7;
                color: white;
                border-radius: 4px;
                padding: 8px;
                font-weight: bold;
                min-height: 36px;
            }
            QPushButton#loginButton:hover {
                background-color: #1683d8;
            }
            QPushButton#loginButton:pressed {
                background-color: #006cc1;
            }
            QCheckBox {
                spacing: 5px;
            }
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
                border-radius: 2px;
                border: 1px solid #d0d0d0;
            }
            QCheckBox::indicator:checked {
                background-color: #0078d7;
                border: 1px solid #0078d7;
            }
            QFrame#loginCard {
                background-color: white;
                border-radius: 8px;
                border: 1px solid #e0e0e0;
            }
        """)

        # 创建主布局
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(0)

        # 创建登录卡片
        login_card = QFrame()
        login_card.setObjectName("loginCard")

        # 卡片内部布局
        card_layout = QVBoxLayout(login_card)
        card_layout.setContentsMargins(20, 25, 20, 25)
        card_layout.setSpacing(12)

        # 添加Logo
        logo_layout = QHBoxLayout()
        self.logo = LogoWidget()
        logo_layout.addStretch()
        logo_layout.addWidget(self.logo)
        logo_layout.addStretch()
        card_layout.addLayout(logo_layout)

        # 版本标签
        version_label = QLabel(f"v{self.app_version}")
        version_label.setFont(QFont("微软雅黑", 9))
        version_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        version_label.setStyleSheet("color: #707070; margin-bottom: 10px;")
        card_layout.addWidget(version_label)

        # 添加间距
        card_layout.addSpacing(5)

        # 用户名输入
        username_label = QLabel("用户名")
        username_label.setFont(QFont("微软雅黑", 10))
        card_layout.addWidget(username_label)

        self.username_input = QLineEdit()
        self.username_input.setFont(QFont("微软雅黑", 10))
        self.username_input.setPlaceholderText("请输入邮箱")
        card_layout.addWidget(self.username_input)

        # 密码输入
        password_label = QLabel("密码")
        password_label.setFont(QFont("微软雅黑", 10))
        card_layout.addWidget(password_label)

        self.password_input = QLineEdit()
        self.password_input.setFont(QFont("微软雅黑", 10))
        self.password_input.setEchoMode(QLineEdit.EchoMode.Password)
        self.password_input.setPlaceholderText("请输入密码")
        card_layout.addWidget(self.password_input)

        # 记住密码选项
        remember_layout = QHBoxLayout()
        self.remember_checkbox = QCheckBox("记住密码")
        self.remember_checkbox.setFont(QFont("微软雅黑", 9))
        remember_layout.addWidget(self.remember_checkbox)
        remember_layout.addStretch()
        card_layout.addLayout(remember_layout)

        # 添加间距
        card_layout.addSpacing(5)

        # 登录按钮
        self.login_button = QPushButton("登 录")
        self.login_button.setObjectName("loginButton")
        self.login_button.setFont(QFont("微软雅黑", 11))
        self.login_button.clicked.connect(self.try_login)
        card_layout.addWidget(self.login_button)

        # 状态标签
        self.status_label = QLabel("")
        self.status_label.setFont(QFont("微软雅黑", 9))
        self.status_label.setStyleSheet("color: #e81123; margin-top: 5px;")
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        card_layout.addWidget(self.status_label)

        # 将登录卡片添加到主布局
        main_layout.addWidget(login_card)

        # 设置主布局
        self.setLayout(main_layout)

        # 设置默认焦点
        self.username_input.setFocus()

        # 设置回车键触发登录
        self.username_input.returnPressed.connect(self.try_login)
        self.password_input.returnPressed.connect(self.try_login)

    def load_saved_credentials(self):
        """加载保存的用户名和密码"""
        settings = QSettings("DeAI", "RobotPSSmart")
        saved_username = settings.value("username", "")
        saved_password = settings.value("password", "")
        saved_remember = settings.value("remember_password", False, type=bool)

        if saved_username:
            self.username_input.setText(saved_username)
            if saved_remember and saved_password:
                self.password_input.setText(saved_password)
                self.remember_checkbox.setChecked(True)
                self.password_input.setFocus()

    def try_login(self):
        """尝试登录"""
        email = self.username_input.text().strip()
        password = self.password_input.text()

        if not email:
            self.status_label.setText("请输入用户名")
            return

        if not password:
            self.status_label.setText("请输入密码")
            return

        # 更新UI状态
        self.status_label.setText("正在登录...")
        self.login_button.setEnabled(False)
        self.login_button.setText("登录中...")
        
        # 确保UI更新
        from PyQt6.QtWidgets import QApplication
        QApplication.processEvents()

        # 尝试登录
        success, message = self.supabase_helper.login(email, password)

        if success:
            # 保存用户名和密码（如果选择了记住密码）
            settings = QSettings("DeAI", "RobotPSSmart")
            settings.setValue("username", email)
            settings.setValue("remember_password", self.remember_checkbox.isChecked())

            if self.remember_checkbox.isChecked():
                settings.setValue("password", password)
            else:
                settings.setValue("password", "")

            log.info(f"用户 {email} 登录成功")
            self.accept()
        else:
            log.error(f"登录失败: {message}")
            self.status_label.setText(message)
            self.login_button.setText("登 录")
            self.login_button.setEnabled(True)
