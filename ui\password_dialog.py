#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
密码验证对话框模块

提供通用密码验证功能：
1. 显示密码输入对话框
2. 验证用户输入密码是否正确
3. 支持自定义标题和提示文本
"""

import logging
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel,
    QLineEdit, QPushButton, QDialogButtonBox,
    QMessageBox
)
from PyQt6.QtCore import Qt

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
log = logging.getLogger("PasswordDialog")

class PasswordDialog(QDialog):
    """密码验证对话框类"""

    def __init__(self, correct_password, parent=None, title="密码验证", prompt="请输入密码:", error_msg="密码验证失败"):
        """初始化密码对话框

        Args:
            correct_password: 正确密码
            parent: 父窗口
            title: 对话框标题
            prompt: 密码输入提示文本
            error_msg: 密码错误提示文本
        """
        super().__init__(parent)
        self.correct_password = correct_password
        self.dialog_title = title
        self.prompt_text = prompt
        self.error_message = error_msg
        self.init_ui()

    def init_ui(self):
        """初始化UI界面"""
        self.setWindowTitle(self.dialog_title)
        self.setMinimumWidth(300)
        self.setWindowFlags(self.windowFlags() & ~Qt.WindowType.WindowContextHelpButtonHint)

        # 创建主布局
        layout = QVBoxLayout(self)

        # 添加密码输入区域
        self.password_label = QLabel(self.prompt_text)
        layout.addWidget(self.password_label)

        self.password_edit = QLineEdit()
        self.password_edit.setEchoMode(QLineEdit.EchoMode.Password)
        self.password_edit.setPlaceholderText("输入密码")
        layout.addWidget(self.password_edit)

        # 添加按钮
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok |
            QDialogButtonBox.StandardButton.Cancel
        )
        button_box.accepted.connect(self.verify_password)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

        # 设置回车键触发验证
        self.password_edit.returnPressed.connect(self.verify_password)

    def verify_password(self):
        """验证密码"""
        entered_password = self.password_edit.text()

        if entered_password == self.correct_password:
            self.accept()
        else:
            QMessageBox.warning(
                self,
                "密码错误",
                self.error_message,
                QMessageBox.StandardButton.Ok
            )
            self.password_edit.clear()
            self.password_edit.setFocus()