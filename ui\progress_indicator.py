#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
进度指示器模块

提供高级进度指示功能：
1. 多阶段进度显示
2. 实时速度和剩余时间估计
3. 性能监控和统计
4. 自定义样式和动画
"""

import os
import sys
import logging
import time
import math
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple, Union

from PyQt6.QtCore import (
    Qt, QSize, QRect, QPoint, QTimer, QPropertyAnimation, 
    QEasingCurve, QSequentialAnimationGroup, pyqtSignal, pyqtProperty
)
from PyQt6.QtGui import (
    QPainter, QColor, QPen, QFont, QFontMetrics, 
    QLinearGradient, QBrush, QPainterPath, QPixmap
)
from PyQt6.QtWidgets import (
    QWidget, QProgressBar, QLabel, QVBoxLayout, QHBoxLayout, 
    QFrame, QSizePolicy, QGraphicsDropShadowEffect, QStyleOption, QStyle
)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
log = logging.getLogger("ProgressIndicator")

class ProgressStage:
    """进度阶段类，表示一个进度指示器的阶段"""
    
    def __init__(self, name: str, weight: float = 1.0, color: QColor = None):
        """
        初始化进度阶段
        
        Args:
            name: 阶段名称
            weight: 阶段权重，用于计算总进度
            color: 阶段颜色
        """
        self.name = name
        self.weight = max(0.1, weight)  # 确保权重至少为0.1
        self.color = color or QColor(0, 120, 215)  # 默认蓝色
        self.progress = 0.0  # 当前阶段进度，0.0-1.0
        self.start_time = None
        self.end_time = None
        self.is_active = False
        self.is_completed = False
        self.status = ""
        
    def start(self):
        """开始阶段"""
        self.start_time = time.time()
        self.is_active = True
        self.is_completed = False
        self.progress = 0.0
        
    def complete(self):
        """完成阶段"""
        self.end_time = time.time()
        self.is_active = False
        self.is_completed = True
        self.progress = 1.0
        
    def update(self, progress: float):
        """
        更新阶段进度
        
        Args:
            progress: 进度值，0.0-1.0
        """
        self.progress = max(0.0, min(1.0, progress))
        
    def get_duration(self) -> float:
        """
        获取阶段持续时间
        
        Returns:
            float: 持续时间（秒）
        """
        if not self.start_time:
            return 0.0
            
        end = self.end_time or time.time()
        return end - self.start_time
        
    def get_estimated_remaining_time(self) -> float:
        """
        获取估计剩余时间
        
        Returns:
            float: 估计剩余时间（秒）
        """
        if not self.is_active or self.progress >= 1.0:
            return 0.0
            
        if self.progress <= 0.0:
            return float('inf')
            
        elapsed = time.time() - self.start_time
        total_estimated = elapsed / self.progress
        return total_estimated - elapsed

class AnimatedProgressBar(QProgressBar):
    """带动画效果的进度条"""
    
    def __init__(self, parent=None):
        """初始化进度条"""
        super().__init__(parent)
        
        # 设置样式
        self.setTextVisible(False)
        self.setMinimumHeight(10)
        self.setMaximumHeight(10)
        
        # 动画属性
        self._pulse_position = 0.0
        self._pulse_visible = False
        
        # 创建脉冲动画
        self._pulse_animation = QPropertyAnimation(self, b"pulsePosition")
        self._pulse_animation.setDuration(1500)  # 1.5秒一个周期
        self._pulse_animation.setStartValue(0.0)
        self._pulse_animation.setEndValue(1.0)
        self._pulse_animation.setLoopCount(-1)  # 无限循环
        self._pulse_animation.setEasingCurve(QEasingCurve.Type.InOutQuad)
        
        # 设置样式表
        self.setStyleSheet("""
            QProgressBar {
                border: none;
                background-color: #f0f0f0;
                border-radius: 5px;
            }
            
            QProgressBar::chunk {
                background-color: qlineargradient(
                    x1:0, y1:0, x2:1, y2:0,
                    stop:0 #4a86e8, stop:1 #17b978
                );
                border-radius: 5px;
            }
        """)
    
    def setPulseVisible(self, visible: bool):
        """设置脉冲是否可见"""
        self._pulse_visible = visible
        if visible:
            self._pulse_animation.start()
        else:
            self._pulse_animation.stop()
        self.update()
    
    def pulsePosition(self) -> float:
        """获取脉冲位置"""
        return self._pulse_position
    
    def setPulsePosition(self, pos: float):
        """设置脉冲位置"""
        self._pulse_position = pos
        self.update()
    
    # 定义属性
    pulsePosition = pyqtProperty(float, pulsePosition, setPulsePosition)
    
    def paintEvent(self, event):
        """绘制事件"""
        # 调用基类绘制
        super().paintEvent(event)
        
        # 如果脉冲可见，绘制脉冲效果
        if self._pulse_visible:
            painter = QPainter(self)
            painter.setRenderHint(QPainter.RenderHint.Antialiasing)
            
            # 计算脉冲位置
            width = self.width()
            pulse_width = width * 0.2  # 脉冲宽度为总宽度的20%
            pulse_pos = (width - pulse_width) * self._pulse_position
            
            # 创建渐变
            gradient = QLinearGradient(pulse_pos, 0, pulse_pos + pulse_width, 0)
            gradient.setColorAt(0.0, QColor(255, 255, 255, 0))
            gradient.setColorAt(0.5, QColor(255, 255, 255, 128))
            gradient.setColorAt(1.0, QColor(255, 255, 255, 0))
            
            # 绘制脉冲
            painter.fillRect(QRect(int(pulse_pos), 0, int(pulse_width), self.height()), gradient)
            painter.end()

class ProgressIndicator(QWidget):
    """高级进度指示器"""
    
    # 定义信号
    progress_changed = pyqtSignal(float)  # 进度变化信号
    stage_changed = pyqtSignal(str)  # 阶段变化信号
    completed = pyqtSignal()  # 完成信号
    
    def __init__(self, parent=None):
        """初始化进度指示器"""
        super().__init__(parent)
        
        # 初始化变量
        self.stages = []  # 阶段列表
        self.current_stage_index = -1  # 当前阶段索引
        self.start_time = None  # 开始时间
        self.last_update_time = None  # 上次更新时间
        self.is_completed = False  # 是否已完成
        self.is_indeterminate = False  # 是否为不确定进度
        
        # 性能监控
        self.speed = 0.0  # 处理速度（项/秒）
        self.items_processed = 0  # 已处理项数
        self.total_items = 0  # 总项数
        
        # 创建UI
        self.init_ui()
        
        # 创建更新定时器
        self.update_timer = QTimer(self)
        self.update_timer.timeout.connect(self.update_display)
        self.update_timer.start(500)  # 每500毫秒更新一次
    
    def init_ui(self):
        """初始化UI"""
        # 创建主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(5)
        
        # 创建标题和状态布局
        header_layout = QHBoxLayout()
        
        # 创建标题标签
        self.title_label = QLabel("准备中...")
        self.title_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        header_layout.addWidget(self.title_label)
        
        # 添加弹性空间
        header_layout.addStretch()
        
        # 创建状态标签
        self.status_label = QLabel("")
        self.status_label.setFont(QFont("Arial", 9))
        header_layout.addWidget(self.status_label)
        
        # 添加标题和状态布局
        main_layout.addLayout(header_layout)
        
        # 创建进度条
        self.progress_bar = AnimatedProgressBar()
        main_layout.addWidget(self.progress_bar)
        
        # 创建详细信息布局
        details_layout = QHBoxLayout()
        details_layout.setContentsMargins(0, 0, 0, 0)
        details_layout.setSpacing(10)
        
        # 创建阶段标签
        self.stage_label = QLabel("阶段: 准备中")
        self.stage_label.setFont(QFont("Arial", 9))
        details_layout.addWidget(self.stage_label)
        
        # 创建时间标签
        self.time_label = QLabel("时间: 00:00:00")
        self.time_label.setFont(QFont("Arial", 9))
        details_layout.addWidget(self.time_label)
        
        # 创建速度标签
        self.speed_label = QLabel("速度: 0 项/秒")
        self.speed_label.setFont(QFont("Arial", 9))
        details_layout.addWidget(self.speed_label)
        
        # 创建剩余时间标签
        self.eta_label = QLabel("剩余: --:--:--")
        self.eta_label.setFont(QFont("Arial", 9))
        details_layout.addWidget(self.eta_label)
        
        # 添加详细信息布局
        main_layout.addLayout(details_layout)
        
        # 设置最小高度
        self.setMinimumHeight(70)
        
        # 添加阴影效果
        shadow = QGraphicsDropShadowEffect(self)
        shadow.setBlurRadius(10)
        shadow.setColor(QColor(0, 0, 0, 50))
        shadow.setOffset(0, 2)
        self.setGraphicsEffect(shadow)
        
        # 设置样式表
        self.setStyleSheet("""
            ProgressIndicator {
                background-color: white;
                border-radius: 5px;
                padding: 10px;
            }
        """)
    
    def add_stage(self, name: str, weight: float = 1.0, color: QColor = None) -> int:
        """
        添加进度阶段
        
        Args:
            name: 阶段名称
            weight: 阶段权重
            color: 阶段颜色
            
        Returns:
            int: 阶段索引
        """
        stage = ProgressStage(name, weight, color)
        self.stages.append(stage)
        return len(self.stages) - 1
    
    def start(self):
        """开始进度指示"""
        self.start_time = time.time()
        self.last_update_time = self.start_time
        self.is_completed = False
        self.items_processed = 0
        
        # 重置所有阶段
        for stage in self.stages:
            stage.is_active = False
            stage.is_completed = False
            stage.progress = 0.0
        
        # 如果有阶段，开始第一个阶段
        if self.stages:
            self.start_stage(0)
        
        # 更新UI
        self.title_label.setText("正在处理...")
        self.progress_bar.setValue(0)
        self.progress_bar.setPulseVisible(False)
        self.update_display()
    
    def start_stage(self, stage_index: int):
        """
        开始指定阶段
        
        Args:
            stage_index: 阶段索引
        """
        if 0 <= stage_index < len(self.stages):
            # 如果有当前阶段，完成它
            if 0 <= self.current_stage_index < len(self.stages):
                self.stages[self.current_stage_index].complete()
            
            # 开始新阶段
            self.current_stage_index = stage_index
            self.stages[stage_index].start()
            
            # 发送阶段变化信号
            self.stage_changed.emit(self.stages[stage_index].name)
            
            # 更新UI
            self.stage_label.setText(f"阶段: {self.stages[stage_index].name}")
            self.update_display()
    
    def update_progress(self, progress: float, items_processed: int = None, total_items: int = None):
        """
        更新当前阶段进度
        
        Args:
            progress: 进度值，0.0-1.0
            items_processed: 已处理项数
            total_items: 总项数
        """
        # 更新项数
        if items_processed is not None:
            self.items_processed = items_processed
        
        if total_items is not None and total_items > 0:
            self.total_items = total_items
        
        # 如果没有阶段或已完成，忽略更新
        if self.is_completed or self.current_stage_index < 0 or self.current_stage_index >= len(self.stages):
            return
        
        # 更新当前阶段进度
        current_stage = self.stages[self.current_stage_index]
        current_stage.update(progress)
        
        # 计算总进度
        total_progress = self.calculate_total_progress()
        
        # 更新进度条
        self.progress_bar.setValue(int(total_progress * 100))
        
        # 发送进度变化信号
        self.progress_changed.emit(total_progress)
        
        # 计算处理速度
        now = time.time()
        if self.last_update_time and now > self.last_update_time:
            time_diff = now - self.last_update_time
            if time_diff > 0 and hasattr(self, 'last_items_processed'):
                items_diff = self.items_processed - self.last_items_processed
                self.speed = items_diff / time_diff
        
        # 保存当前状态用于下次计算
        self.last_update_time = now
        self.last_items_processed = self.items_processed
        
        # 更新显示
        self.update_display()
    
    def set_indeterminate(self, indeterminate: bool):
        """
        设置是否为不确定进度
        
        Args:
            indeterminate: 是否为不确定进度
        """
        self.is_indeterminate = indeterminate
        self.progress_bar.setPulseVisible(indeterminate)
        
        if indeterminate:
            self.progress_bar.setValue(0)
        else:
            # 恢复到当前进度
            total_progress = self.calculate_total_progress()
            self.progress_bar.setValue(int(total_progress * 100))
    
    def complete(self):
        """完成所有阶段"""
        # 完成当前阶段
        if 0 <= self.current_stage_index < len(self.stages):
            self.stages[self.current_stage_index].complete()
        
        # 完成所有未完成的阶段
        for stage in self.stages:
            if not stage.is_completed:
                stage.complete()
        
        # 设置状态
        self.is_completed = True
        self.progress_bar.setValue(100)
        self.progress_bar.setPulseVisible(False)
        
        # 更新UI
        self.title_label.setText("已完成")
        self.eta_label.setText("剩余: 00:00:00")
        
        # 发送完成信号
        self.completed.emit()
        
        # 更新显示
        self.update_display()
    
    def set_status(self, status: str):
        """
        设置状态文本
        
        Args:
            status: 状态文本
        """
        self.status_label.setText(status)
    
    def set_title(self, title: str):
        """
        设置标题文本
        
        Args:
            title: 标题文本
        """
        self.title_label.setText(title)
    
    def calculate_total_progress(self) -> float:
        """
        计算总进度
        
        Returns:
            float: 总进度，0.0-1.0
        """
        if not self.stages:
            return 0.0
        
        total_weight = sum(stage.weight for stage in self.stages)
        weighted_progress = sum(stage.progress * stage.weight for stage in self.stages)
        
        return weighted_progress / total_weight if total_weight > 0 else 0.0
    
    def update_display(self):
        """更新显示"""
        # 更新时间标签
        if self.start_time:
            elapsed = time.time() - self.start_time
            hours, remainder = divmod(int(elapsed), 3600)
            minutes, seconds = divmod(remainder, 60)
            self.time_label.setText(f"时间: {hours:02d}:{minutes:02d}:{seconds:02d}")
        
        # 更新速度标签
        if hasattr(self, 'speed'):
            self.speed_label.setText(f"速度: {self.speed:.1f} 项/秒")
        
        # 更新剩余时间标签
        if not self.is_completed and 0 <= self.current_stage_index < len(self.stages):
            current_stage = self.stages[self.current_stage_index]
            remaining = current_stage.get_estimated_remaining_time()
            
            if remaining == float('inf'):
                self.eta_label.setText("剩余: --:--:--")
            else:
                hours, remainder = divmod(int(remaining), 3600)
                minutes, seconds = divmod(remainder, 60)
                self.eta_label.setText(f"剩余: {hours:02d}:{minutes:02d}:{seconds:02d}")
        
        # 如果是不确定进度，更新脉冲动画
        if self.is_indeterminate:
            self.progress_bar.setPulseVisible(True)
        
        # 更新进度条颜色
        if 0 <= self.current_stage_index < len(self.stages):
            current_stage = self.stages[self.current_stage_index]
            if current_stage.color:
                # 创建渐变色
                gradient_style = f"""
                    QProgressBar::chunk {{
                        background-color: qlineargradient(
                            x1:0, y1:0, x2:1, y2:0,
                            stop:0 {current_stage.color.name()}, 
                            stop:1 {QColor(current_stage.color.red(), 
                                          min(255, current_stage.color.green() + 50), 
                                          min(255, current_stage.color.blue() + 20)).name()}
                        );
                        border-radius: 5px;
                    }}
                """
                self.progress_bar.setStyleSheet(self.progress_bar.styleSheet() + gradient_style)
