#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
基于RectPack的完全统一布局工作器
完全使用RectPack算法，不再进行A/B/C分类，实现最优画布利用率

特性：
1. 完全移除A/B/C分类逻辑
2. 使用RectPack算法统一处理所有图片
3. 自动优化图片旋转和排列
4. 实现最大化画布利用率
5. 简化的处理流程
"""

import os
import time
import logging
from typing import List, Dict, Any
from PyQt6.QtCore import QThread, pyqtSignal, QMutex, QMutexLocker

# 设置日志
log = logging.getLogger(__name__)

# 导入核心模块
from core.excel_processor import ExcelProcessor
from core.image_indexer_duckdb import ImageIndexerDuckDB
from core.unified_image_arranger import UnifiedImageArranger

from utils.photoshop_helper import PhotoshopHelper
from utils.memory_manager import MemoryManager
from utils.image_processor import get_image_processor

# 配置日志
from utils.log_config import get_logger
log = get_logger("RectPackLayoutWorker")

class RectPackLayoutWorker(QThread):
    """
    基于RectPack的完全统一布局工作器

    完全使用RectPack算法，移除所有A/B/C分类逻辑
    """

    # 信号定义
    progress_signal = pyqtSignal(int)  # 进度信号 (0-100)
    log_signal = pyqtSignal(str)       # 日志信号
    error_signal = pyqtSignal(str)     # 错误信号
    finished_signal = pyqtSignal()     # 完成信号
    stage_signal = pyqtSignal(str, int, str)  # 阶段信号 (阶段名, 进度, 状态)
    new_canvas_needed = pyqtSignal(str, list)  # 新画布需求信号，参数为材质名称和剩余图案

    def __init__(self, config_manager, image_indexer: ImageIndexerDuckDB,
                 excel_processor: ExcelProcessor, parent=None):
        """
        初始化RectPack布局工作器

        Args:
            config_manager: 配置管理器
            image_indexer: 图片索引器
            excel_processor: Excel处理器
            parent: 父对象
        """
        super().__init__(parent)

        self.config_manager = config_manager
        self.image_indexer = image_indexer
        self.excel_processor = excel_processor

        # 工作参数
        self.library_path = ""
        self.material_folder_path = ""
        self.canvas_width_m = 2.0
        self.max_height_cm = 5000
        self.ppi = 72
        self.image_spacing_cm = 0.1
        self.horizontal_expansion_cm = 0

        # 控制标志
        self._stop_requested = False
        self._mutex = QMutex()

        # 核心组件
        self.unified_arranger = None

        # 性能监控
        self.memory_manager = MemoryManager()

        # 状态属性
        self.success = False  # 添加success属性
        self.successful_arrangements = 0
        self.failed_arrangements = 0
        self.start_time = None

        # 多画布支持属性
        self.canvas_name = ""
        self.material_name = ""
        self.output_path = ""
        self.canvas_sequence = 1  # 画布序号
        self.remaining_patterns = []  # 未处理完的图案

        # 图案数据
        self.pattern_items = []  # 从主应用程序接收的图案数据
        self.use_external_patterns = False  # 是否使用外部提供的图案数据

        # 图片处理器
        self.image_processor = None

    def set_parameters(self, library_path: str, material_folder_path: str,
                      canvas_width_m: float, max_height_cm: float, ppi: float,
                      image_spacing_cm: float, horizontal_expansion_cm: float = 0):
        """
        设置工作参数

        Args:
            library_path: 图库路径
            material_folder_path: 材质文件夹路径
            canvas_width_m: 画布宽度（米）
            max_height_cm: 最大高度（厘米）
            ppi: 每英寸像素数
            image_spacing_cm: 图片间距（厘米）
            horizontal_expansion_cm: 水平扩展（厘米）
        """
        with QMutexLocker(self._mutex):
            self.library_path = library_path
            self.material_folder_path = material_folder_path
            self.canvas_width_m = canvas_width_m
            self.max_height_cm = max_height_cm
            self.ppi = ppi
            self.image_spacing_cm = image_spacing_cm
            self.horizontal_expansion_cm = horizontal_expansion_cm

    def set_canvas_info(self, canvas_name: str, material_name: str, output_path: str, canvas_sequence: int = 1):
        """
        设置画布信息（用于多画布支持）

        Args:
            canvas_name: 画布名称
            material_name: 材质名称
            output_path: 输出路径
            canvas_sequence: 画布序号
        """
        with QMutexLocker(self._mutex):
            self.canvas_name = canvas_name
            self.material_name = material_name
            self.output_path = output_path
            self.canvas_sequence = canvas_sequence

    def set_pattern_items(self, pattern_items: List[Dict[str, Any]]):
        """
        设置图案数据（从主应用程序接收）

        Args:
            pattern_items: 图案数据列表
        """
        with QMutexLocker(self._mutex):
            self.pattern_items = pattern_items
            self.use_external_patterns = True
            self.log_signal.emit(f"接收到 {len(pattern_items)} 个图案数据")

    def request_stop(self):
        """请求停止工作"""
        with QMutexLocker(self._mutex):
            self._stop_requested = True
        self.log_signal.emit("收到停止请求...")

    def stop(self):
        """停止工作（兼容性方法）"""
        self.request_stop()

    def is_stop_requested(self) -> bool:
        """检查是否请求停止"""
        with QMutexLocker(self._mutex):
            return self._stop_requested

    def emit_stage(self, stage_name: str, progress: int, status: str):
        """发送阶段信号"""
        self.stage_signal.emit(stage_name, progress, status)
        self.log_signal.emit(f"[{stage_name}] {status}")

    def run(self):
        """主工作流程"""
        try:
            self.start_time = time.time()
            self._stop_requested = False
            self.success = False  # 初始化为失败状态

            self.log_signal.emit("=" * 60)
            self.log_signal.emit("开始RectPack统一布局处理")
            self.log_signal.emit("=" * 60)

            # 阶段1: 准备工作
            self.emit_stage("准备", 0, "初始化组件...")
            if not self._initialize_components():
                self.error_signal.emit("组件初始化失败")
                return

            if self.is_stop_requested():
                self.log_signal.emit("用户取消了操作")
                return

            # 阶段2: 获取图案数据
            if self.use_external_patterns and self.pattern_items:
                # 使用从主应用程序接收的图案数据
                self.emit_stage("检索", 10, "使用已提供的图案数据...")
                pattern_items = self.pattern_items
                self.log_signal.emit(f"使用已提供的 {len(pattern_items)} 个图案数据")
            else:
                # 从 Excel 文件处理图案数据
                self.emit_stage("检索", 10, "处理材质表格...")
                pattern_items = self._process_excel_files()
                if not pattern_items:
                    self.error_signal.emit("没有找到有效的图片数据，请检查Excel文件")
                    return

            if self.is_stop_requested():
                self.log_signal.emit("用户取消了操作")
                return

            # 阶段3: 检索图片（测试模式下跳过）
            self.emit_stage("检索", 30, "检索图片文件...")

            # 检查是否为测试模式
            test_mode_settings = self.config_manager.get_test_mode_settings()
            is_test_mode = test_mode_settings.get('is_test_mode', False)

            if is_test_mode:
                self.log_signal.emit("测试模式: 跳过图片检索，直接使用色块表示图片")
                retrieved_patterns = pattern_items  # 在测试模式下直接使用原始数据

                # 为测试模式添加颜色信息
                colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8', '#F7DC6F']
                for i, pattern in enumerate(retrieved_patterns):
                    pattern['color'] = colors[i % len(colors)]
                    pattern['test_mode'] = True
                    # 在测试模式下，设置一个虚拟的图片路径
                    if not pattern.get('path') or pattern.get('path') == '未入库':
                        pattern['path'] = f"test_mode_color_block_{i}.png"

                self.log_signal.emit(f"测试模式: 将使用 {len(retrieved_patterns)} 个色块进行排列")
            else:
                # 非测试模式下正常检索图片
                retrieved_patterns = self._retrieve_images(pattern_items)
                if not retrieved_patterns:
                    self.error_signal.emit("没有找到匹配的图片文件，请检查图库索引和图案名称")
                    return

                # 检查是否有足够的图片进行排列
                if len(retrieved_patterns) < len(pattern_items) * 0.1:  # 如果找到的图片少于10%
                    self.log_signal.emit(f"警告: 只找到 {len(retrieved_patterns)}/{len(pattern_items)} 个图片，成功率较低")
                    self.log_signal.emit("建议检查图案名称与图库文件名的匹配规则")

            if self.is_stop_requested():
                self.log_signal.emit("用户取消了操作")
                return

            # 阶段4: RectPack统一排列（跳过分类步骤）
            self.emit_stage("排列", 50, "使用RectPack算法统一排列所有图片...")
            arranged_images = self._arrange_images_unified(retrieved_patterns)
            if not arranged_images:
                self.error_signal.emit("图片排列失败，可能是图片尺寸过大或算法错误")
                return

            if self.is_stop_requested():
                self.log_signal.emit("用户取消了操作")
                return

            # 阶段5: 输出到Photoshop
            self.emit_stage("输出", 80, "创建Photoshop画布...")
            success = self._create_photoshop_canvas(arranged_images)
            if not success:
                self.error_signal.emit("Photoshop画布创建失败，请检查Photoshop是否正常运行")
                return

            # 检查是否有剩余图片需要新画布
            if self.remaining_patterns and len(self.remaining_patterns) > 0:
                self.log_signal.emit(f"有 {len(self.remaining_patterns)} 个图片未能放置，需要创建新画布")

                # 发送新画布需求信号
                if self.material_name:
                    self.new_canvas_needed.emit(self.material_name, self.remaining_patterns)
                else:
                    self.log_signal.emit("警告: 未设置材质名称，无法创建新画布")
            else:
                self.log_signal.emit("所有图片已成功放置")

            # 完成
            self.emit_stage("输出", 100, "处理完成")
            self._log_final_statistics()
            self.success = True  # 设置成功标志
            self.log_signal.emit("✅ RectPack算法处理成功完成！")

        except Exception as e:
            error_msg = f"RectPack布局处理发生严重错误: {str(e)}"
            self.error_signal.emit(error_msg)
            log.error(error_msg, exc_info=True)
            self.success = False  # 设置失败标志

            # 输出详细的错误信息
            self.log_signal.emit("❌ 发生严重错误，处理中断")
            self.log_signal.emit(f"错误类型: {type(e).__name__}")
            self.log_signal.emit(f"错误详情: {str(e)}")

        finally:
            # 确保始终发送完成信号
            self.finished_signal.emit()

    def _initialize_components(self) -> bool:
        """初始化组件"""
        try:
            # 初始化单位转换器
            from utils.unit_converter import get_default_converter, initialize_from_config
            self.unit_converter = get_default_converter()
            initialize_from_config(self.config_manager)

            # 计算画布参数，使用统一的单位转换器
            canvas_width_cm = self.canvas_width_m * 100 + self.horizontal_expansion_cm
            canvas_width_px = self.unit_converter.cm_to_px(canvas_width_cm)

            # 限制最大高度，避免过大的画布导致性能问题
            max_reasonable_height_cm = 5000  # 50米最大高度
            actual_max_height_cm = min(self.max_height_cm, max_reasonable_height_cm)
            max_height_px = self.unit_converter.cm_to_px(actual_max_height_cm)

            image_spacing_px = self.unit_converter.cm_to_px(self.image_spacing_cm)

            # 记录限制信息
            if self.max_height_cm > max_reasonable_height_cm:
                self.log_signal.emit(f"警告: 设置的最大高度 {self.max_height_cm}cm 超过合理范围，已限制为 {actual_max_height_cm}cm")

            # 初始化统一排列器
            self.unified_arranger = UnifiedImageArranger(log_signal=self.log_signal)
            self.unified_arranger.initialize(
                canvas_width_px=canvas_width_px,
                max_height_px=max_height_px,
                image_spacing_px=image_spacing_px,
                ppi=self.ppi
            )

            # 应用RectPack优化配置
            self._apply_rectpack_optimizations()

            # 初始化图片处理器，统一单位处理
            is_test_mode = False
            test_mode_config = {
                'is_test_all_data': False
            }

            try:
                if hasattr(self, 'config_manager') and self.config_manager:
                    test_mode_settings = self.config_manager.get_test_mode_settings()
                    is_test_mode = test_mode_settings.get('is_test_mode', False)
                    test_mode_config = {
                        'is_test_all_data': test_mode_settings.get('is_test_all_data', False)
                    }
                    self.log_signal.emit(f"测试模式设置: 启用={is_test_mode}, 测试全部数据={test_mode_config['is_test_all_data']}")
                else:
                    self.log_signal.emit("未设置配置管理器，使用默认测试模式设置")
            except Exception as e:
                self.log_signal.emit(f"获取测试模式设置失败: {str(e)}，使用默认设置")

            self.image_processor = get_image_processor(is_test_mode, test_mode_config)

            self.log_signal.emit(f"RectPack组件初始化完成")
            self.log_signal.emit(f"画布参数: 宽度={canvas_width_cm:.1f}cm ({canvas_width_px}px), 最大高度={actual_max_height_cm}cm ({max_height_px}px)")
            self.log_signal.emit(f"图片间距: {self.image_spacing_cm}cm ({image_spacing_px}px), PPI={self.ppi}")

            return True

        except Exception as e:
            self.error_signal.emit(f"组件初始化失败: {str(e)}")
            return False

    def _apply_rectpack_optimizations(self):
        """应用RectPack算法优化配置"""
        try:
            # 检查RectPack库可用性
            try:
                import rectpack
                self.log_signal.emit("✅ RectPack库检测成功")
            except ImportError as e:
                self.log_signal.emit(f"⚠️ RectPack库不可用: {str(e)}")
                return

            # 获取统一排列器中的RectPack排列器
            if hasattr(self.unified_arranger, 'rectpack_arranger'):
                arranger = self.unified_arranger.rectpack_arranger

                # 从配置管理器获取优化参数
                try:
                    rectpack_settings = self.config_manager.get_rectpack_settings()

                    # 应用生产环境优化配置
                    optimizations = {
                        # 基础算法参数 - 提升画布利用率
                        'rotation_enabled': rectpack_settings.get('rectpack_rotation_enabled', True),
                        'sort_key': rectpack_settings.get('rectpack_sort_strategy', 0),  # 面积排序
                        'pack_algo': rectpack_settings.get('rectpack_pack_algorithm', 0),  # Best Short Side Fit

                        # 高级算法参数 - 提升排列质量
                        'bin_selection_strategy': rectpack_settings.get('rectpack_bin_selection_strategy', 2),
                        'split_heuristic': rectpack_settings.get('rectpack_split_heuristic', 1),
                        'free_rect_choice': rectpack_settings.get('rectpack_free_rect_choice', 0),

                        # 优化参数 - 平衡性能和质量
                        'enable_optimization': rectpack_settings.get('rectpack_enable_optimization', True),
                        'optimization_iterations': rectpack_settings.get('rectpack_optimization_iterations', 3),
                        'min_utilization_threshold': rectpack_settings.get('rectpack_min_utilization_threshold', 80.0),
                        'rotation_penalty': rectpack_settings.get('rectpack_rotation_penalty', 0.02),
                        'aspect_ratio_preference': rectpack_settings.get('rectpack_aspect_ratio_preference', 1.2),

                        # 性能参数 - 确保稳定运行
                        'batch_size': rectpack_settings.get('rectpack_batch_size', 50),
                        'memory_limit_mb': rectpack_settings.get('rectpack_memory_limit_mb', 512),
                        'max_processing_time': rectpack_settings.get('rectpack_max_processing_time', 180),
                        'enable_parallel': rectpack_settings.get('rectpack_enable_parallel', False),

                        # 调试参数 - 生产环境设置
                        'debug_mode': rectpack_settings.get('rectpack_debug_mode', False),
                        'log_level': rectpack_settings.get('rectpack_log_level', 1),
                        'save_intermediate_results': rectpack_settings.get('rectpack_save_intermediate_results', False),
                        'visualization_enabled': rectpack_settings.get('rectpack_visualization_enabled', False),
                    }

                    # 应用优化配置到排列器
                    for param, value in optimizations.items():
                        if hasattr(arranger, param):
                            setattr(arranger, param, value)

                    # 重新加载参数以应用配置
                    if hasattr(arranger, 'reload_parameters'):
                        arranger.reload_parameters()

                    self.log_signal.emit("✅ RectPack算法优化配置已应用")
                    self.log_signal.emit(f"   - 旋转功能: {'启用' if optimizations['rotation_enabled'] else '禁用'}")
                    self.log_signal.emit(f"   - 优化迭代: {optimizations['optimization_iterations']}次")
                    self.log_signal.emit(f"   - 利用率阈值: {optimizations['min_utilization_threshold']}%")
                    self.log_signal.emit(f"   - 内存限制: {optimizations['memory_limit_mb']}MB")
                    self.log_signal.emit(f"   - 处理超时: {optimizations['max_processing_time']}秒")

                except Exception as e:
                    self.log_signal.emit(f"⚠️ 获取RectPack配置失败，使用默认值: {str(e)}")
                    # 应用默认优化配置
                    self._apply_default_rectpack_config(arranger)
            else:
                self.log_signal.emit("⚠️ 统一排列器中未找到RectPack排列器")

        except Exception as e:
            self.log_signal.emit(f"⚠️ RectPack优化配置应用失败: {str(e)}")
            # 不抛出异常，使用默认配置继续运行

    def _apply_default_rectpack_config(self, arranger):
        """应用默认的RectPack优化配置"""
        try:
            # 生产环境默认优化配置
            default_config = {
                'rotation_enabled': True,
                'sort_key': 0,  # 面积排序
                'pack_algo': 0,  # Best Short Side Fit
                'enable_optimization': True,
                'optimization_iterations': 3,
                'min_utilization_threshold': 80.0,
                'rotation_penalty': 0.02,
                'aspect_ratio_preference': 1.2,
                'batch_size': 50,
                'memory_limit_mb': 512,
                'max_processing_time': 180,
                'debug_mode': False,
                'log_level': 1,
            }

            for param, value in default_config.items():
                if hasattr(arranger, param):
                    setattr(arranger, param, value)

            self.log_signal.emit("✅ 已应用默认RectPack优化配置")

        except Exception as e:
            self.log_signal.emit(f"⚠️ 应用默认配置失败: {str(e)}")

    def _process_excel_files(self) -> List[Dict[str, Any]]:
        """处理Excel文件 - 使用与俄罗斯方块算法相同的数据提取逻辑"""
        try:
            if not os.path.exists(self.material_folder_path):
                self.error_signal.emit(f"材质文件夹不存在: {self.material_folder_path}")
                return []

            # 查找已检索的Excel文件（优先使用）
            indexed_files = [f for f in os.listdir(self.material_folder_path)
                           if f.endswith('_已检索.xlsx')]

            # 如果没有已检索的文件，查找原始Excel文件
            if not indexed_files:
                excel_files = [f for f in os.listdir(self.material_folder_path)
                              if f.lower().endswith(('.xlsx', '.xls')) and not f.endswith('_已检索.xlsx')]
                if not excel_files:
                    self.error_signal.emit("材质文件夹中没有找到Excel文件")
                    return []
                self.log_signal.emit(f"找到 {len(excel_files)} 个原始Excel文件，将直接处理")
                files_to_process = excel_files
            else:
                self.log_signal.emit(f"找到 {len(indexed_files)} 个已检索的Excel文件，优先使用")
                files_to_process = indexed_files

            all_patterns = []

            # 获取配置参数
            exact_pattern_search = False
            is_standard_mode = True
            is_fuzzy_query = False

            try:
                if hasattr(self, 'config_manager') and self.config_manager:
                    exact_pattern_search = self.config_manager.get('exact_pattern_search', False)
                    is_standard_mode = self.config_manager.get('is_standard_mode', True)
                    is_fuzzy_query = self.config_manager.get('is_fuzzy_query', False)
                    self.log_signal.emit(f"Excel处理参数: 精确查询={exact_pattern_search}, 标准模式={is_standard_mode}, 模糊查询={is_fuzzy_query}")
            except Exception as e:
                self.log_signal.emit(f"获取Excel处理参数失败: {str(e)}，使用默认参数")

            for excel_file in files_to_process:
                if self.is_stop_requested():
                    break

                excel_path = os.path.join(self.material_folder_path, excel_file)
                self.log_signal.emit(f"处理Excel文件: {excel_file}")

                try:
                    # 使用与俄罗斯方块算法完全相同的Excel处理器
                    # 无论是已检索文件还是原始文件，都使用统一的处理逻辑
                    self.log_signal.emit(f"开始处理Excel文件: {excel_file}")

                    # 先检查Excel文件的基本信息
                    try:
                        import pandas as pd
                        xls = pd.ExcelFile(excel_path)
                        self.log_signal.emit(f"Excel文件包含 {len(xls.sheet_names)} 个工作表: {', '.join(xls.sheet_names)}")

                        # 检查第一个工作表的列名
                        if xls.sheet_names:
                            first_sheet = xls.sheet_names[0]
                            df_sample = pd.read_excel(excel_path, sheet_name=first_sheet, nrows=0)  # 只读取表头
                            self.log_signal.emit(f"工作表 '{first_sheet}' 的原始列名: {list(df_sample.columns)}")
                    except Exception as e:
                        self.log_signal.emit(f"检查Excel文件信息时发生错误: {str(e)}")

                    patterns = self.excel_processor.process_excel_file(
                        excel_path,
                        image_indexer=self.image_indexer,
                        exact_pattern_search=exact_pattern_search,
                        is_standard_mode=is_standard_mode,
                        is_fuzzy_query=is_fuzzy_query
                    )

                    if patterns:
                        all_patterns.extend(patterns)
                        self.log_signal.emit(f"✅ 从 {excel_file} 提取了 {len(patterns)} 个图案")

                        # 显示前几个图案的信息作为调试
                        for i, pattern in enumerate(patterns[:3]):
                            self.log_signal.emit(f"  图案 {i+1}: {pattern.get('pattern_name', '未知')} - "
                                               f"{pattern.get('width_cm', 0)}x{pattern.get('height_cm', 0)}cm")
                    else:
                        self.log_signal.emit(f"❌ 警告: 从 {excel_file} 未提取到任何图案")

                        # 尝试直接读取Excel文件来诊断问题
                        try:
                            import pandas as pd
                            xls = pd.ExcelFile(excel_path)
                            self.log_signal.emit(f"诊断信息: Excel文件包含 {len(xls.sheet_names)} 个工作表")

                            for sheet_name in xls.sheet_names:
                                df = pd.read_excel(excel_path, sheet_name=sheet_name, nrows=1)
                                self.log_signal.emit(f"  工作表 '{sheet_name}': {len(df.columns)} 列 - {list(df.columns)}")

                                # 检查是否有数据
                                total_rows = len(pd.read_excel(excel_path, sheet_name=sheet_name))
                                self.log_signal.emit(f"    数据行数: {total_rows}")

                        except Exception as diag_e:
                            self.log_signal.emit(f"诊断 Excel文件时发生错误: {str(diag_e)}")

                except Exception as e:
                    self.log_signal.emit(f"处理Excel文件 {excel_file} 时发生错误: {str(e)}")
                    log.error(f"处理Excel文件 {excel_file} 时发生错误: {str(e)}", exc_info=True)
                    continue

            self.log_signal.emit(f"总共提取了 {len(all_patterns)} 个图案")

            # 添加详细的调试信息
            if len(all_patterns) == 0:
                self.log_signal.emit("❌ 警告: 未提取到任何图案数据")
                self.log_signal.emit("")
                self.log_signal.emit("🔍 请检查以下问题:")
                self.log_signal.emit("1. Excel文件是否包含有效数据")
                self.log_signal.emit("2. 表格列名是否正确（需要: 图案/图案全称、宽度、高度、数量）")
                self.log_signal.emit("3. 数据行是否包含有效的尺寸和图案信息")
                self.log_signal.emit("4. 如果使用已检索文件，请检查文件是否完整")

                # 列出Excel文件信息
                self.log_signal.emit("")
                self.log_signal.emit("📄 Excel文件信息:")
                for excel_file in files_to_process:
                    excel_path = os.path.join(self.material_folder_path, excel_file)
                    try:
                        import pandas as pd
                        xls = pd.ExcelFile(excel_path)
                        self.log_signal.emit(f"  {excel_file}: {len(xls.sheet_names)} 个工作表 ({', '.join(xls.sheet_names[:3])}{'...' if len(xls.sheet_names) > 3 else ''})")
                    except Exception as e:
                        self.log_signal.emit(f"  {excel_file}: 读取失败 - {str(e)}")

            return all_patterns

        except Exception as e:
            error_msg = f"处理Excel文件时发生严重错误: {str(e)}"
            self.error_signal.emit(error_msg)
            log.error(error_msg, exc_info=True)
            return []



    def _retrieve_images(self, pattern_items: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """检索图片文件 - 使用与俄罗斯方块算法相同的检索逻辑"""
        try:
            # 检查图片索引器是否可用
            if not self.image_indexer:
                self.error_signal.emit("图片索引器未初始化")
                return []

            # 检查图库是否已索引
            if not self.image_indexer.is_indexed(self.library_path):
                self.error_signal.emit("图库尚未索引，请先索引图库")
                return []

            retrieved_patterns = []
            total_patterns = len(pattern_items)
            found_count = 0
            not_found_count = 0
            error_count = 0
            skipped_count = 0

            self.log_signal.emit(f"开始检索 {total_patterns} 个图案的图片文件...")
            self.log_signal.emit(f"图库路径: {self.library_path}")

            # 获取测试模式设置
            test_mode_settings = self.config_manager.get_test_mode_settings()
            is_test_mode = test_mode_settings.get('is_test_mode', False)
            is_test_all_data = test_mode_settings.get('is_test_all_data', False)

            # 减少批量大小以避免过多日志输出
            batch_size = 50
            for batch_start in range(0, total_patterns, batch_size):
                if self.is_stop_requested():
                    break

                batch_end = min(batch_start + batch_size, total_patterns)
                batch_patterns = pattern_items[batch_start:batch_end]

                # 只在处理大量数据时显示批次信息
                if total_patterns > 100:
                    self.log_signal.emit(f"处理第 {batch_start//batch_size + 1} 批次（{batch_start+1}-{batch_end}/{total_patterns}）")

                for i, pattern in enumerate(batch_patterns):
                    actual_index = batch_start + i

                    if self.is_stop_requested():
                        break

                    pattern_name = pattern.get('pattern_name', '')
                    existing_path = pattern.get('path', '')

                    # 跳过无效的图案名称
                    if not pattern_name or pattern_name == '未命名图案':
                        skipped_count += 1
                        continue

                    try:
                        # 检查是否已经有有效的图片路径
                        if existing_path and existing_path != '未入库':
                            if os.path.exists(existing_path):
                                retrieved_patterns.append(pattern)
                                found_count += 1
                                continue
                            else:
                                self.log_signal.emit(f"图片路径无效，重新查找: {pattern_name} -> {existing_path}")

                        # 在测试模式下，如果开启了测试全部数据，则跳过"未入库"的图片
                        if is_test_mode and not is_test_all_data and existing_path == '未入库':
                            skipped_count += 1
                            continue

                        # 使用图片索引器查找图片
                        image_path = None
                        try:
                            # 首先尝试精确匹配
                            image_path = self.image_indexer.find_image(pattern_name, exact_match=True)

                            # 如果精确匹配失败，尝试模糊匹配
                            if not image_path:
                                image_path = self.image_indexer.find_image(pattern_name, exact_match=False)

                        except Exception as e:
                            # 捕获数据库连接错误等问题
                            if "database" in str(e).lower() or "connection" in str(e).lower():
                                self.log_signal.emit(f"数据库连接问题，尝试重新连接: {str(e)}")
                                try:
                                    # 尝试重新连接数据库
                                    if hasattr(self.image_indexer, '_reconnect_database'):
                                        self.image_indexer._reconnect_database()
                                        # 重试查找
                                        image_path = self.image_indexer.find_image(pattern_name, exact_match=True)
                                except Exception as retry_e:
                                    self.log_signal.emit(f"重新连接失败: {str(retry_e)}")
                                    error_count += 1
                                    continue
                            else:
                                self.log_signal.emit(f"查找图片时发生错误 {pattern_name}: {str(e)}")
                                log.error(f"查找图片时发生错误 {pattern_name}: {str(e)}", exc_info=True)
                                error_count += 1
                                continue

                        if image_path and os.path.exists(image_path):
                            pattern['path'] = image_path
                            retrieved_patterns.append(pattern)
                            found_count += 1
                            # 只显示前10个找到的图片，避免日志过多
                            if found_count <= 10:
                                self.log_signal.emit(f"✓ 找到图片: {pattern_name} -> {os.path.basename(image_path)}")
                        else:
                            # 只记录前20个未找到的图片，避免日志过多
                            if not_found_count < 20:
                                self.log_signal.emit(f"未找到图片: {pattern_name}")
                            not_found_count += 1

                    except Exception as e:
                        self.log_signal.emit(f"处理图案时发生错误 {pattern_name}: {str(e)}")
                        log.error(f"处理图案时发生错误 {pattern_name}: {str(e)}", exc_info=True)
                        error_count += 1

                    # 更新进度
                    progress = int((actual_index + 1) / total_patterns * 20) + 30  # 30-50%
                    self.progress_signal.emit(progress)

                # 每批次处理后输出进度（仅在处理大量数据时）
                if total_patterns > 100:
                    self.log_signal.emit(f"第 {batch_start//batch_size + 1} 批次完成，已找到 {found_count} 个图片")

            # 输出详细统计信息
            self.log_signal.emit("=" * 50)
            self.log_signal.emit(f"RectPack图片检索统计:")
            self.log_signal.emit(f"  总图案数: {total_patterns}")
            self.log_signal.emit(f"  找到图片: {found_count} 个")
            self.log_signal.emit(f"  未找到: {not_found_count} 个")
            self.log_signal.emit(f"  跳过: {skipped_count} 个")
            self.log_signal.emit(f"  错误: {error_count} 个")
            self.log_signal.emit(f"  可用于排列: {len(retrieved_patterns)} 个")
            self.log_signal.emit("=" * 50)

            # 如果没有找到任何图片，提供详细的诊断信息
            if len(retrieved_patterns) == 0:
                self.log_signal.emit("❌ 警告: 没有找到任何可用的图片文件")
                self.log_signal.emit("")
                self.log_signal.emit("🔍 请检查以下问题:")
                self.log_signal.emit("1. 图库是否已正确索引")
                self.log_signal.emit("2. Excel文件中的图案名称是否与图库中的文件名匹配")
                self.log_signal.emit("3. 图库路径是否正确")
                self.log_signal.emit("4. 图片文件是否存在于图库目录中")

                # 显示一些示例图案名称
                if pattern_items:
                    self.log_signal.emit("")
                    self.log_signal.emit("📋 Excel中的图案名称示例:")
                    for i, pattern in enumerate(pattern_items[:5]):
                        pattern_name = pattern.get('pattern_name', '未知')
                        self.log_signal.emit(f"  {i+1}. {pattern_name}")
                    if len(pattern_items) > 5:
                        self.log_signal.emit(f"  ... 还有 {len(pattern_items) - 5} 个图案")

                # 尝试显示图库中的一些文件名
                try:
                    if self.image_indexer and self.image_indexer.db:
                        sample_files = self.image_indexer.db.execute(
                            "SELECT name_without_ext FROM image_files LIMIT 5"
                        ).fetchall()
                        if sample_files:
                            self.log_signal.emit("")
                            self.log_signal.emit("📁 图库中的文件名示例:")
                            for i, (filename,) in enumerate(sample_files):
                                self.log_signal.emit(f"  {i+1}. {filename}")
                        else:
                            self.log_signal.emit("")
                            self.log_signal.emit("⚠️ 图库索引为空，请重新索引图库")
                except Exception as e:
                    self.log_signal.emit(f"无法获取图库文件示例: {str(e)}")

            # 如果未找到的图片过多，给出额外提示
            if not_found_count > 20:
                self.log_signal.emit(f"")
                self.log_signal.emit(f"⚠️ 注意: 还有 {not_found_count - 20} 个图片未找到（未全部显示）")
                self.log_signal.emit(f"建议检查图案名称与图库文件名的匹配规则")

            return retrieved_patterns

        except Exception as e:
            error_msg = f"检索图片时发生严重错误: {str(e)}"
            self.error_signal.emit(error_msg)
            log.error(error_msg, exc_info=True)
            return []

    def _arrange_images_unified(self, pattern_items: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """使用RectPack算法统一排列所有图片（不进行分类）"""
        try:
            self.log_signal.emit("=" * 50)
            self.log_signal.emit("开始RectPack统一排列算法")
            self.log_signal.emit("=" * 50)
            self.log_signal.emit(f"待排列图片总数: {len(pattern_items)}")
            self.log_signal.emit("注意: 使用RectPack算法，不再进行A/B/C分类")

            # 为所有图片添加唯一标识符
            for i, pattern in enumerate(pattern_items):
                if 'unique_id' not in pattern:
                    width_cm = pattern.get('width_cm', 0)
                    height_cm = pattern.get('height_cm', 0)
                    pattern_name = pattern.get('pattern_name', f'图片{i+1}')
                    pattern['unique_id'] = f"{pattern_name}_{width_cm:.1f}_{height_cm:.1f}_{i}"
                    pattern['index'] = i
                    pattern['row_number'] = i + 1

            # 检查统一排列器是否已初始化
            if not hasattr(self, 'unified_arranger') or self.unified_arranger is None:
                self.error_signal.emit("统一排列器未初始化")
                return []

            # 使用统一排列器排列所有图片
            arranged_images = self.unified_arranger.arrange_images(pattern_items)

            # 尝试优化布局
            if arranged_images:
                self.log_signal.emit("尝试优化布局以提高画布利用率...")
                self.unified_arranger.optimize_layout()

            # 收集未排列的图片（用于多画布支持）
            remaining_patterns = self._collect_remaining_patterns(pattern_items, arranged_images)

            # 获取布局统计信息
            stats = self.unified_arranger.get_layout_statistics()
            if stats:
                self.log_signal.emit("=" * 50)
                self.log_signal.emit("RectPack排列统计")
                self.log_signal.emit("=" * 50)
                self.log_signal.emit(f"成功排列: {len(arranged_images)}/{len(pattern_items)} 个图片")
                self.log_signal.emit(f"画布利用率: {stats.get('utilization_percent', 0):.2f}%")
                self.log_signal.emit(f"画布尺寸: {stats.get('container_width', 0)}x{stats.get('container_height', 0)}px")
                self.log_signal.emit("=" * 50)

            self.successful_arrangements = len(arranged_images)
            self.failed_arrangements = len(pattern_items) - len(arranged_images)

            # 存储剩余图案信息，用于后续处理
            self.remaining_patterns = remaining_patterns

            return arranged_images

        except Exception as e:
            self.error_signal.emit(f"RectPack统一排列时发生错误: {str(e)}")
            return []

    def _collect_remaining_patterns(self, original_patterns: List[Dict[str, Any]],
                                  arranged_images: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        收集未排列的图案（用于多画布支持）

        Args:
            original_patterns: 原始图案列表
            arranged_images: 已排列的图片列表

        Returns:
            List[Dict[str, Any]]: 未排列的图案列表
        """
        try:
            # 获取已排列图片的unique_id集合
            arranged_ids = set()
            for img in arranged_images:
                unique_id = img.get('unique_id')
                if unique_id:
                    arranged_ids.add(unique_id)

            # 找出未排列的图案
            remaining_patterns = []
            for pattern in original_patterns:
                unique_id = pattern.get('unique_id')
                if unique_id and unique_id not in arranged_ids:
                    remaining_patterns.append(pattern)

            if remaining_patterns:
                self.log_signal.emit(f"收集到 {len(remaining_patterns)} 个未排列的图案，将在新画布继续排列")

                # 显示前几个未排列的图案
                for i, pattern in enumerate(remaining_patterns[:5]):
                    pattern_name = pattern.get('pattern_name', '未知')
                    width_cm = pattern.get('width_cm', 0)
                    height_cm = pattern.get('height_cm', 0)
                    self.log_signal.emit(f"  未排列 {i+1}: {pattern_name} - {width_cm}x{height_cm}cm")

                if len(remaining_patterns) > 5:
                    self.log_signal.emit(f"  ... 还有 {len(remaining_patterns) - 5} 个图案未显示")

            return remaining_patterns

        except Exception as e:
            self.log_signal.emit(f"收集剩余图案时发生错误: {str(e)}")
            return []

    def _create_photoshop_canvas(self, arranged_images: List[Dict[str, Any]]) -> bool:
        """创建Photoshop画布"""
        try:
            # 检查是否处于测试模式，与俄罗斯方块算法保持一致
            is_test_mode = False
            try:
                if hasattr(self, 'config_manager') and self.config_manager:
                    test_mode_settings = self.config_manager.get_test_mode_settings()
                    is_test_mode = test_mode_settings.get('is_test_mode', False)
                else:
                    self.log_signal.emit("未设置配置管理器，默认为非测试模式")
            except Exception as e:
                self.log_signal.emit(f"获取测试模式设置失败: {str(e)}，默认为非测试模式")

            if is_test_mode:
                self.log_signal.emit("测试模式: 跳过Photoshop画布创建，生成JPG图片和说明文档")

                # 参照Tetris算法，只使用RectPack算法的测试模式支持生成JPG和说明文档
                success = self._create_test_mode_canvas_with_rectpack(arranged_images)

                if success:
                    self.log_signal.emit("✅ RectPack测试模式处理完成")
                else:
                    self.log_signal.emit("❌ RectPack测试模式处理失败")

                return success

            # 检查Photoshop连接
            success, message = PhotoshopHelper.check_photoshop()
            if not success:
                self.error_signal.emit(f"Photoshop未运行: {message}")
                return False

            # 获取画布尺寸
            stats = self.unified_arranger.get_layout_statistics()
            canvas_width_px = stats.get('container_width', 0)
            canvas_height_px = stats.get('container_height', 0)

            if canvas_width_px <= 0 or canvas_height_px <= 0:
                self.error_signal.emit("无效的画布尺寸")
                return False

            # 创建Photoshop文档
            self.log_signal.emit(f"创建Photoshop画布: {canvas_width_px}x{canvas_height_px}px")

            # 使用image_processor架构 - 完全参照tetris算法，增强错误处理
            # 第一步：创建画布，增加重试机制
            canvas_name = f"{self.canvas_name}_{self.canvas_sequence}"
            canvas_created = False
            retry_count = 3

            for attempt in range(retry_count):
                try:
                    canvas_created = self.image_processor.create_canvas(
                        width=canvas_width_px,
                        height=canvas_height_px,
                        name=canvas_name,
                        ppi=self.ppi
                    )
                    if canvas_created:
                        break
                    else:
                        if attempt < retry_count - 1:
                            self.log_signal.emit(f"画布创建失败，重试中... ({attempt + 1}/{retry_count})")
                            import time
                            time.sleep(2)  # 等待2秒后重试
                except Exception as e:
                    if attempt < retry_count - 1:
                        self.log_signal.emit(f"画布创建异常，重试中... ({attempt + 1}/{retry_count}): {str(e)}")
                        import time
                        time.sleep(2)
                    else:
                        self.error_signal.emit(f"画布创建失败（重试{retry_count}次后）: {str(e)}")
                        return False

            if not canvas_created:
                self.error_signal.emit("创建画布失败，请检查Photoshop状态")
                return False

            self.log_signal.emit(f"✅ 画布创建成功: {canvas_name} ({canvas_width_px}x{canvas_height_px}px)")

            # 第二步：放置所有图片，增强错误处理和性能监控
            self.log_signal.emit(f"开始放置 {len(arranged_images)} 张图片...")

            total_images = len(arranged_images)
            images_placed = 0
            failed_images = []
            consecutive_failures = 0
            max_consecutive_failures = 5  # 连续失败阈值

            # 内存监控
            try:
                self.memory_manager.log_memory_usage("开始放置图片")
            except Exception as e:
                self.log_signal.emit(f"内存监控记录失败: {str(e)}")

            # 遍历所有图片并放置 - 完全参照tetris算法，增强错误处理
            for i, image_info in enumerate(arranged_images):
                # 检查是否取消任务
                if self._stop_requested:
                    self.log_signal.emit("布局任务已取消")
                    return False

                # 检查连续失败次数
                if consecutive_failures >= max_consecutive_failures:
                    self.error_signal.emit(f"连续{consecutive_failures}次图片放置失败，可能存在系统问题")
                    return False

                # 准备图片信息 - 完全参照tetris算法的数据结构
                image_path = image_info.get('path', '')
                x = image_info.get('x', 0)
                y = image_info.get('y', 0)
                width = image_info.get('width', 0)
                height = image_info.get('height', 0)
                need_rotation = image_info.get('need_rotation', False) or image_info.get('rotated', False)
                image_name = image_info.get('name', os.path.basename(image_path))
                image_class = image_info.get('image_class', 'C')  # RectPack默认为C类

                # 创建完整的图片信息字典 - 与tetris算法保持一致
                place_info = {
                    'image_path': image_path,
                    'x': x,
                    'y': y,
                    'width': width,
                    'height': height,
                    'rotated': need_rotation,
                    'name': image_name,
                    'image_class': image_class
                }

                # 使用图片处理器放置图片 - 完全参照tetris算法，增强错误处理
                success = False
                retry_count = 2  # 每个图片最多重试2次

                for attempt in range(retry_count):
                    try:
                        success = self.image_processor.place_image(place_info)
                        if success:
                            break
                        elif attempt < retry_count - 1:
                            self.log_signal.emit(f"图片放置失败，重试中: {image_name} ({attempt + 1}/{retry_count})")
                            import time
                            time.sleep(0.5)  # 短暂等待
                    except Exception as e:
                        if attempt < retry_count - 1:
                            self.log_signal.emit(f"图片放置异常，重试中: {image_name} - {str(e)}")
                            import time
                            time.sleep(0.5)
                        else:
                            self.log_signal.emit(f"⚠️ 图片放置失败（重试后）: {image_name} - {str(e)}")

                if success:
                    images_placed += 1
                    consecutive_failures = 0  # 重置连续失败计数

                    # 只显示前10个和最后10个成功放置的图片，减少日志输出
                    if images_placed <= 10 or images_placed > total_images - 10:
                        self.log_signal.emit(f"✅ 放置成功: {image_name} at ({x},{y})")
                else:
                    failed_images.append(image_name)
                    consecutive_failures += 1
                    self.log_signal.emit(f"⚠️ 放置图片 {image_name} 失败")

                # 更新进度 - 每20个图片更新一次进度，减少UI更新频率
                if i % 20 == 0 or i == len(arranged_images) - 1:
                    progress = int((images_placed / total_images) * 80)  # 80%用于放置图片
                    self.progress_signal.emit(progress)
                    self.log_signal.emit(f"📊 已放置: {images_placed}/{total_images} 张图片 (失败: {len(failed_images)})")

                # 每50个图片进行一次内存检查
                if i % 50 == 0 and i > 0:
                    try:
                        self.memory_manager.log_memory_usage(f"已放置{images_placed}张图片")
                    except Exception as e:
                        self.log_signal.emit(f"内存监控记录失败: {str(e)}")

            # 第三步：保存画布并关闭以节省内存 - 完全参照tetris算法，增强错误处理
            self.log_signal.emit("保存画布并关闭以节省内存...")

            # 内存监控
            try:
                self.memory_manager.log_memory_usage("保存画布前")
            except Exception as e:
                self.log_signal.emit(f"内存监控记录失败: {str(e)}")

            # 使用图片处理器保存画布（内部会自动关闭画布），增加重试机制
            save_success = False
            save_retry_count = 3

            for attempt in range(save_retry_count):
                try:
                    save_success = self.image_processor.save_canvas(self.output_path)
                    if save_success:
                        break
                    else:
                        if attempt < save_retry_count - 1:
                            self.log_signal.emit(f"画布保存失败，重试中... ({attempt + 1}/{save_retry_count})")
                            import time
                            time.sleep(3)  # 等待3秒后重试
                except Exception as e:
                    if attempt < save_retry_count - 1:
                        self.log_signal.emit(f"画布保存异常，重试中... ({attempt + 1}/{save_retry_count}): {str(e)}")
                        import time
                        time.sleep(3)
                    else:
                        self.error_signal.emit(f"画布保存失败（重试{save_retry_count}次后）: {str(e)}")
                        return False

            if not save_success:
                self.error_signal.emit("保存画布失败，请检查输出路径和Photoshop状态")
                return False

            self.log_signal.emit(f"画布保存成功并已关闭: {self.output_path}")

            # 第四步：生成RectPack专用TIFF说明文档 - 完全参照tetris算法
            self.log_signal.emit("生成RectPack算法TIFF说明文档...")

            # 准备画布信息 - 完全参照tetris算法的数据结构
            canvas_info = {
                'canvas_name': canvas_name,
                'material_name': self.material_name,
                'canvas_sequence': self.canvas_sequence,
                'canvas_width_m': self.canvas_width_m,
                'canvas_width_px': canvas_width_px,
                'canvas_height': canvas_height_px,
                'horizontal_expansion_cm': self.horizontal_expansion_cm,
                'max_height_cm': self.max_height_cm,
                'ppi': self.ppi,
                'generation_time': time.strftime('%Y-%m-%d %H:%M:%S')
            }

            # 使用图片处理器生成说明文档
            desc_success = self.image_processor.generate_description(
                output_path=self.output_path,
                images_info=arranged_images,
                canvas_info=canvas_info
            )

            if not desc_success:
                self.log_signal.emit("⚠️ 生成RectPack算法TIFF说明文档失败")
                # 文档生成失败不应该导致整个流程失败
            else:
                self.log_signal.emit("✅ RectPack算法TIFF说明文档生成成功")

            # 第五步：关闭画布 - 完全参照tetris算法
            self.log_signal.emit("关闭画布...")
            self.image_processor.close_canvas()

            # 第六步：清理资源 - 完全参照tetris算法
            self.image_processor.cleanup()

            # 更新进度到100%
            self.progress_signal.emit(100)

            # 显示最终统计
            success_rate = (images_placed / total_images * 100) if total_images > 0 else 0
            self.log_signal.emit(f"✅ RectPack布局完成: {images_placed}/{total_images} 张成功 ({success_rate:.1f}%)")

            if failed_images:
                self.log_signal.emit(f"⚠️ 失败图片: {', '.join(failed_images[:5])}{'...' if len(failed_images) > 5 else ''}")

            return True

        except Exception as e:
            # 使用RectPack恢复机制处理错误
            try:
                from utils.rectpack_recovery import get_recovery_manager
                recovery_manager = get_recovery_manager(self.log_signal.emit)
                strategy = recovery_manager.handle_error(e, "photoshop_canvas_creation")

                self.error_signal.emit(f"RectPack错误: 创建Photoshop画布时发生错误: {str(e)}")

                # 根据恢复策略决定是否重试
                if strategy.value == "retry":
                    self.log_signal.emit("🔄 尝试恢复并重试...")
                    # 这里可以添加重试逻辑
                elif strategy.value == "fallback":
                    self.log_signal.emit("⬇️ 使用降级策略...")
                    # 这里可以添加降级逻辑，比如使用测试模式

            except Exception as recovery_error:
                self.log_signal.emit(f"恢复机制失败: {str(recovery_error)}")

            self.error_signal.emit("RectPack错误: Photoshop画布创建失败，请检查Photoshop是否正常运行")
            return False

    def _log_final_statistics(self):
        """记录最终统计信息"""
        try:
            elapsed_time = time.time() - self.start_time if self.start_time else 0

            self.log_signal.emit("=" * 60)
            self.log_signal.emit("RectPack处理完成统计")
            self.log_signal.emit("=" * 60)
            self.log_signal.emit(f"总处理时间: {elapsed_time:.2f}秒")
            self.log_signal.emit(f"成功排列: {self.successful_arrangements} 个图片")
            self.log_signal.emit(f"排列失败: {self.failed_arrangements} 个图片")

            if elapsed_time > 0:
                speed = self.successful_arrangements / elapsed_time
                self.log_signal.emit(f"平均速度: {speed:.2f} 图片/秒")

            # 获取最终布局统计
            stats = self.unified_arranger.get_layout_statistics()
            if stats:
                self.log_signal.emit(f"最终画布利用率: {stats.get('utilization_percent', 0):.2f}%")
                self.log_signal.emit(f"最终画布尺寸: {stats.get('container_width', 0)}x{stats.get('container_height', 0)}px")

            self.log_signal.emit("🎉 RectPack算法处理完成！")
            self.log_signal.emit("✓ 实现了最优的画布空间利用率")
            self.log_signal.emit("✓ 简化了图片排列流程")
            self.log_signal.emit("✓ 移除了复杂的A/B/C分类逻辑")
            self.log_signal.emit("=" * 60)

        except Exception as e:
            log.error(f"记录统计信息时发生错误: {str(e)}")

    def _create_test_mode_canvas_with_rectpack(self, arranged_images: List[Dict[str, Any]]) -> bool:
        """
        使用新的RectPack测试模式创建画布和生成文档，完全按照test_rectpack_real_data.py标准

        Args:
            arranged_images: 排列好的图片列表

        Returns:
            bool: 是否成功
        """
        try:
            # 获取测试模式设置，统一单位处理
            test_mode_settings = self.config_manager.get_test_mode_settings()

            # 获取画布配置
            stats = self.unified_arranger.get_layout_statistics()
            canvas_width_px = stats.get('container_width', 0)
            canvas_height_px = stats.get('container_height', 0)

            if canvas_width_px <= 0 or canvas_height_px <= 0:
                self.log_signal.emit("无效的画布尺寸，无法创建 RectPack 测试模式画布")
                return False

            # 从配置中读取测试模式参数，确保遵循容器最大高度限制
            canvas_settings = self.config_manager.get_canvas_settings()

            # 获取配置中的最大高度，确保测试模式遵循容器最大高度限制
            max_height_cm_from_config = canvas_settings.get('max_height_cm', self.max_height_cm)

            # 从配置中读取其他参数
            image_spacing_cm_from_config = canvas_settings.get('image_spacing_cm', 0.1)
            horizontal_expansion_cm_from_config = canvas_settings.get('horizontal_expansion_cm', 2)

            # 计算画布尺寸（使用统一单位转换器）
            canvas_width_cm = self.canvas_width_m * 100
            canvas_height_cm = max_height_cm_from_config

            self.log_signal.emit(f"测试模式配置: 最大高度={canvas_height_cm}cm, 图片间距={image_spacing_cm_from_config}cm, 水平拓展={horizontal_expansion_cm_from_config}cm")
            self.log_signal.emit(f"统一单位处理: 测试模式最大高度不超过{canvas_height_cm}px")

            # 使用新的RectPack测试模式
            from core.rectpack_test_mode import run_rectpack_test_mode

            # 将arranged_images转换为pattern_items格式
            pattern_items = []
            for i, img in enumerate(arranged_images):
                # 从图片信息中提取尺寸，使用统一单位转换器
                width_cm = img.get('width_cm', self.unit_converter.px_to_cm(img.get('width', 100)))
                height_cm = img.get('height_cm', self.unit_converter.px_to_cm(img.get('height', 100)))
                pattern_name = img.get('name', f'Image_{i+1}')

                pattern_items.append({
                    'width_cm': width_cm,
                    'height_cm': height_cm,
                    'pattern_name': pattern_name,
                    'quantity': 1
                })

            self.log_signal.emit(f"开始RectPack新测试模式处理: {len(pattern_items)} 张图片")

            # 设置输出目录
            output_dir = self.output_path or "output"
            canvas_name = self.canvas_name or f"rectpack_canvas_{self.canvas_sequence}"
            test_output_dir = os.path.join(output_dir, f"{canvas_name}_rectpack_test")

            # 运行新的RectPack测试模式，统一单位处理
            result = run_rectpack_test_mode(
                pattern_items=pattern_items,
                canvas_width_cm=canvas_width_cm,
                horizontal_expansion_cm=horizontal_expansion_cm_from_config,
                max_height_cm=canvas_height_cm,
                image_spacing_cm=image_spacing_cm_from_config,
                output_dir=test_output_dir,
                material_name=canvas_name
            )

            if result['success']:
                self.log_signal.emit("✅ RectPack 新测试模式处理完成！")
                self.log_signal.emit(f"📊 容器数量: {result['total_containers']}")
                self.log_signal.emit(f"🎨 图片总数: {result['total_images']}")
                self.log_signal.emit(f"📈 平均利用率: {result['avg_utilization_rate']:.2f}%")
                self.log_signal.emit(f"⚡ 处理速度: {result['processing_speed']:.1f} 图片/秒")
                self.log_signal.emit(f"🖼️ 可视化文件: {len(result['visualization_files'])}个")
                self.log_signal.emit(f"📄 文档文件: {len(result['documentation_files'])}个")
                self.log_signal.emit(f"📁 输出目录: {result['output_dir']}")

                # 更新进度到100%
                self.progress_signal.emit(100)

                return True
            else:
                self.log_signal.emit(f"✗ RectPack 新测试模式失败: {result.get('error', '未知错误')}")
                return False

        except Exception as e:
            self.log_signal.emit(f"RectPack 新测试模式创建画布时发生错误: {str(e)}")
            return False

    # 旧的_create_production_canvas_with_rectpack方法已移除，
    # 现在使用新的_create_rectpack_photoshop_layout方法

    # 旧的_create_rectpack_photoshop_layout方法已移除，
    # 现在使用image_processor架构，完全参照tetris算法

    def _generate_rectpack_production_documentation(self, doc_path: str, arranged_images: List[Dict[str, Any]],
                                                  canvas_width_px: int, canvas_height_px: int,
                                                  tiff_path: str) -> bool:
        """
        生成RectPack正式环境说明文档，参照tetris算法的文档格式

        Args:
            doc_path: 文档输出路径
            arranged_images: 排列好的图片列表
            canvas_width_px: 画布宽度（像素）
            canvas_height_px: 画布高度（像素）
            tiff_path: TIFF文件路径

        Returns:
            bool: 是否生成成功
        """
        try:
            import time
            import os

            # 获取统计信息
            stats = self.unified_arranger.get_layout_statistics()

            # 计算cm尺寸（使用PPI转换）
            canvas_width_cm = canvas_width_px / self.ppi * 2.54
            canvas_height_cm = canvas_height_px / self.ppi * 2.54

            content = []
            content.append("# RectPack算法正式环境说明文档")
            content.append("")
            content.append(f"生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
            content.append("")

            # 正式环境信息
            content.append("★ 正式环境信息")
            content.append("")
            content.append("正式环境下，使用Photoshop进行实际图片排版，生成高质量TIFF格式输出。")
            content.append("支持高分辨率输出，适用于印刷和专业设计工作。")
            content.append("")

            # 画布详情
            content.append("★ 画布详情")
            content.append("")
            content.append(f"- 画布名称: {self.canvas_name}_{self.canvas_sequence}")
            content.append(f"- 材质名称: {self.material_name or 'rectpack_material'}")
            content.append(f"- 画布序号: {self.canvas_sequence}")
            content.append(f"- 画布宽度: {canvas_width_px}px ({canvas_width_cm:.2f}cm)")
            content.append(f"- 画布高度: {canvas_height_px}px ({canvas_height_cm:.2f}cm)")
            content.append(f"- 分辨率: {self.ppi} PPI")
            content.append(f"- 总面积: {stats.get('total_area', 0):,}px²")
            content.append(f"- 已用面积: {stats.get('used_area', 0):,}px²")
            content.append(f"- 利用率: {stats.get('utilization_percent', 0):.2f}%")
            content.append("")

            # RectPack算法信息
            content.append("★ RectPack算法信息")
            content.append("")
            content.append("RectPack算法是一种高效的矩形装箱算法，专门用于图片排列优化。")
            content.append("特点：")
            content.append("- 高效的空间利用率")
            content.append("- 简化的排列逻辑")
            content.append("- 移除了复杂的A/B/C分类逻辑")
            content.append("- 支持智能旋转和优化放置")
            content.append("")

            # 图片统计
            content.append("★ 图片统计")
            content.append("")
            content.append(f"- 图片总数: {len(arranged_images)} 张")

            # 统计图片尺寸分布
            size_stats = {}
            rotation_count = 0
            for img in arranged_images:
                width = img.get('width', 0)
                height = img.get('height', 0)
                size_key = f"{width}x{height}"
                size_stats[size_key] = size_stats.get(size_key, 0) + 1

                if img.get('need_rotation', False) or img.get('rotated', False):
                    rotation_count += 1

            content.append(f"- 旋转图片: {rotation_count} 张")
            content.append(f"- 未旋转图片: {len(arranged_images) - rotation_count} 张")
            content.append("")

            # 尺寸分布
            content.append("★ 图片尺寸分布")
            content.append("")
            for size_key, count in sorted(size_stats.items(), key=lambda x: x[1], reverse=True):
                content.append(f"- {size_key}px: {count} 张")
            content.append("")

            # Photoshop信息
            content.append("★ Photoshop信息")
            content.append("")
            content.append(f"- 颜色模式: RGB")
            content.append(f"- 压缩方式: LZW")
            content.append(f"- 文件格式: TIFF")
            content.append(f"- 输出文件: {os.path.basename(tiff_path)}")
            content.append("")

            # 排列详情
            content.append("★ 图片排列详情")
            content.append("")
            content.append("| 序号 | 图片名称 | 宽度(px) | 高度(px) | X坐标 | Y坐标 | 旋转 | 文件路径 |")
            content.append("|------|----------|---------|---------|------|------|------|----------|")

            for i, img in enumerate(arranged_images, 1):
                name = img.get('name', f'Image_{i}')
                width = img.get('width', 0)
                height = img.get('height', 0)
                x = img.get('x', 0)
                y = img.get('y', 0)
                rotated = "是" if (img.get('need_rotation', False) or img.get('rotated', False)) else "否"
                path = img.get('path', '')
                filename = os.path.basename(path) if path else 'N/A'

                content.append(f"| {i:3d} | {name:12s} | {width:7d} | {height:7d} | {x:4d} | {y:4d} | {rotated:2s} | {filename} |")

            content.append("")

            # 性能统计
            content.append("★ 性能统计")
            content.append("")
            elapsed_time = time.time() - self.start_time if self.start_time else 0
            if elapsed_time > 0:
                speed = len(arranged_images) / elapsed_time
                content.append(f"- 处理时间: {elapsed_time:.2f}秒")
                content.append(f"- 处理速度: {speed:.2f} 图片/秒")
            content.append("")

            # 输出信息
            content.append("★ 输出信息")
            content.append("")
            content.append(f"- TIFF文件: {tiff_path}")
            content.append(f"- 说明文档: {doc_path}")
            content.append(f"- 生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
            content.append("")

            content.append("---")
            content.append("由 RectPack 算法自动生成")

            # 写入文件
            with open(doc_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(content))

            return True

        except Exception as e:
            self.log_signal.emit(f"生成RectPack说明文档失败: {str(e)}")
            return False

    def _setup_matplotlib_chinese_font(self):
        """
        设置 matplotlib 中文字体，解决中文乱码问题 - 增强版
        """
        try:
            import matplotlib.pyplot as plt
            import matplotlib.font_manager as fm
            import platform
            import os

            # 根据操作系统选择合适的中文字体
            system = platform.system()

            if system == 'Windows':
                # Windows系统中文字体路径和名称
                font_configs = [
                    ('SimHei', ['C:/Windows/Fonts/simhei.ttf']),
                    ('Microsoft YaHei', ['C:/Windows/Fonts/msyh.ttc', 'C:/Windows/Fonts/msyh.ttf']),
                    ('SimSun', ['C:/Windows/Fonts/simsun.ttc', 'C:/Windows/Fonts/simsun.ttf']),
                    ('KaiTi', ['C:/Windows/Fonts/simkai.ttf']),
                    ('Microsoft JhengHei', ['C:/Windows/Fonts/msjh.ttc']),
                ]
            elif system == 'Darwin':  # macOS
                font_configs = [
                    ('PingFang SC', ['/System/Library/Fonts/PingFang.ttc']),
                    ('Heiti SC', ['/System/Library/Fonts/STHeiti Light.ttc']),
                    ('STHeiti', ['/System/Library/Fonts/STHeiti Medium.ttc']),
                    ('Arial Unicode MS', ['/Library/Fonts/Arial Unicode.ttf']),
                ]
            else:  # Linux
                font_configs = [
                    ('WenQuanYi Micro Hei', ['/usr/share/fonts/truetype/wqy/wqy-microhei.ttc']),
                    ('Droid Sans Fallback', ['/usr/share/fonts/truetype/droid/DroidSansFallbackFull.ttf']),
                    ('Noto Sans CJK SC', ['/usr/share/fonts/opentype/noto/NotoSansCJK-Regular.ttc']),
                ]

            # 尝试设置字体
            font_set = False
            working_font = None

            for font_name, font_paths in font_configs:
                # 检查字体文件是否存在
                font_exists = any(os.path.exists(path) for path in font_paths)

                if font_exists:
                    try:
                        # 尝试设置字体
                        plt.rcParams['font.sans-serif'] = [font_name] + plt.rcParams['font.sans-serif']
                        plt.rcParams['axes.unicode_minus'] = False

                        # 测试字体是否能正确显示中文
                        test_fig, test_ax = plt.subplots(figsize=(1, 1))
                        test_ax.text(0.5, 0.5, '测试', fontsize=12)
                        plt.close(test_fig)

                        font_set = True
                        working_font = font_name
                        self.log_signal.emit(f"RectPack成功设置中文字体: {font_name}")
                        break

                    except Exception as e:
                        self.log_signal.emit(f"RectPack字体 {font_name} 设置失败: {str(e)}")
                        continue

            if not font_set:
                # 如果都失败了，尝试系统默认字体
                try:
                    # 获取系统中所有可用的中文字体
                    available_fonts = [f.name for f in fm.fontManager.ttflist]
                    chinese_fonts = [f for f in available_fonts if any(keyword in f for keyword in
                                   ['SimHei', 'SimSun', 'Microsoft', 'YaHei', 'PingFang', 'Heiti', 'WenQuanYi', 'Noto'])]

                    if chinese_fonts:
                        plt.rcParams['font.sans-serif'] = chinese_fonts[:3] + ['DejaVu Sans']
                        plt.rcParams['axes.unicode_minus'] = False
                        working_font = chinese_fonts[0]
                        font_set = True
                        self.log_signal.emit(f"RectPack使用系统检测到的中文字体: {working_font}")
                    else:
                        # 最后的备选方案
                        plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial', 'sans-serif']
                        plt.rcParams['axes.unicode_minus'] = False
                        self.log_signal.emit("RectPack警告: 未找到中文字体，使用默认字体，中文可能显示为方块")

                except Exception as e:
                    plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
                    plt.rcParams['axes.unicode_minus'] = False
                    self.log_signal.emit(f"RectPack字体检测失败: {str(e)}，使用默认字体")

            # 清除matplotlib字体缓存，确保新设置生效
            try:
                fm._rebuild()
            except:
                pass

        except Exception as e:
            self.log_signal.emit(f"RectPack设置中文字体失败: {str(e)}")
