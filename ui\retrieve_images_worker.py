#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
检索图片工作线程模块

提供检索图片的异步处理功能：
1. 支持多线程处理
2. 支持进度显示
3. 支持状态更新
4. 支持错误处理
"""

import os
import logging
import time
from PyQt6.QtCore import pyqtSignal

# 导入基类
from ui.base_worker import BaseWorker

# 配置日志
log = logging.getLogger("RetrieveImagesWorker")

class RetrieveImagesWorker(BaseWorker):
    """检索图片工作线程

    提供异步处理检索图片的功能，包括：
    1. 多线程检索处理
    2. 进度显示
    3. 状态更新
    4. 错误处理
    """

    # 特定信号定义
    finished_signal = pyqtSignal(bool, str, list)  # 完成信号，参数为成功标志、消息和任务列表

    def __init__(self):
        """初始化检索图片工作线程"""
        super().__init__()
        self.material_folder_path = ""
        self.image_indexer = None
        self.excel_processor = None
        self.exact_pattern_search = False  # 默认不开启精确查询图案全称
        self.is_standard_mode = True  # 默认使用标准表格模式
        self.is_fuzzy_query = False  # 默认不开启模糊查询

    def set_exact_pattern_search(self, exact_pattern_search):
        """设置是否仅精确查询图案全称

        Args:
            exact_pattern_search: 是否仅精确查询图案全称
        """
        self.exact_pattern_search = exact_pattern_search

    def set_table_mode(self, is_standard_mode):
        """设置表格模式

        Args:
            is_standard_mode: 是否使用标准表格模式，True为标准模式，False为自定义模式
        """
        self.is_standard_mode = is_standard_mode

    def set_fuzzy_query(self, is_fuzzy_query):
        """设置是否开启模糊查询

        Args:
            is_fuzzy_query: 是否开启模糊查询，True为开启，False为关闭
        """
        self.is_fuzzy_query = is_fuzzy_query

    def set_material_folder_path(self, material_folder_path):
        """设置材质文件夹路径

        Args:
            material_folder_path: 材质文件夹路径
        """
        self.material_folder_path = material_folder_path

    def set_image_indexer(self, image_indexer):
        """设置图片索引器

        Args:
            image_indexer: 图片索引器实例
        """
        self.image_indexer = image_indexer

    def set_excel_processor(self, excel_processor):
        """设置Excel处理器

        Args:
            excel_processor: Excel处理器实例
        """
        self.excel_processor = excel_processor

        # 连接Excel处理器的信号
        self.excel_processor.progress_signal.connect(self.on_processor_progress)
        self.excel_processor.status_signal.connect(self.on_processor_status)
        self.excel_processor.error_signal.connect(self.on_processor_error)

    def stop(self):
        """停止检索处理"""
        super().stop()

    def run(self):
        """线程执行函数"""
        try:
            # 检查参数
            if not self.material_folder_path or not os.path.exists(self.material_folder_path):
                self.error_signal.emit("材质文件夹路径无效")
                self.finished_signal.emit(False, "材质文件夹路径无效", [])
                return

            if not self.image_indexer:
                self.error_signal.emit("图片索引器未设置")
                self.finished_signal.emit(False, "图片索引器未设置", [])
                return

            if not self.excel_processor:
                self.error_signal.emit("Excel处理器未设置")
                self.finished_signal.emit(False, "Excel处理器未设置", [])
                return

            # 开始任务
            self._start_task("检索图片")
            self.log_signal.emit(f"开始检索图片: {self.material_folder_path}")
            self.status_signal.emit("正在检索图片...")

            # 删除旧的检索结果文件
            self.delete_old_result_files()

            # 执行检索操作
            start_time = time.time()
            # 如果开启了精确查询图案全称，记录日志
            if self.exact_pattern_search:
                self.log_signal.emit("精确查询图案全称模式已开启，仅查询'图案全称'字段")

            # 记录表格模式
            mode_str = "标准" if self.is_standard_mode else "自定义"
            self.log_signal.emit(f"使用{mode_str}表格模式处理Excel数据")

            # 如果开启了模糊查询，记录日志
            if self.is_fuzzy_query:
                self.log_signal.emit("模糊查询模式已开启，可以查询部分匹配的结果")

            success, message, tasks = self.excel_processor.process_material_folder(
                self.material_folder_path,
                self.image_indexer,
                self.exact_pattern_search,
                self.is_standard_mode,
                self.is_fuzzy_query
            )
            elapsed_time = time.time() - start_time

            # 统计结果
            if success:
                total_tasks = len(tasks)
                success_tasks = sum(1 for task in tasks if task['status'] == 'success')
                failed_tasks = sum(1 for task in tasks if task['status'] == 'failed')
                result_message = f"总任务数 {total_tasks}，成功 {success_tasks}，失败 {failed_tasks}"
            else:
                result_message = message

            # 结束任务
            self._end_task(success, result_message, elapsed_time)

            # 发送完成信号
            self.finished_signal.emit(success, message, tasks)

        except Exception as e:
            log.error(f"检索图片时出错: {str(e)}")
            self.error_signal.emit(f"检索图片时出错: {str(e)}")
            self.finished_signal.emit(False, f"检索图片时出错: {str(e)}", [])

    def delete_old_result_files(self):
        """删除旧的检索结果文件"""
        try:
            self.log_signal.emit("开始删除旧的检索结果文件...")
            deleted_count = 0

            for filename in os.listdir(self.material_folder_path):
                if filename.endswith('.xlsx') and '已检索' in filename:
                    try:
                        file_path = os.path.join(self.material_folder_path, filename)
                        os.remove(file_path)
                        deleted_count += 1
                        self.log_signal.emit(f"已删除文件: {filename}")
                    except Exception as e:
                        self.log_signal.emit(f"删除文件 {filename} 失败: {str(e)}")

            self.log_signal.emit(f"已删除 {deleted_count} 个旧的检索结果文件")

        except Exception as e:
            self.log_signal.emit(f"删除旧文件时出错: {str(e)}")

    def on_processor_progress(self, progress):
        """处理Excel处理器进度信号

        Args:
            progress: 进度值 (0-100)
        """
        self.progress_signal.emit(progress)

    def on_processor_status(self, status):
        """处理Excel处理器状态信号

        Args:
            status: 状态信息
        """
        self.status_signal.emit(status)
        self.log_signal.emit(status)

    def on_processor_error(self, error):
        """处理Excel处理器错误信号

        Args:
            error: 错误信息
        """
        self.error_signal.emit(error)
        self.log_signal.emit(f"错误: {error}")
