#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
高级设置授权模块

提供高级设置的访问控制功能：
1. 从云端获取密码
2. 验证访问权限
"""

import logging
from typing import Optional, Tuple
from .supabase_helper import SupabaseHelper

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
log = logging.getLogger("SettingsAuth")

class AdvancedSettingsAuth:
    """高级设置授权管理类"""

    def __init__(self, supabase_helper=None):
        """初始化授权管理类

        Args:
            supabase_helper: Supabase辅助类实例，如果为None则创建新实例
        """
        self.supabase_helper = supabase_helper if supabase_helper else SupabaseHelper()
        self._password = None
        self._fetch_password()

    def _fetch_password(self) -> None:
        """从云端获取密码"""
        try:
            if not self.supabase_helper.is_connected():
                log.warning("云端服务未连接，无法获取高级设置密码")
                return

            config = self.supabase_helper.fetch_config()
            if config and 'password' in config and config['password']:
                self._password = str(config['password'])
                log.info("成功获取高级设置密码")
            else:
                log.warning("未在云端配置中找到密码字段，高级设置将无法访问")
        except Exception as e:
            log.error(f"获取高级设置密码失败: {str(e)}")

    def verify_password(self, password: str) -> bool:
        """验证密码是否正确

        Args:
            password: 用户输入的密码

        Returns:
            是否验证通过
        """
        # 如果密码未设置，则拒绝所有访问
        if not self._password:
            self._fetch_password()  # 尝试重新获取

            if not self._password:
                log.warning("高级设置密码未设置，拒绝访问")
                return False

        return password == self._password

    def has_password(self) -> bool:
        """检查是否已获取到密码

        Returns:
            是否已获取到密码
        """
        if not self._password:
            self._fetch_password()  # 尝试重新获取

        return self._password is not None

    def refresh_password(self) -> bool:
        """刷新密码

        Returns:
            是否成功刷新
        """
        old_password = self._password
        self._password = None
        self._fetch_password()

        return self._password is not None and self._password != old_password