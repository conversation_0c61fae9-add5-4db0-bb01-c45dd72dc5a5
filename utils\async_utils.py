#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
异步工具模块

提供异步操作和并行处理的工具函数：
1. 异步文件IO操作
2. 异步图像处理
3. 批量并行处理
4. 异步任务调度
"""

import os
import sys
import asyncio
import logging
import time
import concurrent.futures
from typing import List, Dict, Any, Optional, Callable, Tuple, Union, TypeVar, Generic, Iterable
import functools
import inspect
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
log = logging.getLogger("AsyncUtils")

# 定义泛型类型变量
T = TypeVar('T')
R = TypeVar('R')

# 检查是否支持异步IO
try:
    import aiofiles
    AIOFILES_AVAILABLE = True
except ImportError:
    AIOFILES_AVAILABLE = False
    log.warning("aiofiles库未安装，将使用同步文件IO操作")

# 检查是否支持异步图像处理
try:
    from PIL import Image
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False
    log.warning("PIL库未安装，图像处理功能将受限")

async def async_read_file(file_path: str, encoding: str = 'utf-8') -> str:
    """
    异步读取文件内容
    
    Args:
        file_path: 文件路径
        encoding: 文件编码
        
    Returns:
        str: 文件内容
    """
    if AIOFILES_AVAILABLE:
        try:
            async with aiofiles.open(file_path, 'r', encoding=encoding) as f:
                return await f.read()
        except Exception as e:
            log.error(f"异步读取文件失败: {str(e)}")
            # 回退到同步读取
            with open(file_path, 'r', encoding=encoding) as f:
                return f.read()
    else:
        # 使用同步读取
        with open(file_path, 'r', encoding=encoding) as f:
            return f.read()

async def async_write_file(file_path: str, content: str, encoding: str = 'utf-8') -> bool:
    """
    异步写入文件内容
    
    Args:
        file_path: 文件路径
        content: 文件内容
        encoding: 文件编码
        
    Returns:
        bool: 是否成功写入
    """
    if AIOFILES_AVAILABLE:
        try:
            async with aiofiles.open(file_path, 'w', encoding=encoding) as f:
                await f.write(content)
            return True
        except Exception as e:
            log.error(f"异步写入文件失败: {str(e)}")
            # 回退到同步写入
            with open(file_path, 'w', encoding=encoding) as f:
                f.write(content)
            return True
    else:
        # 使用同步写入
        with open(file_path, 'w', encoding=encoding) as f:
            f.write(content)
        return True

async def async_read_binary_file(file_path: str) -> bytes:
    """
    异步读取二进制文件内容
    
    Args:
        file_path: 文件路径
        
    Returns:
        bytes: 文件内容
    """
    if AIOFILES_AVAILABLE:
        try:
            async with aiofiles.open(file_path, 'rb') as f:
                return await f.read()
        except Exception as e:
            log.error(f"异步读取二进制文件失败: {str(e)}")
            # 回退到同步读取
            with open(file_path, 'rb') as f:
                return f.read()
    else:
        # 使用同步读取
        with open(file_path, 'rb') as f:
            return f.read()

async def async_write_binary_file(file_path: str, content: bytes) -> bool:
    """
    异步写入二进制文件内容
    
    Args:
        file_path: 文件路径
        content: 文件内容
        
    Returns:
        bool: 是否成功写入
    """
    if AIOFILES_AVAILABLE:
        try:
            async with aiofiles.open(file_path, 'wb') as f:
                await f.write(content)
            return True
        except Exception as e:
            log.error(f"异步写入二进制文件失败: {str(e)}")
            # 回退到同步写入
            with open(file_path, 'wb') as f:
                f.write(content)
            return True
    else:
        # 使用同步写入
        with open(file_path, 'wb') as f:
            f.write(content)
        return True

async def async_get_image_dimensions(file_path: str) -> Optional[Tuple[int, int]]:
    """
    异步获取图像尺寸
    
    Args:
        file_path: 图像文件路径
        
    Returns:
        Tuple[int, int]: 图像宽度和高度，如果失败则返回None
    """
    if not PIL_AVAILABLE:
        log.error("PIL库未安装，无法获取图像尺寸")
        return None
    
    try:
        # 读取图像文件
        image_data = await async_read_binary_file(file_path)
        
        # 在线程池中处理图像
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, _get_image_dimensions, image_data)
    except Exception as e:
        log.error(f"获取图像尺寸失败: {str(e)}")
        return None

def _get_image_dimensions(image_data: bytes) -> Optional[Tuple[int, int]]:
    """
    在线程池中获取图像尺寸
    
    Args:
        image_data: 图像数据
        
    Returns:
        Tuple[int, int]: 图像宽度和高度，如果失败则返回None
    """
    try:
        import io
        from PIL import Image
        
        # 从二进制数据创建图像对象
        with io.BytesIO(image_data) as f:
            with Image.open(f) as img:
                return img.size
    except Exception as e:
        log.error(f"处理图像数据失败: {str(e)}")
        return None

async def parallel_map(func: Callable[[T], R], items: List[T], 
                      max_workers: Optional[int] = None, 
                      use_process_pool: bool = False) -> List[R]:
    """
    并行映射函数，类似于map但使用线程池或进程池并行处理
    
    Args:
        func: 要应用于每个项目的函数
        items: 项目列表
        max_workers: 最大工作线程/进程数，默认为None（由系统决定）
        use_process_pool: 是否使用进程池，默认为False（使用线程池）
        
    Returns:
        List[R]: 结果列表
    """
    if not items:
        return []
    
    # 创建执行器
    executor_class = ProcessPoolExecutor if use_process_pool else ThreadPoolExecutor
    
    # 获取事件循环
    loop = asyncio.get_event_loop()
    
    # 使用执行器并行处理
    with executor_class(max_workers=max_workers) as executor:
        # 提交所有任务
        futures = [loop.run_in_executor(executor, func, item) for item in items]
        
        # 等待所有任务完成
        results = await asyncio.gather(*futures)
        
        return list(results)

def run_async(func):
    """
    装饰器，用于在同步环境中运行异步函数
    
    Args:
        func: 异步函数
        
    Returns:
        包装后的函数，可以在同步环境中调用
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        if asyncio.iscoroutinefunction(func):
            loop = asyncio.get_event_loop()
            return loop.run_until_complete(func(*args, **kwargs))
        return func(*args, **kwargs)
    return wrapper

def is_async_callable(obj):
    """
    检查对象是否为异步可调用对象
    
    Args:
        obj: 要检查的对象
        
    Returns:
        bool: 是否为异步可调用对象
    """
    return asyncio.iscoroutinefunction(obj) or (
        hasattr(obj, '__call__') and asyncio.iscoroutinefunction(obj.__call__)
    )

async def batch_process(items: List[T], batch_size: int, 
                       process_func: Callable[[List[T]], Any],
                       progress_callback: Optional[Callable[[int, int], None]] = None) -> List[Any]:
    """
    批量处理项目
    
    Args:
        items: 项目列表
        batch_size: 批次大小
        process_func: 处理函数，接受一个批次的项目
        progress_callback: 进度回调函数，接受当前进度和总进度
        
    Returns:
        List[Any]: 处理结果列表
    """
    results = []
    total_items = len(items)
    processed_items = 0
    
    # 分批处理
    for i in range(0, total_items, batch_size):
        batch = items[i:i+batch_size]
        
        # 处理当前批次
        if is_async_callable(process_func):
            batch_results = await process_func(batch)
        else:
            batch_results = process_func(batch)
        
        # 添加结果
        if isinstance(batch_results, list):
            results.extend(batch_results)
        else:
            results.append(batch_results)
        
        # 更新进度
        processed_items += len(batch)
        if progress_callback:
            progress_callback(processed_items, total_items)
    
    return results
