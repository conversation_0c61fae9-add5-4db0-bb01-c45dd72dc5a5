#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
底部空隙填充器模块

提供画布底部空隙填充功能：
1. 识别底部右侧、中间等空隙
2. 填充底部空隙，提高画布利用率
3. 确保B类图片完整排列一组
"""

import logging
import copy
from typing import List, Dict, Any, Tuple, Optional

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
log = logging.getLogger("BottomGapFiller")

class BottomGapFiller:
    """
    底部空隙填充器，提供画布底部空隙填充功能

    特性：
    1. 识别底部右侧、中间等空隙
    2. 填充底部空隙，提高画布利用率
    3. 确保B类图片完整排列一组
    """

    def __init__(self, tetris_packer=None):
        """
        初始化底部空隙填充器

        Args:
            tetris_packer: Tetris算法实例，如果为None则需要在调用方法时提供
        """
        self.tetris_packer = tetris_packer
        log.info("底部空隙填充器初始化完成")

    def fill_bottom_gaps(self, tetris_packer=None, remaining_patterns: List[Dict[str, Any]] = None) -> bool:
        """
        填充底部空隙

        Args:
            tetris_packer: Tetris算法实例，如果为None则使用初始化时提供的实例
            remaining_patterns: 剩余未放置的图片列表

        Returns:
            bool: 是否成功填充
        """
        packer = tetris_packer or self.tetris_packer
        if not packer or not packer.placed_images:
            log.error("未提供有效的Tetris算法实例或画布为空")
            return False

        try:
            # 获取底部行信息
            bottom_row_info = self._get_bottom_row_info(packer)
            if not bottom_row_info:
                log.info("未找到有效的底部行")
                return False

            # 识别底部空隙
            gaps = self._identify_bottom_gaps(packer, bottom_row_info)
            if not gaps:
                log.info("未找到有效的底部空隙")
                return False

            log.info(f"找到 {len(gaps)} 个底部空隙")

            # 如果没有提供剩余图片，无法进行填充
            if not remaining_patterns:
                log.info("未提供剩余图片，无法进行填充")
                return False

            # 填充底部空隙
            success = self._fill_gaps(packer, gaps, remaining_patterns)

            return success

        except Exception as e:
            log.error(f"填充底部空隙失败: {str(e)}")
            return False

    def ensure_b_class_complete_row(self, tetris_packer=None, remaining_patterns: List[Dict[str, Any]] = None) -> bool:
        """
        确保B类图片完整排列一组

        Args:
            tetris_packer: Tetris算法实例，如果为None则使用初始化时提供的实例
            remaining_patterns: 剩余未放置的图片列表

        Returns:
            bool: 是否成功确保
        """
        packer = tetris_packer or self.tetris_packer
        if not packer or not packer.placed_images:
            log.error("未提供有效的Tetris算法实例或画布为空")
            return False

        try:
            # 获取底部行信息
            bottom_row_info = self._get_bottom_row_info(packer)
            if not bottom_row_info:
                log.info("未找到有效的底部行")
                return False

            # 检查底部行是否包含B类图片
            b_class_images = [img for img in bottom_row_info['images'] if img.get('category') == 'B']
            if not b_class_images:
                log.info("底部行不包含B类图片")
                return False

            # 获取B类图片的宽度
            b_width = b_class_images[0]['width']

            # 计算一行可以放置的B类图片数量
            max_count = packer.container_width // b_width

            # 计算当前已放置的B类图片数量
            current_count = len(b_class_images)

            # 如果已经是完整的一组，不需要处理
            if current_count == max_count:
                log.info(f"底部行B类图片已经是完整的一组 ({current_count}/{max_count})")
                return True

            # 如果不是完整的一组，需要补充或移除
            if current_count < max_count:
                # 需要补充B类图片
                needed_count = max_count - current_count
                log.info(f"底部行B类图片不完整 ({current_count}/{max_count})，需要补充 {needed_count} 个")

                # 查找剩余图片中的B类图片
                b_patterns = [p for p in remaining_patterns if p.get('category') == 'B' and p.get('width') == b_width]

                # 如果没有足够的B类图片，无法补充
                if len(b_patterns) < needed_count:
                    log.info(f"剩余图片中没有足够的B类图片 ({len(b_patterns)}/{needed_count})，无法补充")

                    # 如果剩余的B类图片数量加上当前的数量仍然不足以形成完整的一组，考虑移除当前的B类图片
                    if len(b_patterns) + current_count < max_count:
                        log.info(f"考虑移除当前的B类图片 ({current_count} 个)")

                        # 移除底部行的B类图片
                        for img in b_class_images:
                            if img in packer.placed_images:
                                packer.placed_images.remove(img)
                                pattern = img.get('pattern', {})
                                if pattern:
                                    remaining_patterns.insert(0, pattern)

                        # 更新当前最大高度
                        packer.current_max_height = max(img['y'] + img['height'] for img in packer.placed_images) if packer.placed_images else 0

                        log.info(f"已移除底部行的B类图片 ({current_count} 个)")
                        return True

                    return False

                # 补充B类图片
                current_x = bottom_row_info['images'][-1]['x'] + bottom_row_info['images'][-1]['width']

                for i in range(needed_count):
                    if i < len(b_patterns):
                        pattern = b_patterns[i]

                        # 放置图片
                        packer._update_after_placement(
                            current_x, bottom_row_info['y'],
                            b_width, bottom_row_info['height'],
                            b_width, bottom_row_info['height'],
                            pattern, False
                        )

                        # 更新当前x坐标
                        current_x += b_width

                        # 从剩余图片列表中移除已放置的图片
                        remaining_patterns.remove(pattern)

                log.info(f"已补充 {needed_count} 个B类图片")
                return True

            return False

        except Exception as e:
            log.error(f"确保B类图片完整排列一组失败: {str(e)}")
            return False

    def optimize_bottom_image_orientation(self, tetris_packer=None) -> bool:
        """
        优化底部图片方向，优先横向放置

        Args:
            tetris_packer: Tetris算法实例，如果为None则使用初始化时提供的实例

        Returns:
            bool: 是否成功优化
        """
        packer = tetris_packer or self.tetris_packer
        if not packer or not packer.placed_images:
            log.error("未提供有效的Tetris算法实例或画布为空")
            return False

        try:
            # 获取底部行信息
            bottom_row_info = self._get_bottom_row_info(packer)
            if not bottom_row_info:
                log.info("未找到有效的底部行")
                return False

            # 检查底部行图片是否有旋转的
            rotated_images = [img for img in bottom_row_info['images'] if img.get('need_rotation', False)]
            if not rotated_images:
                log.info("底部行没有旋转的图片")
                return False

            # 记录原始状态
            original_placed_images = copy.deepcopy(packer.placed_images)
            original_max_height = packer.get_max_height()

            # 尝试取消旋转
            for img in rotated_images:
                # 从已放置图片列表中移除
                if img in packer.placed_images:
                    packer.placed_images.remove(img)

                # 交换宽高
                width = img['height']
                height = img['width']

                # 尝试横向放置
                x, y, success = packer.find_position(width, height, img.get('pattern', {}))

                if success:
                    # 更新图片信息
                    img['width'] = width
                    img['height'] = height
                    img['x'] = x
                    img['y'] = y
                    img['need_rotation'] = False

                    # 重新添加到已放置图片列表
                    packer._update_after_placement(
                        x, y, width, height, width, height,
                        img.get('pattern', {}), False
                    )
                else:
                    # 如果无法横向放置，恢复原始状态
                    packer.placed_images = original_placed_images
                    packer.current_max_height = original_max_height
                    log.info("无法优化底部图片方向，已恢复原始状态")
                    return False

            # 更新当前最大高度
            packer.current_max_height = max(img['y'] + img['height'] for img in packer.placed_images) if packer.placed_images else 0

            log.info(f"已优化 {len(rotated_images)} 个底部图片的方向")
            return True

        except Exception as e:
            log.error(f"优化底部图片方向失败: {str(e)}")
            return False

    def _get_bottom_row_info(self, packer) -> Dict[str, Any]:
        """
        获取画布底部行信息

        Args:
            packer: Tetris算法实例

        Returns:
            Dict[str, Any]: 底部行信息
        """
        if not packer.placed_images:
            return None

        # 获取当前最大高度
        max_height = packer.get_max_height()
        if max_height <= 0:
            return None

        # 找出底部行的图片
        bottom_row_images = []
        bottom_row_y = None

        # 使用相对误差，更加灵活
        tolerance = max(5, max_height * 0.001)  # 最小5像素，或者最大高度的0.1%

        for img in packer.placed_images:
            img_bottom = img['y'] + img['height']

            # 如果是底部图片
            if abs(img_bottom - max_height) < tolerance:
                if bottom_row_y is None or img['y'] > bottom_row_y:
                    bottom_row_y = img['y']
                bottom_row_images.append(img)

        if not bottom_row_images or bottom_row_y is None:
            return None

        # 按x坐标排序
        bottom_row_images.sort(key=lambda img: img['x'])

        # 计算底部行的宽度和高度
        width_used = sum(img['width'] for img in bottom_row_images)
        max_height = max(img['height'] for img in bottom_row_images)

        # 计算底部行的剩余宽度
        width_remaining = packer.container_width - width_used

        return {
            'y': bottom_row_y,
            'height': max_height,
            'width_used': width_used,
            'width_remaining': width_remaining,
            'images': bottom_row_images
        }

    def _identify_bottom_gaps(self, packer, bottom_row_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        识别底部空隙

        Args:
            packer: Tetris算法实例
            bottom_row_info: 底部行信息

        Returns:
            List[Dict[str, Any]]: 空隙列表
        """
        gaps = []

        # 如果底部行没有图片，整个底部都是空隙
        if not bottom_row_info['images']:
            gaps.append({
                'x': 0,
                'y': 0,
                'width': packer.container_width,
                'height': bottom_row_info['height']
            })
            return gaps

        # 按x坐标排序
        sorted_images = sorted(bottom_row_info['images'], key=lambda img: img['x'])

        # 检查左侧空隙
        if sorted_images[0]['x'] > 0:
            gaps.append({
                'x': 0,
                'y': bottom_row_info['y'],
                'width': sorted_images[0]['x'],
                'height': bottom_row_info['height']
            })

        # 检查中间空隙
        for i in range(len(sorted_images) - 1):
            gap_start = sorted_images[i]['x'] + sorted_images[i]['width']
            gap_end = sorted_images[i + 1]['x']

            if gap_end > gap_start:
                gaps.append({
                    'x': gap_start,
                    'y': bottom_row_info['y'],
                    'width': gap_end - gap_start,
                    'height': bottom_row_info['height']
                })

        # 检查右侧空隙
        last_image = sorted_images[-1]
        right_edge = last_image['x'] + last_image['width']

        if right_edge < packer.container_width:
            gaps.append({
                'x': right_edge,
                'y': bottom_row_info['y'],
                'width': packer.container_width - right_edge,
                'height': bottom_row_info['height']
            })

        return gaps

    def _fill_gaps(self, packer, gaps: List[Dict[str, Any]], remaining_patterns: List[Dict[str, Any]]) -> bool:
        """
        填充空隙

        Args:
            packer: Tetris算法实例
            gaps: 空隙列表
            remaining_patterns: 剩余未放置的图片列表

        Returns:
            bool: 是否成功填充
        """
        # 按宽度降序排序空隙
        gaps.sort(key=lambda g: -g['width'])

        # 记录是否成功填充
        success = False

        # 尝试填充每个空隙
        for gap in gaps:
            # 找出可以放入空隙的图片
            suitable_patterns = []

            for pattern in remaining_patterns:
                # 获取图片尺寸
                width = pattern.get('width_px', pattern.get('width', 0))
                height = pattern.get('height_px', pattern.get('height', 0))

                # 检查是否可以放入空隙
                if width <= gap['width'] and height <= gap['height']:
                    suitable_patterns.append({
                        'pattern': pattern,
                        'width': width,
                        'height': height,
                        'area': width * height,
                        'fit_score': width / gap['width']  # 宽度比作为适应度分数
                    })

                # 检查旋转后是否可以放入空隙
                elif height <= gap['width'] and width <= gap['height']:
                    suitable_patterns.append({
                        'pattern': pattern,
                        'width': height,  # 旋转后的宽度
                        'height': width,  # 旋转后的高度
                        'area': width * height,
                        'fit_score': height / gap['width'],  # 宽度比作为适应度分数
                        'need_rotation': True
                    })

            # 如果没有合适的图片，跳过当前空隙
            if not suitable_patterns:
                continue

            # 按适应度分数降序排序
            suitable_patterns.sort(key=lambda p: -p['fit_score'])

            # 尝试放置图片
            for pattern_info in suitable_patterns:
                pattern = pattern_info['pattern']
                width = pattern_info['width']
                height = pattern_info['height']
                need_rotation = pattern_info.get('need_rotation', False)

                # 检查是否还有足够空间
                if width > gap['width'] or height > gap['height']:
                    continue

                # 检查是否与已放置的图片重叠
                if packer._check_collision(gap['x'], gap['y'], width, height):
                    continue

                # 放置图片
                packer._update_after_placement(
                    gap['x'], gap['y'],
                    width, height,
                    width, height,
                    pattern, need_rotation
                )

                # 从剩余图片列表中移除已放置的图片
                remaining_patterns.remove(pattern)

                # 更新空隙信息
                gap['x'] += width
                gap['width'] -= width

                success = True

                # 如果空隙已经填满，跳出循环
                if gap['width'] < 10:  # 最小宽度阈值
                    break

        return success
