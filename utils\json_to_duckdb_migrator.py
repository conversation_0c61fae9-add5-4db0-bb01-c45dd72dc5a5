#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
JSON到DuckDB配置迁移工具

将原有的config.json配置文件迁移到DuckDB数据库
"""

import os
import json
import logging
import sys
from typing import Dict, Any

# 添加项目根目录到搜索路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from utils.config_manager_duckdb import ConfigManagerDuckDB

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
log = logging.getLogger("JsonToDuckDBMigrator")

def migrate_config(json_file="config.json", db_file="config.db"):
    """将JSON配置迁移到DuckDB数据库
    
    Args:
        json_file: JSON配置文件路径
        db_file: DuckDB数据库文件路径
        
    Returns:
        bool: 是否成功迁移
    """
    try:
        # 检查JSON文件是否存在
        if not os.path.exists(json_file):
            log.warning(f"配置文件 {json_file} 不存在，将使用默认配置")
            return True
        
        # 读取JSON配置
        with open(json_file, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
        
        log.info(f"成功读取配置文件 {json_file}")
        
        # 初始化DuckDB配置管理器
        db_config = ConfigManagerDuckDB(db_file)
        
        # 迁移配置
        success = db_config.update(config_data)
        
        if success:
            log.info(f"成功将配置迁移到 {db_file}")
            
            # 重命名原配置文件为备份
            backup_file = f"{json_file}.bak"
            os.rename(json_file, backup_file)
            log.info(f"原配置文件已备份为 {backup_file}")
        else:
            log.error("配置迁移失败")
        
        return success
        
    except Exception as e:
        log.error(f"配置迁移失败: {str(e)}")
        return False

if __name__ == "__main__":
    # 取命令行参数，如果有的话
    json_file = sys.argv[1] if len(sys.argv) > 1 else "config.json"
    db_file = sys.argv[2] if len(sys.argv) > 2 else "config.db"
    
    # 执行迁移
    success = migrate_config(json_file, db_file)
    
    if success:
        print("配置迁移成功")
        sys.exit(0)
    else:
        print("配置迁移失败")
        sys.exit(1) 