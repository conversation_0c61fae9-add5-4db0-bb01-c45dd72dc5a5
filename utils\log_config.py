#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
日志配置模块

提供统一的日志配置和格式化功能：
1. 统一日志格式
2. 集中日志配置
3. 安全的日志格式化
4. 自定义日志处理器
"""

import os
import sys
import logging
import traceback
from datetime import datetime
from typing import Optional, Dict, Any, List

# 默认日志格式
DEFAULT_LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'

# 自定义日志格式化器，处理特殊字符和格式化问题
class SafeFormatter(logging.Formatter):
    """安全的日志格式化器，处理特殊字符和格式化问题"""
    
    def __init__(self, fmt=None, datefmt=None, style='%', validate=True):
        """初始化格式化器"""
        super().__init__(fmt, datefmt, style, validate)
    
    def format(self, record):
        """安全格式化日志记录"""
        # 保存原始消息
        original_msg = record.msg
        original_args = record.args
        
        try:
            # 尝试使用原始格式化方法
            return super().format(record)
        except Exception as e:
            # 如果格式化失败，使用安全的方式处理
            try:
                # 尝试将消息转换为字符串
                if isinstance(record.msg, (dict, list, tuple)):
                    record.msg = str(record.msg)
                
                # 如果有参数，尝试安全格式化
                if record.args:
                    try:
                        # 尝试正常格式化
                        record.msg = record.msg % record.args
                    except:
                        # 如果格式化失败，简单连接
                        record.msg = f"{record.msg} {str(record.args)}"
                    # 清空参数，防止再次格式化
                    record.args = ()
                
                # 再次尝试格式化
                result = super().format(record)
                return result
            except Exception as inner_e:
                # 如果仍然失败，返回一个安全的错误消息
                error_msg = f"日志格式化错误: {str(inner_e)}"
                record.msg = error_msg
                record.args = ()
                return super().format(record)
            finally:
                # 恢复原始消息和参数
                record.msg = original_msg
                record.args = original_args

# 自定义日志处理器，捕获所有异常
class SafeHandler(logging.StreamHandler):
    """安全的日志处理器，捕获所有异常"""
    
    def emit(self, record):
        """安全发送日志记录"""
        try:
            super().emit(record)
        except Exception as e:
            # 如果发送失败，打印错误到标准错误
            sys.stderr.write(f"日志处理器错误: {str(e)}\n")
            traceback.print_exc(file=sys.stderr)

def configure_logging(level=logging.INFO, log_format=None, log_file=None):
    """配置日志系统
    
    Args:
        level: 日志级别
        log_format: 日志格式
        log_file: 日志文件路径
    """
    # 使用默认格式如果未指定
    if log_format is None:
        log_format = DEFAULT_LOG_FORMAT
    
    # 创建根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(level)
    
    # 清除现有处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # 创建安全的格式化器
    formatter = SafeFormatter(log_format)
    
    # 创建并配置控制台处理器
    console_handler = SafeHandler()
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)
    
    # 如果指定了日志文件，创建文件处理器
    if log_file:
        try:
            file_handler = logging.FileHandler(log_file, encoding='utf-8')
            file_handler.setFormatter(formatter)
            root_logger.addHandler(file_handler)
        except Exception as e:
            sys.stderr.write(f"无法创建日志文件 {log_file}: {str(e)}\n")
    
    return root_logger

def get_logger(name):
    """获取指定名称的日志记录器
    
    Args:
        name: 日志记录器名称
    
    Returns:
        Logger: 日志记录器实例
    """
    return logging.getLogger(name)

# 默认配置
configure_logging()
