#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
并行处理管理器模块

提供动态CPU核心分配和任务调度功能：
1. 根据系统负载自动调整并行度
2. 提供线程池和进程池管理
3. 实现任务优先级调度
4. 优化资源利用效率
"""

import os
import sys
import logging
import time
import concurrent.futures
from typing import Dict, Any, Optional, Callable, List, Tuple, Union
import threading

# 尝试导入psutil，用于获取系统负载信息
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
log = logging.getLogger("ParallelManager")

class ParallelManager:
    """
    并行处理管理器，提供动态CPU核心分配和任务调度
    
    特性：
    1. 根据系统负载自动调整并行度
    2. 提供线程池和进程池管理
    3. 实现任务优先级调度
    4. 优化资源利用效率
    """
    
    # 任务类型常量
    TASK_TYPE_IO = 'io'      # IO密集型任务
    TASK_TYPE_CPU = 'cpu'    # CPU密集型任务
    TASK_TYPE_MIXED = 'mixed'  # 混合型任务
    
    # 优先级常量
    PRIORITY_LOW = 0
    PRIORITY_NORMAL = 1
    PRIORITY_HIGH = 2
    
    def __init__(self, min_workers: int = 1, max_workers: Optional[int] = None):
        """
        初始化并行处理管理器
        
        Args:
            min_workers: 最小工作线程/进程数
            max_workers: 最大工作线程/进程数，默认为CPU核心数的2倍
        """
        self.cpu_count = os.cpu_count() or 4  # 如果无法获取CPU核心数，默认为4
        self.min_workers = max(1, min_workers)
        self.max_workers = max_workers or min(32, self.cpu_count * 2)
        
        # 初始化线程池和进程池
        self._thread_pools: Dict[str, concurrent.futures.ThreadPoolExecutor] = {}
        self._process_pools: Dict[str, concurrent.futures.ProcessPoolExecutor] = {}
        
        # 初始化任务队列
        self._task_queues: Dict[int, List] = {
            self.PRIORITY_LOW: [],
            self.PRIORITY_NORMAL: [],
            self.PRIORITY_HIGH: []
        }
        
        # 初始化锁
        self._lock = threading.Lock()
        
        # 记录上次系统负载检查时间
        self._last_load_check = 0
        self._load_check_interval = 5  # 秒
        self._system_load = 0.0
        
        # 初始化系统负载
        self.update_system_load()
        
        log.info(f"并行处理管理器初始化完成，CPU核心数: {self.cpu_count}，"
                f"工作线程/进程范围: {self.min_workers}-{self.max_workers}")
    
    def update_system_load(self) -> float:
        """
        更新系统负载信息
        
        Returns:
            float: 当前系统负载（0-1之间的浮点数）
        """
        current_time = time.time()
        
        # 如果距离上次检查时间不足指定间隔，直接返回缓存的负载值
        if current_time - self._last_load_check < self._load_check_interval:
            return self._system_load
        
        # 更新系统负载
        if PSUTIL_AVAILABLE:
            try:
                # 获取CPU使用率
                self._system_load = psutil.cpu_percent(interval=0.1) / 100.0
                # 获取内存使用率
                memory_info = psutil.virtual_memory()
                memory_load = memory_info.percent / 100.0
                
                # 综合考虑CPU和内存负载
                self._system_load = (self._system_load * 0.7) + (memory_load * 0.3)
                
                # 确保负载值在0-1之间
                self._system_load = max(0.0, min(1.0, self._system_load))
            except Exception as e:
                log.warning(f"获取系统负载失败: {str(e)}，使用默认值")
                self._system_load = 0.5
        else:
            # 如果没有psutil，使用默认值
            self._system_load = 0.5
        
        # 更新检查时间
        self._last_load_check = current_time
        
        log.debug(f"当前系统负载: {self._system_load:.2f}")
        return self._system_load
    
    def get_optimal_workers(self, task_type: str = TASK_TYPE_MIXED) -> int:
        """
        获取最优工作线程/进程数
        
        Args:
            task_type: 任务类型，可选值为'io'、'cpu'或'mixed'
            
        Returns:
            int: 最优工作线程/进程数
        """
        # 更新系统负载
        system_load = self.update_system_load()
        
        # 根据任务类型和系统负载动态调整
        if task_type == self.TASK_TYPE_IO:
            # IO密集型任务，使用更多线程
            if system_load < 0.3:
                workers = self.cpu_count * 2
            elif system_load < 0.7:
                workers = self.cpu_count + 2
            else:
                workers = self.cpu_count
        elif task_type == self.TASK_TYPE_CPU:
            # CPU密集型任务，根据系统负载调整
            if system_load < 0.3:
                workers = self.cpu_count
            elif system_load < 0.7:
                workers = max(1, self.cpu_count - 1)
            else:
                workers = max(1, self.cpu_count // 2)
        else:  # TASK_TYPE_MIXED
            # 混合型任务，平衡配置
            if system_load < 0.3:
                workers = self.cpu_count + 1
            elif system_load < 0.7:
                workers = self.cpu_count
            else:
                workers = max(1, self.cpu_count - 1)
        
        # 确保工作线程/进程数在指定范围内
        workers = max(self.min_workers, min(self.max_workers, workers))
        
        log.debug(f"任务类型: {task_type}, 系统负载: {system_load:.2f}, 最优工作线程/进程数: {workers}")
        return workers
    
    def create_thread_pool(self, name: str = "default", task_type: str = TASK_TYPE_IO) -> concurrent.futures.ThreadPoolExecutor:
        """
        创建线程池
        
        Args:
            name: 线程池名称
            task_type: 任务类型
            
        Returns:
            ThreadPoolExecutor: 线程池对象
        """
        with self._lock:
            # 如果已存在同名线程池，先关闭它
            if name in self._thread_pools:
                self._thread_pools[name].shutdown(wait=False)
            
            # 创建新线程池
            workers = self.get_optimal_workers(task_type)
            pool = concurrent.futures.ThreadPoolExecutor(max_workers=workers, thread_name_prefix=f"pool-{name}")
            self._thread_pools[name] = pool
            
            log.info(f"创建线程池 '{name}'，工作线程数: {workers}")
            return pool
    
    def create_process_pool(self, name: str = "default", task_type: str = TASK_TYPE_CPU) -> concurrent.futures.ProcessPoolExecutor:
        """
        创建进程池
        
        Args:
            name: 进程池名称
            task_type: 任务类型
            
        Returns:
            ProcessPoolExecutor: 进程池对象
        """
        with self._lock:
            # 如果已存在同名进程池，先关闭它
            if name in self._process_pools:
                self._process_pools[name].shutdown(wait=False)
            
            # 创建新进程池
            workers = self.get_optimal_workers(task_type)
            pool = concurrent.futures.ProcessPoolExecutor(max_workers=workers)
            self._process_pools[name] = pool
            
            log.info(f"创建进程池 '{name}'，工作进程数: {workers}")
            return pool
    
    def get_thread_pool(self, name: str = "default") -> Optional[concurrent.futures.ThreadPoolExecutor]:
        """
        获取指定名称的线程池
        
        Args:
            name: 线程池名称
            
        Returns:
            ThreadPoolExecutor: 线程池对象，如果不存在则返回None
        """
        return self._thread_pools.get(name)
    
    def get_process_pool(self, name: str = "default") -> Optional[concurrent.futures.ProcessPoolExecutor]:
        """
        获取指定名称的进程池
        
        Args:
            name: 进程池名称
            
        Returns:
            ProcessPoolExecutor: 进程池对象，如果不存在则返回None
        """
        return self._process_pools.get(name)
    
    def shutdown(self, wait: bool = True):
        """
        关闭所有线程池和进程池
        
        Args:
            wait: 是否等待所有任务完成
        """
        with self._lock:
            # 关闭所有线程池
            for name, pool in self._thread_pools.items():
                log.info(f"关闭线程池 '{name}'")
                pool.shutdown(wait=wait)
            
            # 关闭所有进程池
            for name, pool in self._process_pools.items():
                log.info(f"关闭进程池 '{name}'")
                pool.shutdown(wait=wait)
            
            # 清空池字典
            self._thread_pools.clear()
            self._process_pools.clear()
            
            log.info("所有线程池和进程池已关闭")
