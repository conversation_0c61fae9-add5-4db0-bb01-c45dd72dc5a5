#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
RectPack性能监控工具
实时监控RectPack算法的性能指标，包括利用率、处理时间、内存使用等
"""

import os
import sys
import time
import psutil
import threading
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass, field
from collections import deque

# 配置日志
from utils.log_config import get_logger
log = get_logger("RectPackMonitor")

@dataclass
class PerformanceMetrics:
    """性能指标数据类"""
    timestamp: float = field(default_factory=time.time)
    utilization_percent: float = 0.0
    processing_time_ms: float = 0.0
    memory_usage_mb: float = 0.0
    cpu_usage_percent: float = 0.0
    images_processed: int = 0
    images_failed: int = 0
    canvas_width: int = 0
    canvas_height: int = 0
    algorithm_config: Dict[str, Any] = field(default_factory=dict)

class RectPackPerformanceMonitor:
    """RectPack性能监控器"""
    
    def __init__(self, log_signal: Optional[Callable] = None, max_history: int = 1000):
        """
        初始化性能监控器
        
        Args:
            log_signal: 日志信号回调函数
            max_history: 最大历史记录数量
        """
        self.log_signal = log_signal
        self.max_history = max_history
        
        # 性能数据存储
        self.metrics_history = deque(maxlen=max_history)
        self.current_session = None
        
        # 监控状态
        self.monitoring_active = False
        self.monitor_thread = None
        self.monitor_interval = 1.0  # 监控间隔（秒）
        
        # 性能阈值
        self.thresholds = {
            'min_utilization': 70.0,  # 最小利用率阈值
            'max_processing_time': 30000,  # 最大处理时间（毫秒）
            'max_memory_usage': 1024,  # 最大内存使用（MB）
            'max_cpu_usage': 80.0,  # 最大CPU使用率
        }
        
        # 统计数据
        self.session_stats = {
            'total_sessions': 0,
            'successful_sessions': 0,
            'failed_sessions': 0,
            'average_utilization': 0.0,
            'average_processing_time': 0.0,
        }
    
    def log(self, message: str):
        """记录日志"""
        if self.log_signal:
            self.log_signal(message)
        log.info(message)
    
    def start_session(self, session_info: Dict[str, Any] = None):
        """
        开始新的监控会话
        
        Args:
            session_info: 会话信息
        """
        self.current_session = {
            'start_time': time.time(),
            'session_info': session_info or {},
            'metrics': [],
            'status': 'running'
        }
        
        self.log(f"📊 开始性能监控会话: {session_info.get('name', 'Unknown')}")
    
    def end_session(self, success: bool = True, final_metrics: PerformanceMetrics = None):
        """
        结束当前监控会话
        
        Args:
            success: 会话是否成功
            final_metrics: 最终性能指标
        """
        if not self.current_session:
            return
        
        self.current_session['end_time'] = time.time()
        self.current_session['duration'] = self.current_session['end_time'] - self.current_session['start_time']
        self.current_session['status'] = 'success' if success else 'failed'
        
        if final_metrics:
            self.current_session['final_metrics'] = final_metrics
            self.metrics_history.append(final_metrics)
        
        # 更新统计数据
        self.session_stats['total_sessions'] += 1
        if success:
            self.session_stats['successful_sessions'] += 1
        else:
            self.session_stats['failed_sessions'] += 1
        
        # 计算平均值
        if self.metrics_history:
            self.session_stats['average_utilization'] = sum(
                m.utilization_percent for m in self.metrics_history
            ) / len(self.metrics_history)
            
            self.session_stats['average_processing_time'] = sum(
                m.processing_time_ms for m in self.metrics_history
            ) / len(self.metrics_history)
        
        duration_str = f"{self.current_session['duration']:.2f}秒"
        status_str = "✅ 成功" if success else "❌ 失败"
        
        self.log(f"📊 监控会话结束: {status_str}, 耗时: {duration_str}")
        
        if final_metrics:
            self.log(f"   - 最终利用率: {final_metrics.utilization_percent:.2f}%")
            self.log(f"   - 处理时间: {final_metrics.processing_time_ms:.0f}ms")
            self.log(f"   - 内存使用: {final_metrics.memory_usage_mb:.1f}MB")
        
        self.current_session = None
    
    def record_metrics(self, metrics: PerformanceMetrics):
        """
        记录性能指标
        
        Args:
            metrics: 性能指标
        """
        self.metrics_history.append(metrics)
        
        if self.current_session:
            self.current_session['metrics'].append(metrics)
        
        # 检查性能阈值
        self._check_thresholds(metrics)
    
    def _check_thresholds(self, metrics: PerformanceMetrics):
        """
        检查性能阈值
        
        Args:
            metrics: 性能指标
        """
        warnings = []
        
        if metrics.utilization_percent < self.thresholds['min_utilization']:
            warnings.append(f"利用率过低: {metrics.utilization_percent:.2f}% < {self.thresholds['min_utilization']}%")
        
        if metrics.processing_time_ms > self.thresholds['max_processing_time']:
            warnings.append(f"处理时间过长: {metrics.processing_time_ms:.0f}ms > {self.thresholds['max_processing_time']}ms")
        
        if metrics.memory_usage_mb > self.thresholds['max_memory_usage']:
            warnings.append(f"内存使用过高: {metrics.memory_usage_mb:.1f}MB > {self.thresholds['max_memory_usage']}MB")
        
        if metrics.cpu_usage_percent > self.thresholds['max_cpu_usage']:
            warnings.append(f"CPU使用率过高: {metrics.cpu_usage_percent:.1f}% > {self.thresholds['max_cpu_usage']}%")
        
        for warning in warnings:
            self.log(f"⚠️ 性能警告: {warning}")
    
    def get_current_system_metrics(self) -> Dict[str, float]:
        """
        获取当前系统性能指标
        
        Returns:
            Dict[str, float]: 系统指标
        """
        try:
            process = psutil.Process()
            
            return {
                'memory_usage_mb': process.memory_info().rss / 1024 / 1024,
                'cpu_usage_percent': process.cpu_percent(),
                'system_memory_percent': psutil.virtual_memory().percent,
                'system_cpu_percent': psutil.cpu_percent(),
            }
        except Exception as e:
            self.log(f"获取系统指标失败: {str(e)}")
            return {}
    
    def start_monitoring(self):
        """开始实时监控"""
        if self.monitoring_active:
            return
        
        self.monitoring_active = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        
        self.log("📊 开始实时性能监控")
    
    def stop_monitoring(self):
        """停止实时监控"""
        self.monitoring_active = False
        
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=2.0)
        
        self.log("📊 停止实时性能监控")
    
    def _monitor_loop(self):
        """监控循环"""
        while self.monitoring_active:
            try:
                system_metrics = self.get_current_system_metrics()
                
                # 创建当前指标
                current_metrics = PerformanceMetrics(
                    memory_usage_mb=system_metrics.get('memory_usage_mb', 0),
                    cpu_usage_percent=system_metrics.get('cpu_usage_percent', 0),
                )
                
                # 检查阈值（仅系统指标）
                if (current_metrics.memory_usage_mb > self.thresholds['max_memory_usage'] or
                    current_metrics.cpu_usage_percent > self.thresholds['max_cpu_usage']):
                    self._check_thresholds(current_metrics)
                
                time.sleep(self.monitor_interval)
                
            except Exception as e:
                self.log(f"监控循环错误: {str(e)}")
                time.sleep(self.monitor_interval)
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """
        获取性能摘要
        
        Returns:
            Dict[str, Any]: 性能摘要
        """
        if not self.metrics_history:
            return {'status': 'no_data'}
        
        recent_metrics = list(self.metrics_history)[-10:]  # 最近10次
        
        summary = {
            'total_records': len(self.metrics_history),
            'recent_records': len(recent_metrics),
            'session_stats': self.session_stats.copy(),
            'recent_performance': {
                'avg_utilization': sum(m.utilization_percent for m in recent_metrics) / len(recent_metrics),
                'avg_processing_time': sum(m.processing_time_ms for m in recent_metrics) / len(recent_metrics),
                'avg_memory_usage': sum(m.memory_usage_mb for m in recent_metrics) / len(recent_metrics),
                'success_rate': sum(1 for m in recent_metrics if m.images_failed == 0) / len(recent_metrics) * 100,
            },
            'thresholds': self.thresholds.copy(),
            'current_system': self.get_current_system_metrics(),
        }
        
        return summary
    
    def generate_report(self) -> str:
        """
        生成性能报告
        
        Returns:
            str: 性能报告文本
        """
        summary = self.get_performance_summary()
        
        if summary.get('status') == 'no_data':
            return "📊 RectPack性能报告\n暂无性能数据"
        
        report = []
        report.append("=" * 60)
        report.append("📊 RectPack性能监控报告")
        report.append("=" * 60)
        
        # 会话统计
        stats = summary['session_stats']
        report.append("会话统计:")
        report.append(f"  总会话数: {stats['total_sessions']}")
        report.append(f"  成功会话: {stats['successful_sessions']}")
        report.append(f"  失败会话: {stats['failed_sessions']}")
        if stats['total_sessions'] > 0:
            success_rate = stats['successful_sessions'] / stats['total_sessions'] * 100
            report.append(f"  成功率: {success_rate:.1f}%")
        report.append("")
        
        # 最近性能
        recent = summary['recent_performance']
        report.append("最近性能 (最近10次):")
        report.append(f"  平均利用率: {recent['avg_utilization']:.2f}%")
        report.append(f"  平均处理时间: {recent['avg_processing_time']:.0f}ms")
        report.append(f"  平均内存使用: {recent['avg_memory_usage']:.1f}MB")
        report.append(f"  成功率: {recent['success_rate']:.1f}%")
        report.append("")
        
        # 当前系统状态
        system = summary['current_system']
        if system:
            report.append("当前系统状态:")
            report.append(f"  进程内存: {system.get('memory_usage_mb', 0):.1f}MB")
            report.append(f"  进程CPU: {system.get('cpu_usage_percent', 0):.1f}%")
            report.append(f"  系统内存: {system.get('system_memory_percent', 0):.1f}%")
            report.append(f"  系统CPU: {system.get('system_cpu_percent', 0):.1f}%")
        
        report.append("=" * 60)
        return "\n".join(report)
    
    def export_metrics(self, file_path: str) -> bool:
        """
        导出性能指标到文件
        
        Args:
            file_path: 导出文件路径
            
        Returns:
            bool: 是否成功导出
        """
        try:
            import json
            
            export_data = {
                'export_time': time.time(),
                'metrics_count': len(self.metrics_history),
                'session_stats': self.session_stats,
                'thresholds': self.thresholds,
                'metrics': [
                    {
                        'timestamp': m.timestamp,
                        'utilization_percent': m.utilization_percent,
                        'processing_time_ms': m.processing_time_ms,
                        'memory_usage_mb': m.memory_usage_mb,
                        'cpu_usage_percent': m.cpu_usage_percent,
                        'images_processed': m.images_processed,
                        'images_failed': m.images_failed,
                        'canvas_width': m.canvas_width,
                        'canvas_height': m.canvas_height,
                        'algorithm_config': m.algorithm_config,
                    }
                    for m in self.metrics_history
                ]
            }
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)
            
            self.log(f"📊 性能指标已导出到: {file_path}")
            return True
            
        except Exception as e:
            self.log(f"导出性能指标失败: {str(e)}")
            return False


# 全局监控器实例
_global_monitor = None

def get_performance_monitor(log_signal: Optional[Callable] = None) -> RectPackPerformanceMonitor:
    """
    获取全局性能监控器实例
    
    Args:
        log_signal: 日志信号回调函数
        
    Returns:
        RectPackPerformanceMonitor: 监控器实例
    """
    global _global_monitor
    
    if _global_monitor is None:
        _global_monitor = RectPackPerformanceMonitor(log_signal)
    elif log_signal and not _global_monitor.log_signal:
        _global_monitor.log_signal = log_signal
    
    return _global_monitor


if __name__ == "__main__":
    # 测试代码
    monitor = RectPackPerformanceMonitor()
    
    # 模拟监控会话
    monitor.start_session({'name': 'test_session'})
    
    # 模拟性能数据
    for i in range(5):
        metrics = PerformanceMetrics(
            utilization_percent=75.0 + i * 2,
            processing_time_ms=1000 + i * 100,
            memory_usage_mb=200 + i * 10,
            cpu_usage_percent=30 + i * 5,
            images_processed=50 + i * 10,
            images_failed=i,
        )
        monitor.record_metrics(metrics)
        time.sleep(0.1)
    
    # 结束会话
    final_metrics = PerformanceMetrics(
        utilization_percent=85.0,
        processing_time_ms=1500,
        memory_usage_mb=250,
        cpu_usage_percent=45,
        images_processed=100,
        images_failed=2,
    )
    monitor.end_session(True, final_metrics)
    
    # 生成报告
    print(monitor.generate_report())
