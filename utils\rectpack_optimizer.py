#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
RectPack算法优化器
专门用于检查、验证和优化RectPack算法配置，确保生产环境下的最佳性能
"""

import os
import sys
import logging
import time
from typing import Dict, Any, Tuple, Optional

# 配置日志
from utils.log_config import get_logger
log = get_logger("RectPackOptimizer")

class RectPackOptimizer:
    """RectPack算法优化器"""
    
    def __init__(self, config_manager=None):
        """
        初始化优化器
        
        Args:
            config_manager: 配置管理器实例
        """
        self.config_manager = config_manager
        self.rectpack_available = False
        self.optimization_results = {}
        
    def check_environment(self) -> Tuple[bool, str]:
        """
        检查RectPack运行环境
        
        Returns:
            Tuple[bool, str]: (是否可用, 状态信息)
        """
        try:
            # 检查RectPack库
            import rectpack
            from rectpack import newPacker, MaxRectsBssf, <PERSON>RectsBaf, MaxRectsBlsf
            
            # 测试基本功能
            test_packer = newPacker()
            test_packer.add_bin(100, 100)
            test_packer.add_rect(50, 50)
            test_packer.pack()
            
            # 检查结果
            bins = test_packer.bin_list()
            if bins and len(bins) > 0:
                self.rectpack_available = True
                return True, "RectPack库功能正常"
            else:
                return False, "RectPack库功能异常：无法正常装箱"
                
        except ImportError as e:
            return False, f"RectPack库未安装: {str(e)}"
        except Exception as e:
            return False, f"RectPack库功能测试失败: {str(e)}"
    
    def get_optimal_config(self) -> Dict[str, Any]:
        """
        获取生产环境优化配置
        
        Returns:
            Dict[str, Any]: 优化配置字典
        """
        return {
            # 基础算法参数 - 经过测试优化
            'rectpack_rotation_enabled': True,  # 启用旋转提高利用率
            'rectpack_sort_strategy': 0,  # 面积排序策略（最佳）
            'rectpack_pack_algorithm': 0,  # Best Short Side Fit（最佳）
            
            # 高级算法参数 - 提升排列质量
            'rectpack_bin_selection_strategy': 2,  # 最佳bin选择策略
            'rectpack_split_heuristic': 1,  # 启用分割启发式
            'rectpack_free_rect_choice': 0,  # 最佳短边适应
            
            # 优化参数 - 平衡性能和质量
            'rectpack_enable_optimization': True,  # 启用利用率优化
            'rectpack_optimization_iterations': 3,  # 3次迭代（平衡速度和质量）
            'rectpack_min_utilization_threshold': 80.0,  # 80%利用率阈值
            'rectpack_rotation_penalty': 0.02,  # 低旋转惩罚
            'rectpack_aspect_ratio_preference': 1.2,  # 轻微偏好横向
            
            # 性能参数 - 确保稳定运行
            'rectpack_max_processing_time': 180,  # 3分钟超时
            'rectpack_batch_size': 50,  # 适中批处理大小
            'rectpack_memory_limit_mb': 512,  # 512MB内存限制
            'rectpack_enable_parallel': False,  # 禁用并行（避免PS冲突）
            
            # 调试参数 - 生产环境设置
            'rectpack_debug_mode': False,  # 关闭调试
            'rectpack_log_level': 1,  # 基础日志
            'rectpack_save_intermediate_results': False,  # 不保存中间结果
            'rectpack_visualization_enabled': False,  # 关闭可视化
        }
    
    def apply_optimal_config(self) -> bool:
        """
        应用优化配置到配置管理器
        
        Returns:
            bool: 是否成功应用
        """
        if not self.config_manager:
            log.error("配置管理器未初始化")
            return False
        
        try:
            optimal_config = self.get_optimal_config()
            
            for key, value in optimal_config.items():
                self.config_manager.set(key, value)
            
            log.info("RectPack优化配置已应用")
            return True
            
        except Exception as e:
            log.error(f"应用优化配置失败: {str(e)}")
            return False
    
    def benchmark_algorithms(self, test_data: list = None) -> Dict[str, Any]:
        """
        基准测试不同算法配置
        
        Args:
            test_data: 测试数据，如果为None则使用默认测试数据
            
        Returns:
            Dict[str, Any]: 基准测试结果
        """
        if not self.rectpack_available:
            log.error("RectPack库不可用，无法进行基准测试")
            return {}
        
        if test_data is None:
            # 使用默认测试数据
            test_data = [
                (100, 50), (80, 60), (120, 40), (90, 70), (110, 55),
                (75, 85), (95, 45), (105, 65), (85, 75), (115, 35)
            ]
        
        results = {}
        
        # 测试不同算法组合
        test_configs = [
            ("Best Short Side Fit + Area Sort", 0, 0),
            ("Best Area Fit + Area Sort", 1, 0),
            ("Best Long Side Fit + Area Sort", 2, 0),
            ("Best Short Side Fit + Perimeter Sort", 0, 1),
        ]
        
        for config_name, pack_algo, sort_strategy in test_configs:
            try:
                start_time = time.time()
                utilization = self._test_algorithm_config(
                    test_data, pack_algo, sort_strategy
                )
                end_time = time.time()
                
                results[config_name] = {
                    'utilization': utilization,
                    'processing_time': end_time - start_time,
                    'pack_algo': pack_algo,
                    'sort_strategy': sort_strategy
                }
                
                log.info(f"{config_name}: 利用率={utilization:.2f}%, 耗时={end_time - start_time:.3f}s")
                
            except Exception as e:
                log.error(f"测试配置 {config_name} 失败: {str(e)}")
                results[config_name] = {'error': str(e)}
        
        return results
    
    def _test_algorithm_config(self, test_data: list, pack_algo: int, sort_strategy: int) -> float:
        """
        测试特定算法配置
        
        Args:
            test_data: 测试数据
            pack_algo: 装箱算法
            sort_strategy: 排序策略
            
        Returns:
            float: 画布利用率百分比
        """
        try:
            from rectpack import newPacker, MaxRectsBssf, MaxRectsBaf, MaxRectsBlsf
            from rectpack import SORT_AREA, SORT_PERI
            
            # 选择算法
            pack_algos = [MaxRectsBssf, MaxRectsBaf, MaxRectsBlsf]
            sort_algos = [SORT_AREA, SORT_PERI]
            
            packer = newPacker(
                pack_algo=pack_algos[pack_algo],
                sort_algo=sort_algos[sort_strategy],
                rotation=True
            )
            
            # 添加容器
            container_width, container_height = 400, 300
            packer.add_bin(container_width, container_height)
            
            # 添加矩形
            for i, (width, height) in enumerate(test_data):
                packer.add_rect(width, height, rid=i)
            
            # 执行装箱
            packer.pack()
            
            # 计算利用率
            total_area = sum(w * h for w, h in test_data)
            container_area = container_width * container_height
            utilization = (total_area / container_area) * 100
            
            return utilization
            
        except Exception as e:
            log.error(f"算法配置测试失败: {str(e)}")
            return 0.0
    
    def validate_config(self, config: Dict[str, Any]) -> Tuple[bool, list]:
        """
        验证配置的有效性
        
        Args:
            config: 要验证的配置
            
        Returns:
            Tuple[bool, list]: (是否有效, 错误列表)
        """
        errors = []
        
        # 检查必需参数
        required_params = [
            'rectpack_rotation_enabled',
            'rectpack_sort_strategy',
            'rectpack_pack_algorithm',
            'rectpack_optimization_iterations',
            'rectpack_min_utilization_threshold'
        ]
        
        for param in required_params:
            if param not in config:
                errors.append(f"缺少必需参数: {param}")
        
        # 检查参数范围
        if 'rectpack_optimization_iterations' in config:
            iterations = config['rectpack_optimization_iterations']
            if not isinstance(iterations, int) or iterations < 1 or iterations > 10:
                errors.append("optimization_iterations 必须是1-10之间的整数")
        
        if 'rectpack_min_utilization_threshold' in config:
            threshold = config['rectpack_min_utilization_threshold']
            if not isinstance(threshold, (int, float)) or threshold < 0 or threshold > 100:
                errors.append("min_utilization_threshold 必须是0-100之间的数值")
        
        if 'rectpack_memory_limit_mb' in config:
            memory_limit = config['rectpack_memory_limit_mb']
            if not isinstance(memory_limit, int) or memory_limit < 128:
                errors.append("memory_limit_mb 必须是至少128的整数")
        
        return len(errors) == 0, errors
    
    def generate_report(self) -> str:
        """
        生成优化报告
        
        Returns:
            str: 优化报告文本
        """
        report = []
        report.append("=" * 60)
        report.append("RectPack算法优化报告")
        report.append("=" * 60)
        
        # 环境检查
        is_available, status = self.check_environment()
        report.append(f"环境状态: {'✅ 正常' if is_available else '❌ 异常'}")
        report.append(f"状态详情: {status}")
        report.append("")
        
        # 推荐配置
        optimal_config = self.get_optimal_config()
        report.append("推荐配置:")
        for key, value in optimal_config.items():
            if key.startswith('rectpack_'):
                display_key = key.replace('rectpack_', '')
                report.append(f"  {display_key}: {value}")
        report.append("")
        
        # 基准测试结果
        if self.optimization_results:
            report.append("基准测试结果:")
            for config_name, result in self.optimization_results.items():
                if 'error' in result:
                    report.append(f"  {config_name}: 测试失败 - {result['error']}")
                else:
                    report.append(f"  {config_name}: 利用率={result['utilization']:.2f}%, "
                                f"耗时={result['processing_time']:.3f}s")
        
        report.append("=" * 60)
        return "\n".join(report)


def optimize_rectpack_for_production(config_manager=None) -> bool:
    """
    为生产环境优化RectPack配置的便捷函数
    
    Args:
        config_manager: 配置管理器实例
        
    Returns:
        bool: 是否成功优化
    """
    optimizer = RectPackOptimizer(config_manager)
    
    # 检查环境
    is_available, status = optimizer.check_environment()
    if not is_available:
        log.error(f"RectPack环境检查失败: {status}")
        return False
    
    # 应用优化配置
    success = optimizer.apply_optimal_config()
    if success:
        log.info("RectPack生产环境优化配置已应用")
        
        # 生成报告
        report = optimizer.generate_report()
        log.info(f"优化报告:\n{report}")
    
    return success


if __name__ == "__main__":
    # 独立运行时的测试代码
    optimizer = RectPackOptimizer()
    
    # 检查环境
    is_available, status = optimizer.check_environment()
    print(f"环境检查: {'✅ 正常' if is_available else '❌ 异常'} - {status}")
    
    if is_available:
        # 运行基准测试
        print("\n运行基准测试...")
        results = optimizer.benchmark_algorithms()
        optimizer.optimization_results = results
        
        # 生成报告
        print("\n" + optimizer.generate_report())
