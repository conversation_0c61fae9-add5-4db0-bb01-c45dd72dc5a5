#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
流式处理器模块

提供流式数据处理功能：
1. 惰性加载和处理
2. 数据流水线处理
3. 批量处理优化
4. 内存使用优化
"""

import os
import sys
import logging
import time
import threading
import queue
import itertools
from typing import List, Dict, Any, Optional, Tuple, Union, Callable, Iterator, Generator, TypeVar

# 导入自定义模块
from utils.memory_manager import MemoryManager

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
log = logging.getLogger("StreamProcessor")

# 类型变量
T = TypeVar('T')
U = TypeVar('U')

class StreamProcessor:
    """流式处理器，提供流式数据处理功能"""
    
    def __init__(self, batch_size: int = 100, max_queue_size: int = 1000):
        """
        初始化流式处理器
        
        Args:
            batch_size: 批处理大小
            max_queue_size: 最大队列大小
        """
        self.batch_size = batch_size
        self.max_queue_size = max_queue_size
        self.memory_manager = MemoryManager()
        self.processing_threads = []
        self.stop_event = threading.Event()
        
        log.info(f"流式处理器初始化完成，批处理大小: {batch_size}，最大队列大小: {max_queue_size}")
    
    def process_stream(self, data_source: Iterator[T], processor_func: Callable[[T], U], 
                      result_handler: Callable[[U], None] = None, 
                      error_handler: Callable[[Exception, T], None] = None,
                      progress_callback: Callable[[int, int], None] = None,
                      num_threads: int = 1) -> List[U]:
        """
        处理数据流
        
        Args:
            data_source: 数据源迭代器
            processor_func: 处理函数，接收一个数据项并返回处理结果
            result_handler: 结果处理函数，接收处理结果
            error_handler: 错误处理函数，接收异常和原始数据项
            progress_callback: 进度回调函数，接收已处理数量和总数量
            num_threads: 处理线程数
            
        Returns:
            List[U]: 处理结果列表，如果提供了result_handler则为空列表
        """
        # 重置停止事件
        self.stop_event.clear()
        
        # 创建队列
        input_queue = queue.Queue(maxsize=self.max_queue_size)
        result_queue = queue.Queue(maxsize=self.max_queue_size)
        
        # 创建处理线程
        self.processing_threads = []
        for _ in range(num_threads):
            thread = threading.Thread(
                target=self._process_worker,
                args=(input_queue, result_queue, processor_func, error_handler),
                daemon=True
            )
            thread.start()
            self.processing_threads.append(thread)
        
        # 创建结果处理线程
        results = []
        result_thread = None
        if result_handler:
            result_thread = threading.Thread(
                target=self._result_worker,
                args=(result_queue, result_handler),
                daemon=True
            )
            result_thread.start()
        
        # 计算数据源大小（如果可能）
        total_items = 0
        try:
            if hasattr(data_source, '__len__'):
                total_items = len(data_source)
            elif isinstance(data_source, list) or isinstance(data_source, tuple):
                total_items = len(data_source)
        except:
            total_items = 0
        
        # 填充输入队列
        processed_count = 0
        try:
            for item in data_source:
                if self.stop_event.is_set():
                    break
                
                # 如果队列已满，等待空间
                while input_queue.full() and not self.stop_event.is_set():
                    time.sleep(0.01)
                
                if self.stop_event.is_set():
                    break
                
                # 添加到队列
                input_queue.put(item)
                
                # 如果没有结果处理线程，从结果队列获取结果
                if not result_handler:
                    while not result_queue.empty():
                        results.append(result_queue.get())
                        result_queue.task_done()
                        processed_count += 1
                        
                        # 更新进度
                        if progress_callback and total_items > 0:
                            progress_callback(processed_count, total_items)
        except Exception as e:
            log.error(f"填充输入队列时出错: {str(e)}")
        
        # 标记输入队列结束
        for _ in range(num_threads):
            input_queue.put(None)
        
        # 等待处理线程完成
        for thread in self.processing_threads:
            thread.join()
        
        # 获取剩余结果
        if not result_handler:
            while not result_queue.empty():
                results.append(result_queue.get())
                result_queue.task_done()
                processed_count += 1
                
                # 更新进度
                if progress_callback and total_items > 0:
                    progress_callback(processed_count, total_items)
        
        # 标记结果队列结束
        if result_handler:
            result_queue.put(None)
            if result_thread:
                result_thread.join()
        
        # 清理
        self.processing_threads = []
        
        return results
    
    def _process_worker(self, input_queue: queue.Queue, result_queue: queue.Queue, 
                       processor_func: Callable[[T], U], 
                       error_handler: Callable[[Exception, T], None] = None):
        """
        处理工作线程
        
        Args:
            input_queue: 输入队列
            result_queue: 结果队列
            processor_func: 处理函数
            error_handler: 错误处理函数
        """
        while not self.stop_event.is_set():
            try:
                # 获取输入项
                item = input_queue.get()
                
                # 检查是否为结束标记
                if item is None:
                    input_queue.task_done()
                    break
                
                # 处理项
                try:
                    result = processor_func(item)
                    result_queue.put(result)
                except Exception as e:
                    log.error(f"处理项时出错: {str(e)}")
                    if error_handler:
                        error_handler(e, item)
                
                # 标记任务完成
                input_queue.task_done()
                
            except Exception as e:
                log.error(f"处理工作线程异常: {str(e)}")
    
    def _result_worker(self, result_queue: queue.Queue, result_handler: Callable[[U], None]):
        """
        结果处理工作线程
        
        Args:
            result_queue: 结果队列
            result_handler: 结果处理函数
        """
        while not self.stop_event.is_set():
            try:
                # 获取结果
                result = result_queue.get()
                
                # 检查是否为结束标记
                if result is None:
                    result_queue.task_done()
                    break
                
                # 处理结果
                result_handler(result)
                
                # 标记任务完成
                result_queue.task_done()
                
            except Exception as e:
                log.error(f"结果处理工作线程异常: {str(e)}")
    
    def stop(self):
        """停止处理"""
        self.stop_event.set()
        
        # 等待处理线程完成
        for thread in self.processing_threads:
            if thread.is_alive():
                thread.join(timeout=1.0)
        
        self.processing_threads = []
        log.info("流式处理器已停止")
    
    def process_batches(self, data_source: Iterator[T], processor_func: Callable[[List[T]], List[U]], 
                       result_handler: Callable[[U], None] = None, 
                       error_handler: Callable[[Exception, List[T]], None] = None,
                       progress_callback: Callable[[int, int], None] = None) -> List[U]:
        """
        批量处理数据流
        
        Args:
            data_source: 数据源迭代器
            processor_func: 批处理函数，接收一批数据项并返回处理结果列表
            result_handler: 结果处理函数，接收单个处理结果
            error_handler: 错误处理函数，接收异常和原始数据批次
            progress_callback: 进度回调函数，接收已处理数量和总数量
            
        Returns:
            List[U]: 处理结果列表，如果提供了result_handler则为空列表
        """
        # 计算数据源大小（如果可能）
        total_items = 0
        try:
            if hasattr(data_source, '__len__'):
                total_items = len(data_source)
            elif isinstance(data_source, list) or isinstance(data_source, tuple):
                total_items = len(data_source)
        except:
            total_items = 0
        
        # 分批处理
        results = []
        processed_count = 0
        
        # 创建批次
        batches = self._create_batches(data_source, self.batch_size)
        
        for batch in batches:
            if self.stop_event.is_set():
                break
            
            try:
                # 处理批次
                batch_results = processor_func(batch)
                
                # 处理结果
                if result_handler:
                    for result in batch_results:
                        result_handler(result)
                else:
                    results.extend(batch_results)
                
                # 更新进度
                processed_count += len(batch)
                if progress_callback and total_items > 0:
                    progress_callback(processed_count, total_items)
                    
            except Exception as e:
                log.error(f"处理批次时出错: {str(e)}")
                if error_handler:
                    error_handler(e, batch)
        
        return results
    
    def _create_batches(self, data_source: Iterator[T], batch_size: int) -> Generator[List[T], None, None]:
        """
        创建批次
        
        Args:
            data_source: 数据源迭代器
            batch_size: 批处理大小
            
        Returns:
            Generator[List[T], None, None]: 批次生成器
        """
        batch = []
        for item in data_source:
            batch.append(item)
            if len(batch) >= batch_size:
                yield batch
                batch = []
        
        if batch:
            yield batch
    
    def create_pipeline(self, *processors: Callable[[T], U]) -> Callable[[T], Any]:
        """
        创建处理管道
        
        Args:
            *processors: 处理函数列表
            
        Returns:
            Callable[[T], Any]: 管道处理函数
        """
        def pipeline(item):
            result = item
            for processor in processors:
                result = processor(result)
            return result
        
        return pipeline
    
    def lazy_map(self, data_source: Iterator[T], map_func: Callable[[T], U]) -> Generator[U, None, None]:
        """
        惰性映射
        
        Args:
            data_source: 数据源迭代器
            map_func: 映射函数
            
        Returns:
            Generator[U, None, None]: 结果生成器
        """
        for item in data_source:
            yield map_func(item)
    
    def lazy_filter(self, data_source: Iterator[T], filter_func: Callable[[T], bool]) -> Generator[T, None, None]:
        """
        惰性过滤
        
        Args:
            data_source: 数据源迭代器
            filter_func: 过滤函数
            
        Returns:
            Generator[T, None, None]: 结果生成器
        """
        for item in data_source:
            if filter_func(item):
                yield item
    
    def lazy_batch(self, data_source: Iterator[T], batch_size: int) -> Generator[List[T], None, None]:
        """
        惰性批处理
        
        Args:
            data_source: 数据源迭代器
            batch_size: 批处理大小
            
        Returns:
            Generator[List[T], None, None]: 批次生成器
        """
        return self._create_batches(data_source, batch_size)
