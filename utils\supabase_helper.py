#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Supabase辅助模块

提供Supabase数据库连接和配置获取功能：
1. 用户认证和会话管理
2. RLS安全策略支持
3. 配置获取和同步
"""

import logging
import time
from typing import Dict, Any, Tuple, Optional
from supabase import create_client, Client

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
log = logging.getLogger("SupabaseHelper")

# 云端配置
SUPABASE_URL = 'https://cnqfr7i5g6hfi0gtjhj0.baseapi.memfiredb.com'
SUPABASE_API_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyb2xlIjoiYW5vbiIsImV4cCI6MzI4NzM1NDUyNiwiaWF0IjoxNzEwNTU0NTI2LCJpc3MiOiJzdXBhYmFzZSJ9.2OKUbREN2eOTDl5iB9FtHfv-nQaTgHCmm9qeRaNqeyI'

ROBOT_BOT_TAG = 'dachuan_robot_001'

class SupabaseHelper:
    """Supabase辅助类"""

    def __init__(self):
        """初始化Supabase客户端"""
        self.client = None
        self.user_session = {
            "user": None,
            "access_token": None,
            "refresh_token": None,
            "expires_at": 0
        }
        self._initialize_client()

    def _initialize_client(self):
        """初始化Supabase客户端"""
        try:
            self.client = create_client(SUPABASE_URL, SUPABASE_API_KEY)
            log.info("成功连接到云端配置服务")
        except Exception as e:
            log.error(f"云端配置服务连接失败: {str(e)}")
            self.client = None

    def is_connected(self):
        """检查是否已连接到云端配置服务"""
        return self.client is not None

    def login(self, email: str, password: str) -> Tuple[bool, str]:
        """使用邮箱和密码登录

        Args:
            email: 用户邮箱
            password: 用户密码

        Returns:
            (是否成功, 消息)
        """
        if not self.is_connected():
            return False, "云端服务未连接，无法登录"

        try:
            # 尝试使用邮箱和密码登录
            response = self.client.auth.sign_in_with_password({
                "email": email,
                "password": password
            })

            # 保存用户会话信息
            self.user_session = {
                "user": response.user,
                "access_token": response.session.access_token,
                "refresh_token": response.session.refresh_token,
                "expires_at": time.time() + response.session.expires_in
            }

            # 更新客户端会话
            self.client.auth.set_session(response.session.access_token, response.session.refresh_token)

            log.info(f"用户 {email} 登录成功")
            return True, "登录成功"

        except Exception as e:
            log.error(f"登录失败: {str(e)}")
            return False, "登录失败，请检查用户名和密码"

    def logout(self) -> Tuple[bool, str]:
        """退出登录

        Returns:
            (是否成功, 消息)
        """
        if not self.is_connected():
            return False, "云端服务未连接，无法退出登录"

        try:
            # 尝试退出登录
            self.client.auth.sign_out()

            # 清除会话信息
            self.user_session = {
                "user": None,
                "access_token": None,
                "refresh_token": None,
                "expires_at": 0
            }

            log.info("用户已退出登录")
            return True, "已退出登录"

        except Exception as e:
            log.error(f"退出登录失败: {str(e)}")
            return False, f"退出登录失败: {str(e)}"

    def is_authenticated(self) -> bool:
        """检查用户是否已认证

        Returns:
            是否已认证
        """
        # 检查是否有会话信息
        if not self.user_session["access_token"]:
            return False

        # 检查会话是否过期
        if time.time() > self.user_session["expires_at"]:
            # 尝试刷新会话
            refreshed = self.refresh_session()
            return refreshed

        return True

    def get_user_email(self) -> Optional[str]:
        """获取当前登录用户的邮箱

        Returns:
            用户邮箱，如果未登录则返回None
        """
        if not self.is_authenticated() or not self.user_session["user"]:
            return None

        try:
            # 从用户会话中获取邮箱
            return self.user_session["user"].email
        except Exception as e:
            log.error(f"获取用户邮箱失败: {str(e)}")
            return None

    def refresh_session(self) -> bool:
        """刷新用户会话

        Returns:
            是否成功刷新
        """
        if not self.is_connected() or not self.user_session["refresh_token"]:
            return False

        try:
            # 尝试刷新会话
            response = self.client.auth.refresh_session()

            # 更新会话信息
            self.user_session = {
                "user": response.user,
                "access_token": response.session.access_token,
                "refresh_token": response.session.refresh_token,
                "expires_at": time.time() + response.session.expires_in
            }

            log.info("用户会话已刷新")
            return True

        except Exception as e:
            log.error(f"刷新会话失败: {str(e)}")
            return False

    def get_authenticated_client(self) -> Optional[Client]:
        """获取已认证的Supabase客户端，用于RLS访问

        Returns:
            已认证的Supabase客户端，如果未认证则返回None
        """
        if not self.is_connected():
            log.warning("云端服务未连接，无法获取已认证的客户端")
            return None

        # 检查是否有会话信息
        if not self.user_session["access_token"]:
            log.warning("用户会话不存在，无法获取已认证的客户端")
            return None

        # 检查会话是否过期
        if time.time() > self.user_session["expires_at"]:
            log.info("用户会话已过期，尝试刷新会话")
            if not self.refresh_session():
                log.warning("刷新会话失败，无法获取已认证的客户端")
                return None
            log.info("会话刷新成功")

        try:
            # 确保客户端使用最新的会话
            log.debug(f"设置客户端会话，token长度: {len(self.user_session['access_token'])}")
            self.client.auth.set_session(self.user_session["access_token"], self.user_session["refresh_token"])

            # 验证会话是否有效
            user = self.client.auth.get_user()
            if user and user.user:
                log.info(f"已获取认证客户端，用户ID: {user.user.id}")
                return self.client
            else:
                log.warning("设置会话后无法获取用户信息，认证可能失败")
                return None
        except Exception as e:
            log.error(f"设置客户端会话失败: {str(e)}")
            return None

    def fetch_config(self):
        """从云端获取配置参数"""
        if not self.is_connected():
            log.warning("云端配置服务未连接，无法获取配置")
            return None

        try:
            # 检查用户是否已登录
            is_auth = self.is_authenticated()
            log.info(f"用户认证状态: {'已认证' if is_auth else '未认证'}")

            config = None

            # 如果用户已登录，优先使用已认证的客户端
            if is_auth:
                try:
                    # 尝试获取已认证的客户端
                    auth_client = self.get_authenticated_client()

                    if auth_client:
                        log.info("使用已认证客户端获取配置（支持RLS）")
                        response = auth_client.table('robot_ps_smart_config').select('*').eq('user_name', ROBOT_BOT_TAG).execute()
                        if not response.data:
                            raise ValueError("未找到 {ROBOT_BOT_TAG} 配置")

                        if response and response.data and len(response.data) > 0:
                            config = response.data[0]  # 获取第一条配置
                            log.info("成功使用已认证客户端从云端获取配置")
                            return config
                        else:
                            log.warning("使用已认证客户端未获取到配置数据")
                    else:
                        log.warning("无法获取已认证的客户端，将尝试使用匿名客户端")
                except Exception as e:
                    log.error(f"使用已认证客户端获取配置失败: {str(e)}")

            # 如果已认证客户端获取失败或用户未登录，尝试使用匿名客户端
            try:
                log.info("尝试使用匿名客户端获取配置（受RLS限制）")
                response = self.client.table('robot_ps_smart_config').select('*').execute()

                if response and response.data and len(response.data) > 0:
                    config = response.data[0]  # 获取第一条配置
                    log.info("成功使用匿名客户端从云端获取配置")
                    return config
                else:
                    log.warning("使用匿名客户端未获取到配置数据")
            except Exception as e:
                log.error(f"使用匿名客户端获取配置失败: {str(e)}")

            # 如果两种方式都失败，返回None
            if not config:
                log.error("所有获取配置的尝试均失败")
                return None

            return config

        except Exception as e:
            log.error(f"从云端获取配置失败: {str(e)}")
            return None

    def get_canvas_settings(self):
        """获取画布设置"""
        config = self.fetch_config()

        # 默认值
        settings = {
            'max_height_cm': 5000,
            'ppi': 72,
            'image_spacing_cm': 0.1,
            'horizontal_expansion_cm': 0
        }

        if config:
            if 'img_ppi' in config and config['img_ppi'] is not None:
                settings['ppi'] = int(config['img_ppi'])

            if 'wide_add' in config and config['wide_add'] is not None:
                settings['horizontal_expansion_cm'] = float(config['wide_add'])

            if 'img_gap' in config and config['img_gap'] is not None:
                settings['image_spacing_cm'] = float(config['img_gap'])

            if 'max_height' in config and config['max_height'] is not None:
                settings['max_height_cm'] = float(config['max_height'])

        return settings

    def get_test_mode_settings(self):
        """获取测试模式设置

        Returns:
            测试模式设置字典，包含is_test_mode和is_test_all_data
            注意：已移除miniature_ratio，现在使用cm直接转px的方式实现测试模式
        """
        config = self.fetch_config()

        # 默认值
        settings = {
            'is_test_mode': False,
            'is_test_all_data': False
        }

        if config:
            # 获取测试模式开关
            if 'is_test_mode' in config and config['is_test_mode'] is not None:
                # 处理不同类型的布尔值表示
                if isinstance(config['is_test_mode'], bool):
                    settings['is_test_mode'] = config['is_test_mode']
                elif isinstance(config['is_test_mode'], (int, float)):
                    settings['is_test_mode'] = bool(config['is_test_mode'])
                elif isinstance(config['is_test_mode'], str):
                    settings['is_test_mode'] = config['is_test_mode'].lower() in ('true', 't', 'yes', 'y', '1')
                else:
                    settings['is_test_mode'] = bool(config['is_test_mode'])

                log.info(f"从云端获取测试模式设置: {settings['is_test_mode']}")

            # 获取是否测试全部数据
            if 'is_test_all_data' in config and config['is_test_all_data'] is not None:
                # 处理不同类型的布尔值表示
                if isinstance(config['is_test_all_data'], bool):
                    settings['is_test_all_data'] = config['is_test_all_data']
                elif isinstance(config['is_test_all_data'], (int, float)):
                    settings['is_test_all_data'] = bool(config['is_test_all_data'])
                elif isinstance(config['is_test_all_data'], str):
                    settings['is_test_all_data'] = config['is_test_all_data'].lower() in ('true', 't', 'yes', 'y', '1')
                else:
                    settings['is_test_all_data'] = bool(config['is_test_all_data'])

                log.info(f"从云端获取测试全部数据设置: {settings['is_test_all_data']}")

        return settings