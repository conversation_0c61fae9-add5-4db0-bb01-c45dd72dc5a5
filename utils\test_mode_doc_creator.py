#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试模式文档创建器模块

提供异步创建测试模式说明文档的功能，避免阻塞主线程
"""

import os
import sys
import logging
import time
import math
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple, Union

from PyQt6.QtCore import QThread, pyqtSignal

# 导入格式化函数
from utils.log_file_creator import format_cm_value

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
log = logging.getLogger("TestModeDocCreator")

class TestModeDocCreator(QThread):
    """异步创建测试模式说明文档的工作线程

    该线程专门用于异步创建测试模式说明文档，不会阻塞主线程
    """

    # 定义信号
    log_signal = pyqtSignal(str)  # 日志信号
    finished_signal = pyqtSignal(bool, str)  # 完成信号，参数为成功标志和消息

    def __init__(self, jpg_path: str, test_data: Dict[str, Any]):
        """初始化测试模式文档创建器

        Args:
            jpg_path: JPG文件路径
            test_data: 测试数据字典，包含创建说明文档所需的所有信息
        """
        super().__init__()
        self.jpg_path = jpg_path
        self.test_data = test_data

    def run(self):
        """执行文档创建任务"""
        try:
            # 检查JPG路径是否有效
            if not self.jpg_path or not isinstance(self.jpg_path, str):
                error_msg = f"无效的JPG文件路径: {self.jpg_path}"
                self.log_signal.emit(error_msg)
                self.finished_signal.emit(False, error_msg)
                return

            # 创建同名的TXT文件路径
            doc_path = os.path.splitext(self.jpg_path)[0] + "_说明.txt"
            self.log_signal.emit(f"开始创建测试模式说明文档: {doc_path}")

            # 确保输出目录存在
            output_dir = os.path.dirname(doc_path)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir)
                self.log_signal.emit(f"创建输出目录: {output_dir}")

            # 提取测试数据
            canvas_name = self.test_data.get('canvas_name', '未知画布')
            material_name = self.test_data.get('material_name', '未知材质')
            canvas_sequence = self.test_data.get('canvas_sequence', 0)
            canvas_width_m = self.test_data.get('canvas_width_m', 0)
            horizontal_expansion_cm = self.test_data.get('horizontal_expansion_cm', 0)
            max_height_cm = self.test_data.get('max_height_cm', 0)
            miniature_ratio = self.test_data.get('miniature_ratio', 0.02)
            is_test_all_data = self.test_data.get('is_test_all_data', False)
            ppi = self.test_data.get('ppi', 72)
            images_info = self.test_data.get('images_info', [])
            utilization = self.test_data.get('utilization', 0)
            canvas_height_px = self.test_data.get('canvas_height_px', 0)

            # 计算画布高度（厘米）
            canvas_height_cm = canvas_height_px * 2.54 / ppi if canvas_height_px > 0 and ppi > 0 else 0

            # 计算画布宽度（像素）- 包含水平拓展
            canvas_width_px = int(canvas_width_m * 100 * ppi / 2.54) + int(horizontal_expansion_cm * ppi / 2.54)

            # 计算图片统计信息
            total_images = len(images_info)
            a_class_images = sum(1 for img in images_info if img.get('image_class') == 'A')
            b_class_images = sum(1 for img in images_info if img.get('image_class') == 'B')
            c_class_images = sum(1 for img in images_info if img.get('image_class') == 'C')
            rotated_images = sum(1 for img in images_info if img.get('need_rotation', False))

            # 写入文件
            with open(doc_path, 'w', encoding='utf-8') as f:
                # 标题和基本信息
                f.write(f"# {material_name}-{canvas_sequence} 测试模式说明文档\n")
                f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"材质名称: {material_name}\n")
                f.write(f"画布序号: {canvas_sequence}\n")
                f.write(f"画布宽度: {canvas_width_m:.2f}米 ({int(canvas_width_m*100)}厘米)\n")
                f.write(f"画布高度: {canvas_height_cm:.2f} 厘米 ({canvas_height_px} 像素)\n")
                f.write(f"水平拓展: {horizontal_expansion_cm} 厘米\n")
                f.write(f"最大高度限制: {max_height_cm} 厘米\n")
                f.write(f"缩小模型比率: {miniature_ratio}\n")
                f.write(f"测试全部数据: {'是' if is_test_all_data else '否'}\n")

                # 添加利用率统计信息
                f.write("## 利用率统计\n")
                f.write(f"画布利用率: {utilization*100:.2f}%\n")
                f.write(f"旋转图片比例: {rotated_images/total_images*100:.2f}% ({rotated_images}/{total_images})\n")

                # 添加图片统计
                f.write("## 图片统计\n")
                f.write(f"总图片数: {total_images}\n")
                f.write(f"A类图片(宽幅类): {a_class_images} ({a_class_images/total_images*100:.2f}%)\n")
                f.write(f"B类图片(宽幅约束类): {b_class_images} ({b_class_images/total_images*100:.2f}%)\n")
                f.write(f"C类图片(俄罗斯方块类): {c_class_images} ({c_class_images/total_images*100:.2f}%)\n")

                # 添加图片排列信息
                f.write("## 图片排列信息\n")
                f.write(f"{'序号':<5}{'名称':<30}{'分类':<5}{'位置(x,y)':<15}{'尺寸(宽x高)':<15}{'表格宽-高':<12}{'旋转':<5}\n\n")

                # 按Y坐标排序，使得输出更有序
                sorted_images = sorted(images_info, key=lambda img: (img.get('y', 0), img.get('x', 0)))

                for i, img in enumerate(sorted_images):
                    name = img.get('name', '未命名')
                    image_class = img.get('image_class', 'C')
                    x = img.get('x', 0)
                    y = img.get('y', 0)
                    width = img.get('width', 0)
                    height = img.get('height', 0)
                    need_rotation = img.get('need_rotation', False)
                    rotated = '是' if need_rotation else '否'

                    # 获取表格里的宽、高数据
                    table_width_cm = img.get('table_width_cm', img.get('width_cm', 0))
                    table_height_cm = img.get('table_height_cm', img.get('height_cm', 0))
                    # 添加表格宽-高列，格式为"宽-高"，使用格式化函数处理小数点
                    table_cm_size = f"{format_cm_value(table_width_cm)}-{format_cm_value(table_height_cm)}"

                    f.write(f"{i+1:<5}{name[:28]:<30}{image_class:<5}({x},{y}){'':>5}({width}x{height}){'':>3}{table_cm_size:<12}{rotated:<5}\n")
                f.write("\n")


                # 添加色块说明
                f.write("## 色块说明\n\n")
                f.write("- 红色: A类图片（宽幅类）\n")
                f.write("- 绿色: B类图片（宽幅约束类）\n")
                f.write("- 蓝色: C类图片（其他图片）\n\n")

                # 添加测试模式说明
                f.write("## 测试模式说明\n\n")
                f.write("测试模式下，使用色块替代实际图片，不启动Photoshop，提高测试效率。\n")
                f.write("色块图片保存为JPG格式，缩小模型比率用于减小生成图片的尺寸。\n")
                if is_test_all_data:
                    f.write("测试全部数据模式已开启，包括处理'未入库'的图片。\n")

            self.log_signal.emit(f"成功创建测试模式说明文档: {doc_path}")
            self.finished_signal.emit(True, f"成功创建测试模式说明文档: {doc_path}")

        except Exception as e:
            error_msg = f"创建测试模式说明文档失败: {str(e)}"
            self.log_signal.emit(error_msg)
            self.finished_signal.emit(False, error_msg)


def create_test_mode_doc_async(jpg_path: str, test_data: Dict[str, Any], log_callback=None) -> Optional[TestModeDocCreator]:
    """为JPG文件异步创建测试模式说明文档

    该函数创建一个新的线程来异步创建测试模式说明文档，不会阻塞调用线程

    Args:
        jpg_path: JPG文件路径
        test_data: 测试数据字典，包含创建说明文档所需的所有信息
        log_callback: 日志回调函数，用于接收日志信息

    Returns:
        TestModeDocCreator实例，如果创建失败则返回None
    """
    try:
        # 检查参数
        if not jpg_path or not isinstance(jpg_path, str):
            if log_callback:
                log_callback(f"无效的JPG文件路径: {jpg_path}")
            return None

        if not test_data or not isinstance(test_data, dict):
            if log_callback:
                log_callback(f"无效的测试数据: {type(test_data)}")
            # 创建一个基本的测试数据字典
            test_data = {
                'canvas_name': os.path.basename(os.path.splitext(jpg_path)[0]),
                'material_name': '',
                'canvas_sequence': 0,
                'canvas_width_m': 0,
                'horizontal_expansion_cm': 0,
                'max_height_cm': 0,
                'miniature_ratio': 0.02,
                'is_test_all_data': False,
                'ppi': 72,
                'images_info': [],
                'utilization': 0,
                'canvas_height_px': 0
            }
            if log_callback:
                log_callback("已创建基本测试数据字典")

        # 创建并启动工作线程
        doc_creator = TestModeDocCreator(jpg_path, test_data)

        # 如果提供了日志回调函数，连接信号
        if log_callback:
            doc_creator.log_signal.connect(log_callback)
            # 使用lambda函数连接信号，只传递消息
            doc_creator.finished_signal.connect(lambda _, msg: log_callback(msg))

        # 启动线程
        doc_creator.start()

        # 返回文档创建器实例，以便调用者可以连接其信号
        return doc_creator

    except Exception as e:
        if log_callback:
            log_callback(f"创建测试模式说明文档失败: {str(e)}")

        # 尝试直接创建一个基本的TXT文件，确保至少有一些信息
        try:
            doc_path = os.path.splitext(jpg_path)[0] + "_说明.txt"
            material_name = os.path.basename(os.path.dirname(jpg_path))
            canvas_sequence = 1  # 默认序号

            # 尝试从文件名提取序号
            try:
                file_name = os.path.basename(jpg_path)
                if '-' in file_name:
                    parts = file_name.split('-')
                    if len(parts) > 1 and parts[-1].split('.')[0].isdigit():
                        canvas_sequence = int(parts[-1].split('.')[0])
            except:
                pass

            with open(doc_path, 'w', encoding='utf-8') as f:
                f.write(f"# {material_name}-{canvas_sequence} 测试模式说明文档\n")
                f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"材质名称: {material_name}\n")
                f.write(f"画布序号: {canvas_sequence}\n")
                f.write(f"JPG文件: {os.path.basename(jpg_path)}\n")
                f.write(f"\n注意: 此为应急创建的简化说明文档，因原始文档创建过程出错。\n")
                f.write(f"错误信息: {str(e)}\n")
            if log_callback:
                log_callback(f"已创建应急简化说明文档: {doc_path}")
        except Exception as e2:
            if log_callback:
                log_callback(f"创建应急说明文档也失败: {str(e2)}")
        return None
