#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
验证RectPack算法优化的完整性
检查所有6项需求是否完全实现
"""

import os
import sys

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def verify_unit_converter():
    """验证全局统一cm转px方法"""
    print("🔍 验证1: 全局统一cm转px方法")
    try:
        from utils.unit_converter import cm_to_px, px_to_cm, get_global_ppi
        
        # 测试基本转换
        ppi = get_global_ppi()
        print(f"  ✅ 全局PPI: {ppi}")
        
        # 测试示例转换
        test_cases = [
            (120, "图片宽度"),
            (60, "图片高度"), 
            (200, "容器宽度"),
            (2, "水平拓展"),
            (5000, "最大高度"),
            (0.1, "图片间距")
        ]
        
        for cm_val, desc in test_cases:
            px_val = cm_to_px(cm_val)
            cm_back = px_to_cm(px_val)
            print(f"  ✅ {desc}: {cm_val}cm → {px_val}px → {cm_back:.3f}cm")
        
        print("  ✅ 全局统一cm转px方法验证通过\n")
        return True
        
    except Exception as e:
        print(f"  ❌ 验证失败: {str(e)}\n")
        return False

def verify_miniature_ratio_removal():
    """验证miniature_ratio逻辑已完全移除"""
    print("🔍 验证2: miniature_ratio逻辑已完全移除")
    try:
        # 检查配置管理器
        from utils.config_manager_duckdb import ConfigManagerDuckDB
        config_manager = ConfigManagerDuckDB()
        
        test_settings = config_manager.get_test_mode_settings()
        if 'miniature_ratio' in test_settings:
            print("  ❌ 配置管理器中仍存在miniature_ratio")
            return False
        else:
            print("  ✅ 配置管理器已移除miniature_ratio")
        
        # 检查测试模式函数
        from core.rectpack_test_mode import convert_pattern_items_to_px_data
        import inspect
        
        sig = inspect.signature(convert_pattern_items_to_px_data)
        if 'miniature_ratio' in sig.parameters:
            print("  ❌ 测试模式函数仍包含miniature_ratio参数")
            return False
        else:
            print("  ✅ 测试模式函数已移除miniature_ratio参数")
        
        print("  ✅ miniature_ratio逻辑完全移除验证通过\n")
        return True
        
    except Exception as e:
        print(f"  ❌ 验证失败: {str(e)}\n")
        return False

def verify_test_mode_config():
    """验证测试模式从配置读取最大高度"""
    print("🔍 验证3: 测试模式遵循容器最大高度限制")
    try:
        from core.rectpack_test_mode import get_container_config_px
        
        # 测试容器配置
        config = get_container_config_px(
            canvas_width_cm=200,
            horizontal_expansion_cm=2,
            max_height_cm=5000,
            image_spacing_cm=0.1
        )
        
        expected_keys = ['base_width', 'horizontal_expansion', 'actual_width', 'max_height', 'spacing']
        for key in expected_keys:
            if key not in config:
                print(f"  ❌ 容器配置缺少{key}")
                return False
        
        # 验证统一单位处理
        if config['base_width'] != 200:
            print(f"  ❌ 基础宽度错误: 期望200，实际{config['base_width']}")
            return False
            
        if config['actual_width'] != 202:  # 200 + 2
            print(f"  ❌ 实际宽度错误: 期望202，实际{config['actual_width']}")
            return False
            
        if config['max_height'] != 5000:
            print(f"  ❌ 最大高度错误: 期望5000，实际{config['max_height']}")
            return False
        
        print(f"  ✅ 容器配置正确: {config['actual_width']}x{config['max_height']}px")
        print("  ✅ 测试模式容器最大高度限制验证通过\n")
        return True
        
    except Exception as e:
        print(f"  ❌ 验证失败: {str(e)}\n")
        return False

def verify_pattern_conversion():
    """验证图案转换统一单位处理"""
    print("🔍 验证4: 图案转换统一单位处理")
    try:
        from core.rectpack_test_mode import convert_pattern_items_to_px_data
        
        # 测试数据
        pattern_items = [
            {
                'pattern_name': '测试图案1',
                'width_cm': 120,
                'height_cm': 60,
                'quantity': 1
            },
            {
                'pattern_name': '测试图案2', 
                'width_cm': 80,
                'height_cm': 40,
                'quantity': 2
            }
        ]
        
        # 转换（不再使用miniature_ratio）
        px_data = convert_pattern_items_to_px_data(pattern_items)
        
        # 验证结果
        if len(px_data) != 3:  # 1 + 2 = 3张图片
            print(f"  ❌ 转换数量错误: 期望3，实际{len(px_data)}")
            return False
        
        # 验证第一张图片
        width_px, height_px, label = px_data[0]
        if width_px != 120 or height_px != 60:
            print(f"  ❌ 第一张图片尺寸错误: 期望120x60，实际{width_px}x{height_px}")
            return False
        
        print(f"  ✅ 图案转换正确: 120x60cm → {width_px}x{height_px}px")
        print("  ✅ 图案转换统一单位处理验证通过\n")
        return True
        
    except Exception as e:
        print(f"  ❌ 验证失败: {str(e)}\n")
        return False

def verify_photoshop_integration():
    """验证Photoshop集成使用统一转换器"""
    print("🔍 验证5: Photoshop集成使用统一转换器")
    try:
        from utils.photoshop_helper import PhotoshopHelper
        import inspect
        
        # 检查place_image方法是否导入了unit_converter
        source = inspect.getsource(PhotoshopHelper.place_image)
        if 'unit_converter' in source:
            print("  ✅ Photoshop助手已集成统一单位转换器")
        else:
            print("  ❌ Photoshop助手未使用统一单位转换器")
            return False
        
        print("  ✅ Photoshop集成统一转换器验证通过\n")
        return True
        
    except Exception as e:
        print(f"  ❌ 验证失败: {str(e)}\n")
        return False

def verify_config_integration():
    """验证配置集成"""
    print("🔍 验证6: 配置集成和初始化")
    try:
        from utils.unit_converter import initialize_from_config, get_default_converter
        
        # 模拟配置管理器
        class MockConfigManager:
            def get_canvas_settings(self):
                return {'ppi': 300}
        
        mock_config = MockConfigManager()
        success = initialize_from_config(mock_config)
        
        if success:
            converter = get_default_converter()
            if converter.get_ppi() == 300:
                print("  ✅ 配置初始化成功，PPI已更新为300")
            else:
                print(f"  ❌ PPI更新失败: 期望300，实际{converter.get_ppi()}")
                return False
        else:
            print("  ❌ 配置初始化失败")
            return False
        
        print("  ✅ 配置集成验证通过\n")
        return True
        
    except Exception as e:
        print(f"  ❌ 验证失败: {str(e)}\n")
        return False

def main():
    """主验证函数"""
    print("=" * 60)
    print("RectPack算法优化完整性验证")
    print("=" * 60)
    
    verifications = [
        verify_unit_converter,
        verify_miniature_ratio_removal,
        verify_test_mode_config,
        verify_pattern_conversion,
        verify_photoshop_integration,
        verify_config_integration
    ]
    
    passed = 0
    total = len(verifications)
    
    for verification in verifications:
        if verification():
            passed += 1
    
    print("=" * 60)
    print(f"验证结果: {passed}/{total} 项通过")
    
    if passed == total:
        print("🎉 所有验证通过！RectPack算法优化完全实现！")
        print("✅ 测试模式和生产模式更统一")
        print("✅ 测试模式更能体现生产环境真实情况")
        print("✅ 全局统一cm转px方法")
        print("✅ 完全移除miniature_ratio逻辑")
        print("✅ 项目更规范、更标准、更符合工程要求")
    else:
        print(f"❌ 还有 {total - passed} 项需要修复")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
